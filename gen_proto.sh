#node node_modules/grpc-tools/bin/protoc.js --js_out=import_style=commonjs,binary:./proto/out --proto_path=./proto/ ./proto/payload_base.proto
#node node_modules/grpc-tools/bin/protoc.js --js_out=import_style=commonjs,binary:./proto/out --proto_path=./proto/ ./proto/webcast_message.proto
#node node_modules/grpc-tools/bin/protoc.js --js_out=import_style=commonjs,binary:./proto/out --proto_path=./proto/ ./proto/webcast/webcast_im.proto
#node node_modules/grpc-tools/bin/protoc.js --js_out=import_style=commonjs,binary:./proto/out --proto_path=./proto/ ./proto/webcast/webcast_data.proto

# 有类型检查 npm install --save-dev protobufjs
#npx pbjs -t static-module -w commonjs -p ./proto/ -o ./proto/out/payload_base.js ./proto/payload_base.proto
#npx pbjs -t static-module -w commonjs -p ./proto/ -o ./proto/out/message.js ./proto/message.proto
#npx pbjs -t static-module -w commonjs -p ./proto/ -o ./proto/out/webcast/im.js ./proto/webcast/im.proto
#npx pbjs -t static-module -w commonjs -p ./proto/ -o ./proto/out/webcast/data.js ./proto/webcast/data.proto

#npx pbjs -t static-module -w commonjs -p ./proto/ -o ./proto/webcast.js ./proto/*.proto

npx pbjs -t static-module -w commonjs -o ./modules/src/auto_gen_priv_webcast.js ./proto/priv/*.proto
npx pbts ./modules/src/auto_gen_priv_webcast.js -o ./modules/src/auto_gen_priv_webcast.d.ts

# https://www.cnblogs.com/abc126655/p/18247499
npx pbjs -t static-module -w commonjs -o ./modules/src/auto_gen_webcast.js ./proto/*.proto
npx pbts ./modules/src/auto_gen_webcast.js -o ./modules/src/auto_gen_webcast.d.ts

#npx pbjs -t static-module -w es6 -o ./modules/src/auto_gen_webcast_es6.js ./proto/*.proto
#npx pbts ./modules/src/auto_gen_webcast_es6.js -o ./modules/src/auto_gen_webcast_es6.d.ts