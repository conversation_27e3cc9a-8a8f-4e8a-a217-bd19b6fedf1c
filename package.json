{"name": "electron-quick-start", "version": "1.0.0", "description": "A minimal Electron application", "scripts": {"start:main": "electron ./modules/record/demo/main-demo.js", "start:websocket": "electron ./modules/record/demo/websocket-demo.js", "client:websocket": "node ./modules/record/demo/websocket-client.js"}, "repository": "https://github.com/electron/electron-quick-start", "keywords": ["Electron", "quick", "start", "tutorial", "demo"], "author": "GitHub", "license": "CC0-1.0", "devDependencies": {"electron": "^33.2.1"}, "dependencies": {"@rollup/plugin-json": "^6.1.0", "@rollup/plugin-terser": "^0.4.4", "@yy/secure-loader": "^0.1.17", "google-protobuf": "^3.21.4", "grpc-tools": "^1.12.4", "protobufjs": "^7.4.0", "ws": "^8.18.3"}}