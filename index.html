<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Input LiveId</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      text-align: center;
      padding: 20px;
    }

    input {
      padding: 8px;
      margin-right: 10px;
    }

    button {
      padding: 8px 20px;
      background-color: #007bff;
      color: white;
      border: none;
      cursor: pointer;
    }
  </style>
</head>

<body>
<h1> 1</h1>
<input type="text" id="LiveId" placeholder="404703311859">
<button id="openBtn">Enter LiveId</button>

<script>
  document.getElementById('openBtn').addEventListener('click', function() {
    var url = document.getElementById('LiveId').value || document.getElementById('LiveId').placeholder;
    if (url.trim() !== '') {
      window.open("https://live.douyin.com/"+url, '_self');
    } else {
      alert('Please enter a valid LiveId');
    }
  });
</script>
</body>
</html>