#### 安装
npm install protobufjs protobufjs-cli
npm install @rollup/plugin-json
npm install @rollup/plugin-terser
npm install rollup


#### 加固打包
cd modules
./build-release.sh
输出 ./dist/
安全js加密
npm install @yy/secure-loader --registry=https://npm-registry.yy.com

https://npm.yy.com/package/@yy/secure-loader

Tips
当配置了字节码的targetCefVersion为all时，请在所有执行字节码的代码前，添加以下的预处理代码（可以添加在html入口文件）：

<!-- index.html -->
<!DOCTYPE html>
<html>
  <head></head>
  <body>
    <!-- 预处理代码 -->
    <script>
      {
        const result = navigator.userAgent.match(/Chrome\/(\d+)/)
        if (result[1]) {
          if (navigator.userAgent.indexOf('WOW64') > -1) {
            window.__bcodeTarget__ = result[1]
          } else {
            window.__bcodeTarget__ = result[1] + '-x64'
          }
        } else {
          window.__bcodeTarget__ = '109'
        }
      }
    </script>

    <!-- 应用入口js -->
    <script src="./app.js"></script>
  </body>
</html>