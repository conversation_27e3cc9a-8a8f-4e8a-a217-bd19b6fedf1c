
            // else if (msg.method === 'WebcastRoomMessage') {
            //     _decodeMessage = pb_webcast.webcast.RoomMessage.decode(msg.payload);
            //     if (_decodeMessage.systemTopMsg) {
            //         log(_decodeMessage.common,  _decodeMessage.content);
            //     } else {
            //         // log(_decodeMessage.common,  JSON.stringify(_decodeMessage));
            //         let formatMsg = parseFormatText(_decodeMessage.common.displayText);
            //         log(_decodeMessage.common,  formatMsg);
            //         // 比如 common_text_game_score  {0:user} 为主播加了 {1:string}分
            //
            //     }
            // } else if (msg.method === 'WebcastRoomUserSeqMessage') {
            //     _decodeMessage = pb_webcast.webcast.RoomUserSeqMessage.decode(msg.payload);
            //     var showMsg = 'ranks:';
            //     _decodeMessage.ranks.forEach((_rank)=>{
            //         showMsg += '[' + _rank.rank + ']' + _rank.user.nickname + ','
            //     });
            //     showMsg += '在线观众:' + _decodeMessage.total +',TotalUser:' + _decodeMessage.totalUser;
            //     log(_decodeMessage.common, showMsg);
            // } else if (msg.method === 'WebcastRoomRankMessage') {
            //     _decodeMessage = pb_webcast.webcast.RoomRankMessage.decode(msg.payload);
            //     var _showMsg = '';
            //     _decodeMessage.ranks.forEach((_rank)=>{
            //         _showMsg += '[' + _rank.scoreStr + ']' + _rank.user.nickname
            //     });
            //     log(_decodeMessage.common, _showMsg);
            // }
            // //
            // else if (msg.method === 'WebcastRoomStatsMessage') {
            //     _decodeMessage = pb_webcast.webcast.RoomStatsMessage.decode(msg.payload);
            //     log(_decodeMessage.common, _decodeMessage.displayLong)
            // } else if (msg.method === 'WebcastUpdateFanTicketMessage') {
            //     _decodeMessage = pb_webcast.webcast.UpdateFanTicketMessage.decode(msg.payload);
            //     log(_decodeMessage.common, _decodeMessage.roomFanTicketCount);
            // } else if (msg.method === 'WebcastAudioChatMessage') {
            //     _decodeMessage = pb_webcast.webcast.AudioChatMessage.decode(msg.payload);
            //     log(_decodeMessage.common,  _decodeMessage.user.nickname+ ':' + _decodeMessage.content);
            // } else if (msg.method === 'WebcastInRoomBannerMessage') {
            //     // 礼物展馆
            //     _decodeMessage = pb_webcast.webcast.InRoomBannerMessage.decode(msg.payload);
            //     log(_decodeMessage.common, 'opType=' + _decodeMessage.opType);
            //     // _decodeMessage.opType == 1 启动时,开启心愿
            //     // _decodeMessage.opType == 2 增加 Add
            //     // _decodeMessage.opType == 3
            //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            //     // extra 是json, 里面有礼物心愿等..
            // } else if (msg.method === 'WebcastEmojiChatMessage') {
            //     _decodeMessage = pb_webcast.webcast.EmojiChatMessage.decode(msg.payload);
            //     let formatMsg = parseFormatText(_decodeMessage.emojiContent);
            //     log(_decodeMessage.common, _decodeMessage.user.nickname + ':' + formatMsg);
            // } else if (msg.method === 'WebcastGiftSortMessage') {
            //     /**
            //      * [2025-1-17 10:45:17:938][7460708521391196982][WebcastGiftSortMessage]{"common":{"method":"WebcastGiftSortMessage","msgId":"7460585858279871488","roomId":"7460708521391196982","isShowMsg":true},"messageType":3,"sceneInsertStrategy":{"scene":"gift_vote","strategyType":1}}
            //      * WebcastGiftVoteMessage
            //      * */
            //     _decodeMessage = pb_webcast.webcast.GiftSortMessage.decode(msg.payload);
            //     // [2025-1-15 17:47:42][WebcastGiftSortMessage]{"common":{"method":"WebcastGiftSortMessage","msgId":"7460076328411272242","roomId":"7460057185150618368","isShowMsg":true},"messageType":3,"sceneInsertStrategy":{"scene":"broadcast_precision","giftIds":["463"],"startTime":"1736934457","endTime":"1736934577","strategyType":1}}
            //     log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // } else if (msg.method === 'WebcastGiftVoteMessage') {
            //     // 礼物投票
            //     _decodeMessage = pb_webcast.webcast.GiftVoteMessage.decode(msg.payload);
            //     log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // } else if (msg.method === 'WebcastScreenChatMessage') {
            //     _decodeMessage = pb_webcast.webcast.ScreenChatMessage.decode(msg.payload);
            //     log(_decodeMessage.common, _decodeMessage.content);
            // } else if (msg.method === 'WebcastRoomIntroMessage') {
            //     _decodeMessage = pb_webcast.webcast.RoomIntroMessage.decode(msg.payload);
            //     log(_decodeMessage.common, _decodeMessage.intro);
            // } else if (msg.method === 'WebcastHotRoomMessage') {
            //     _decodeMessage = pb_webcast.webcast.HotRoomMessage.decode(msg.payload);
            //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            //     log(_decodeMessage.common, _decodeMessage.info.BitMap);
            // } else if (msg.method === 'WebcastHotChatMessage') {
            //     // [2025-1-16 14:32:19][7460380212938099465][WebcastHotChatMessage]{"common":{"method":"WebcastHotChatMessage","msgId":"7460397356812110646","roomId":"7460380212938099465","isShowMsg":true},"title":"大家说","content":"[捂脸][捂脸][捂脸]","num":["1","4","7","10","12","16","22","28"],"duration":"3","showDuration":["200","200","200","200","200","200","200","200"],"sequenceId":"1737009120","extra":{"tag_content_type":"comment"}}
            //     _decodeMessage = pb_webcast.webcast.HotChatMessage.decode(msg.payload);
            //     var _show_msg = _decodeMessage.title + ':' + _decodeMessage.content;
            //     _decodeMessage.num.forEach((_num) => {
            //         _show_msg += 'x' + _num + ',';
            //     })
            //     log(_decodeMessage.common, _show_msg);
            //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // } else if (msg.method === 'WebcastRanklistHourEntranceMessage') {
            //     _decodeMessage = pb_webcast.webcast.RanklistHourEntranceMessage.decode(msg.payload);
            //     var show_msg = '';
            //     // _decodeMessage.info.globalInfos.forEach((globalInfo) => {
            //     //   globalInfo.details.forEach( (_detail) => {
            //     //     _detail.pages.forEach( (page) => {
            //     //       if (page.contentType !== 0) {
            //     //         show_msg += '['+_detail.title+']=>' + page.content;
            //     //       }
            //     //     })
            //     //   })
            //     // });
            //     _decodeMessage.info.defaultGlobalInfos.forEach((globalInfo) => {
            //         globalInfo.details.forEach( (_detail) => {
            //             _detail.pages.forEach( (page) => {
            //                 if (page.contentType !== 0) {
            //                     show_msg += '['+_detail.title+']=>' + page.content;
            //                 }
            //             })
            //         })
            //     });
            //     if (show_msg === '') {
            //         // TODO:
            //         // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            //     } else {
            //         log(_decodeMessage.common, show_msg);
            //     }
            // }
            // else if (msg.method === 'WebcastInteractEffectMessage') {
            //     // [2025-1-15 18:33:35][7460065498965854986][WebcastInteractEffectMessage]{"common":{"method":"WebcastInteractEffectMessage","msgId":"7460088502999716874","roomId":"7460065498965854986","isShowMsg":true},"effectId":"92894524","extra":"null","teaLog":"null","messageType":"40","arg1":"101","arg2":"1000000","arg8":"{\"activity\":\"24v_6ac_fans\",\"data\":{\"anchor_rank\":0,\"backup\":false,\"block\":false,\"down_rank\":2,\"enhancer_card_end_time\":0,\"enhancer_card_rate\":0,\"msg_type\":0,\"promotion_rank\":0,\"rank_type\":1,\"selector_name\":\"五十万粉团实力赛道\",\"status_lock_time\":600,\"status_show_time\":10,\"up_rank\":2}}"}
            //     _decodeMessage = pb_webcast.webcast.InteractEffectMessage.decode(msg.payload);
            //     log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // }
            // else if (msg.method === 'WebcastNotifyEffectMessage') {
            //     _decodeMessage = pb_webcast.webcast.NotifyEffectMessage.decode(msg.payload);
            //     log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // }
            //
            // // else if (msg.method === 'WebcastActivityEmojiGroupsMessage') {
            // //     // [2025-1-17 14:32:25:886][7460736793282562857][WebcastActivityEmojiGroupsMessage]{"common":{"method":"WebcastActivityEmojiGroupsMessage","msgId":"7460737862586993458","roomId":"7460736793282562857","isShowMsg":true},"activityEmojiGroups":{"emojiGroup":{"id":"44","idStr":"44","name":"庆祝表情","tagIcon":{"urlList":["https://p3-webcast.douyinpic.com/img//webcast/71fb07cc7aab742838d815bc0fa4fe66~tplv-obj.image","https://p11-webcast.douyinpic.com/img//webca1fb07cc7aab742838d815bc0fa4fe66~tplv-obj.image"],"uri":"/webcast/71fb07cc7aab742838d815bc0fa4fe66","height":"14","width":"34","avgColor":"#D8EBCE"},"desc":"派对庆祝表情，限时2小时有效","emojiList":[{"id":"77","idStr":"77","name"i":{"urlList":["https://p3-webcast.douyinpic.com/img//webcast/7c8deebd296be4594055d12f3d5ea173~tplv-obj.image","https://p11-webcast.douyinpic.com/img//webcast/7c8deebd296be4594055d12f3d5ea173~tplv-obj.image"],"uri":"/webcast/7c8deebd296be4594055d12f3d5ea173","height":"27","width":"81","avgColor":"#E0D4BC"}},{"id":"78","idStr":"78","name":"升级表情","emoji":{"urlList":["https://p3-webcast.douyinpic.com/img//webcast/7faadf3e2e5a4399bcc32a50145b9f0c~tplv-image","https://p11-webcast.douyinpic.com/img//webcast/7faadf3e2e5a4399bcc32a50145b9f0c~tplv-obj.image"],"uri":"/webcast/7faadf3e2e5a4399bcc32a50145b9f0c","height":"27","width":"81","avgColor":"#A3C4CC"}},{"id":"79","idStr":"79","name":"升级表情","emoji":{"urlList":["https://p3-webcast.douyinpic.com/img//webcast/b9c37f22e95262e51af2541db4a2c0ca~tplv-obj.image","https://p11-webcast.douyinpic.com/img//webcast/b9c37f22e95262e51af2541db4a2c0ca~tplv-obj.ima,"uri":"/webcast/b9c37f22e95262e51af2541db4a2c0ca","height":"27","width":"81","avgColor":"#7A6D53"}},{"id":"81","idStr":"81","name":"升级表情","emoji":{"urlList":["https://p11-webcast.douyinpic.com/img//webcast/58c57256d2ff1dd3a901bb2d0b2c~tplv-obj.image","https://p3-webcast.douyinpic.com/img//webcast/58c57256d2ff1dd3acc70901bb2d0b2c~tplv-obj.image"],"uri":"/webcast/58c57256d2ff1dd3acc70901bb2d0b2c","height":"27","width":"81","avgColor":"#B1CCA3"}}],"insertEmojiNum":"4"},"startTime":"**********","endTime":"**********"}}
            // //     _decodeMessage = pb_webcast.webcast.ActivityEmojiGroupsMessage.decode(msg.payload);
            // //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // //     var emojiList = '';
            // //     if (_decodeMessage.activityEmojiGroups.emojiGroup !== null) {
            // //         _decodeMessage.activityEmojiGroups.emojiGroup.emojiList.forEach((emoji) => {
            // //             emojiList += '[(' + emoji.id + ')' + emoji.name + ']';
            // //         })
            // //         log(_decodeMessage.common, 'id:' + _decodeMessage.activityEmojiGroups.emojiGroup.id + ','
            // //             + _decodeMessage.activityEmojiGroups.emojiGroup.name + ',desc:'
            // //             + _decodeMessage.activityEmojiGroups.emojiGroup.desc + ',' + emojiList);
            // //     }
            // // }
            // else if (msg.method === 'WebcastLightGiftMessage') {
            //     // [2025-1-17 14:30:18:313][7460736793282562857][WebcastLightGiftMessage]{"common":{"method":"WebcastLightGiftMessage","msgId":"7460764752036090907","roomId":"7460736793282562857","createTime":"**********","isShowMsg":true},"groupCount":"1","repeatCount":"1","toUserId":"62657049039","giftInfo":{"giftId":"2002","giftIcon":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/71801c53df3977b1470ac2afb8250ac1.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/71801c53df3977b1470ac2afb8250ac1.png~tplv-obj.image"],"uri":"webcast/71801c53df3977b1470ac2afb8250ac1.png","avgColor":"#EBEBFF"}},"trayInfo":{"durationMs":"2000"},"count":"1","bannedDisplayEffects":"2","giftStruct":{"id":"2002","type":1,"diamondCount":2,"assetIds":["0"]}}
            //     _decodeMessage = pb_webcast.webcast.LightGiftMessage.decode(msg.payload);
            //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // }
            // else if (msg.method === 'WebcastTopEffectMessage') {
            //     // 置顶消息
            //     // [2025-1-17 14:41:3:986][7460736793282562857][WebcastTopEffectMessage]{"common":{"method":"WebcastTopEffectMessage","msgId":"7460493249762464818","roomId":"7460736793282562857","isShowMsg":true},"assetId":"5887","priority":"200","scene":"25v_1_new","endTime":"**********","images":[{"placeholderKey":"avatar_middle","mixImage":{"urlList":["https://p11-webcast.douyinpic.com/img/webcast/mystery_man_medium_avatar.png~tplv-obj.image"],"uri":"webcast/mystery_man_medium_avatar.png"}}],"texts":[{"placeholderKey":"text_content","content":"准备迎接2025"},{"placeholderKey":"text_name","content":"神秘人433"}]}
            //     _decodeMessage = pb_webcast.webcast.TopEffectMessage.decode(msg.payload);
            //     var _text_name = '';
            //     var _text_content = '';
            //     _decodeMessage.texts.forEach((text) => {
            //         // 解析 IEffectTextInfo
            //         if (text.placeholderKey === 'text_name') {
            //             _text_name = text.content;
            //         } else if (text.placeholderKey === 'text_content') {
            //             _text_content = text.content;
            //         }
            //     })
            //     log(_decodeMessage.common, _text_name + ':' + _text_content);
            //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // }
            // else if (msg.method === 'WebcastAtmosphereEffectMessage') {
            //     // [2025-1-17 14:49:26:827][7460736793282562857][WebcastAtmosphereEffectMessage]{"common":{"method":"WebcastAtmosphereEffectMessage","msgId":"7460493249762481202","roomId":"7460736793282562857","isShowMsg":true},"assetId":"96972572","partnerAssetId":"97501369","priority":"200","scene":"25v_1_new","endTime":"1735650575","actvityInformation":"{\"activity\":\"25v_1_new\",\"data\":{\"timestamp\":3471301103}}","partnerHotsoonAssetId":"97499721"}
            //     _decodeMessage = pb_webcast.webcast.AtmosphereEffectMessage.decode(msg.payload);
            //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // }
            // else if (msg.method === 'WebcastProfitGameStatusMessage') {
            //     _decodeMessage = pb_webcast.webcast.ProfitGameStatusMessage.decode(msg.payload);
            //     log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // }
            // else if (msg.method === 'LinkMicMethod') {
            //     // 连麦，分数PK
            //     // [2025-1-17 15:36:12:559][7460736793282562857][LinkMicMethod]{"common":{"method":"LinkMicMethod","msgId":"7460784963357611071","roomId":"7460736793282562857","createTime":"1737099371666"},"messageType":"202","channelId":"7460776146611770407","userScores":[{"score":"140184","userId":"57849095051","scoreBlurText":"140184","battleRank":"2","newScoreOpen":true},{"score":"138871","userId":"65305021017","scoreBlurText":"138871","battleRank":"3","newScoreOpen":true},{"score":"258803","userId":"1772053268011588","scoreBlurText":"258803","battleRank":"1","newScoreOpen":true},{"score":"42190","userId":"7454405802713400378","scoreBlurText":"42190","battleRank":"4","newScoreOpen":true}],"fromUserId":"1753068586936313"}
            //     _decodeMessage = pb_webcast.webcast.LinkMicMethod.decode(msg.payload);
            //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // }
            //     //
            // //WebcastQuizAudienceStatusMessage
            //
            // else if (msg.method === 'WebcastRoomStreamAdaptationMessage') {
            //     // // [2025-1-15 18:26:13][WebcastRoomStreamAdaptationMessage][7460065498965854986]{"common":{"method":"WebcastRoomStreamAdaptationMessage","msgId":"7460086611637195812","roomId":"7460065498965854986","isShowMsg":true},"adaptationType":1}
            //     // _decodeMessage = pb_webcast.webcast.RoomStreamAdaptationMessage.decode(msg.payload);
            //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // }
            // else if (msg.method === 'WebcastChatLikeMessage') {
            //     // 好像没什么用
            //     _decodeMessage = pb_webcast.webcast.ChatLikeMessage.decode(msg.payload);
            //     // log(_decodeMessage.common, JSON.stringify(_decodeMessage));
            // }