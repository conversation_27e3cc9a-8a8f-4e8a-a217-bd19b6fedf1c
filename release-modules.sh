
# npm install -g marked

_ver=1.3.1

_douyin_copy_files() {

  mkdir -p release/${_ver}/dist/douyin
  cp ./modules/douyin/dist/*.js ./release/${_ver}/dist/douyin/
  marked -i ./modules/douyin/接入文档-${_ver}.md -o ./release/${_ver}/dist/douyin/接入文档-${_ver}.html
  marked -i ./modules/douyin/CHANGELOG.md -o ./release/${_ver}/dist/douyin/CHANGELOG.html
  # demo
  cp ./modules/douyin/main-*-demo.js ./release/${_ver}/
}

_douyin() {
  cd ./modules/douyin/ || exit
#  ./gen_sdk_proto.sh
  node encryptoJs.js
  cd ../..

  _douyin_copy_files
}

_tiktok() {
  cd ./modules/tiktok/ || exit
#  ./gen_sdk_proto.sh
  node encryptoJs.js
  cd ../..

  mkdir -p release/${_ver}/dist/tiktok
  cp ./modules/tiktok/dist/*.js ./release/${_ver}/dist/tiktok/
  marked -i ./modules/tiktok/tiktok接入文档-${_ver}.md -o ./release/${_ver}/dist/tiktok/tiktok接入文档-${_ver}.html
  cp ./modules/tiktok/main-*-demo.js ./release/${_ver}/
}

_weixin() {
  cd ./modules/weixin/ || exit
  ./build-release.sh
  cp -r ./src/types ./dist/
  cd ../..

  mkdir -p release/${_ver}/dist
  cp -r ./modules/weixin/dist/ ./release/${_ver}/
  cp ./modules/weixin/main-*-demo.js ./release/${_ver}/
}

#rm -rf ./release/${_ver} ./release/${_ver}.7z

#_douyin
#_tiktok
#_douyin_copy_files
_weixin
7z a ./release/${_ver}.7z ./release/${_ver}/

