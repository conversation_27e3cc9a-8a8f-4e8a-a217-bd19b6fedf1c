const {buildFile, buildModuleFile} = require("@yy/secure-loader/dist/builder");

const options = {
    obfuscate: true, // 是否混淆，默认为`false`
    useByteCode: false, // 是否转换为字节码，默认为`false`
    obfuscateConfig: {
        /* 有需要时可以覆盖一些混淆的配置参数 */
        compact: true,
        renameGlobals: true,
        identifierNamesGenerator: "hexadecimal",
        controlFlowFlattening: true,
        controlFlowFlatteningThreshold: 1,
        deadCodeInjection: true,
        deadCodeInjectionThreshold: 1,
        // transformObjectKeys: false,
        debugProtection: true,
        debugProtectionInterval: true,
        // disableConsoleOutput: false,
        stringArray: true,
        rotateStringArray: true,
        // stringArrayMessy: true,
        // stringArrayEncoding: "rc4",
        // stringArrayThreshold: 1,
        // stringArrayThreshold: 0.8,
        unicodeEscapeSequence: true,
        reserveRequireSyntax: true,
        // operationPromote: false,
        // operationPromoteThreshold: 0,
        selfDefending: true,
        // domainLock: [],
        target: "node"
    },
}

buildFile('src/douyin.js', 'dist/douyin.js', options).catch(err => {
    console.error(err);
});
buildFile('src/tiktok.js', 'dist/tiktok.js', options).catch(err => {
    console.error(err);
});
buildFile('src/weixin.js', 'dist/weixin.js', options).catch(err => {
    console.error(err);
});
buildFile('src/kuaishou.js', 'dist/kuaishou.js', options).catch(err => {
    console.error(err);
});
buildFile('src/common.js', 'dist/common.js', options).catch(err => {
    console.error(err);
});
// 不加密
const fs = require('fs').promises;
fs.copyFile('./src/liveApi.js', './dist/liveApi.js').catch(err => {
    console.error(err);
});

// // 直接处理代码：
// const inputCode = `console.log('hello, world!');`
// buildCode(inputCode, {
//     useByteCode: true, // 可配置参数同上
// }).then(output => {
//     // output为输出代码，可以写回文件，或做其它处理
// });