/**
 * ### 启动接收直播间事件接口：
 * 1. 创建对象
 * const {LiveApi} = require("./dist/liveApi");
 * var liveApi = new LiveApi(context);
 *  * @property {string} context - 上下文，对象的标记，EventsData 会返回这个上下文。
 *
 * 2. 开始监控直播间
 * liveApi.startMonitorRoom(mainWindow, liveUrl, eventCallback, logCallback)
 * @property {BrowserWindow} mainWindow - electron的窗口
 * @property {string} liveUrl - 直播间链接,微信视频号传 'weixin'
 * @property {eventCallback} eventCallback - 事件回调
 * @property {logCallback} logCallback - 日志回调
 *
 * 3. 释放 liveApi 对象。停止监听后需要调用
 * liveApi.release()
 *
 * ### 通用的直播间回调接口：EventData
 *
 * 回调函数定义：
 * eventCallback(eventsData)
 *
 * EventsData 定义可参见demo或以下doc
 * */

/**
 * 回调函数的消息事件
 * @typedef {Object} EventsData
 * @property {Array<EventsData>} events_data - 消息事件。
 * @property {number} like_total - 点赞总数。快手和微信是汇总的，不是独立的
 * @property {number} watching_total - 观看总数。快手和微信是汇总的，不是独立的
 * @property {number} timestamp - 客户端收到消息时的 Unix 时间戳。
 * @property {string} live_platform_type - 平台类型。 'douyin', 'tiktok', 'weixin', 'kuaishou'
 * @property {string} context - 上下文，初始化sdk传入的上下文
 * */

/**
 * 消息事件
 * @typedef {Object} EventData
 * @property {number} msg_type - 消息类型：
 * - 1: 点赞消息
 * - 2: 评论消息
 * - 3: 礼物消息
 * - 4: 进房消息
 * - 5: 控制消息
 * @property {string} msg_type_str - 消息类型字符串:
 * - like_msg
 * - comment_msg
 * - gift_msg
 * - member_msg
 * - control_msg
 * @property {LikeMsg} [like_msg] - 点赞消息
 * @property {CommentMsg} [comment_msg] - 评论消息
 * @property {GiftMsg} [gift_msg] - 礼物消息
 * @property {MemberMsg} [member_msg] - 成员消息
 * @property {ControlMsg} [control_msg] - 控制消息
 * */

/**
 * 粉丝团信息
 * 备注：快手只有评论事件会有粉丝团信息，且只有等级
 * @typedef {Object} FansclubInfo
 * @property {string} [name] - 粉丝团名。（douyin:星守护，）
 * @property {number} level - 粉丝团等级。
 * @property {boolean} [status] - 粉丝团生效状态。（ douyin/tiktok 可以获取状态 ）
 * */

/**
 * 用户信息
 * @typedef {Object} UserInfo
 * @property {string} user_name - 用户名。(ks:principalId,dy:displayId,wx:)
 * @property {string} nick_name - 用户昵称。
 * @property {number} level - 消费等级。
 * @property {string} [head_url] - 用户头像。
 * @property {FansclubInfo} [fansclub] - 粉丝团信息。
 * @property {string} [ext] - 拓展信息。
 * */

/**
 * 点赞事件，具体用户点赞数只有抖音有统计，其他平台都为1。
 * 注：同一用户多次点赞微信不会通知多次
 * @typedef {Object} LikeMsg
 * @property {UserInfo} user - 用户信息。
 * @property {number} like_count - 点赞数。
 * */

/**
 * 评论事件。
 * @typedef {Object} CommentMsg
 * @property {UserInfo} user - 用户信息。
 * @property {string} content - 评论内容。
 * @property {string} [language] - 评论语言类型。(tiktok)
 *
 * */

/**
 * 礼物事件。
 * 内部已经计算了当前组数，和连击送出的总组数
 *
 * @typedef {Object} GiftMsg
 * @property {UserInfo} user - 用户信息。
 * @property {UserInfo} to_user - 礼物接收者的信息。(不存在的话就是送给当前主播)
 * @property {string} gift_id - 礼物ID。
 * @property {string} gift_name - 礼物名。
 * @property {string} gift_url - 礼物图片链接。
 * @property {string} gift_price - 礼物价格。（快手没有这个字段,为0）
 * @property {number} count - 当次送出的礼物个数。
 * @property {number} batch_size - 一组数量。
 * @property {number} total_count - 当前连击总组数。(总个数: total_count*batch_size)
 * @property {string} gift_msg_key - 消息key，内部用来归类连击。（wx:combo_id, ks:merge_key, dy:groupId_displayId, tk:groupId_displayId）
 * */

/**
 * 用户进入直播间事件。
 * 快手没有这个事件
 * @typedef {Object} MemberMsg
 * @property {UserInfo} [user] - 用户信息。
 * @property {number} users_total - 直播间总人数
 * */

/**
 * 进入的直播间信息
 * @typedef {Object} EnterLiveInfo
 * @property {string} room_tital - 直播间标题
 * @property {string} own_user_nickname - 主播的昵称
 * @property {string} own_user_head_url - 主播的头像
 * @property {string} [own_user_sec_uid] - 主播的secUid(抖音,tiktok,weixin)
 * @property {string} [own_user_id_str] - 主播的id_str(抖音,tiktok)
 * @property {string} [own_user_uniqueId] - 主播的唯一id(tiktok,kuaishou)
 * */

/**
 * 控制事件，进入直播间失败，成功，关闭直播等
 * @typedef {Object} ControlMsg
 * @property {number} action - 活动id。
 * - 0: 正在直播
 * - 1: 直播结束
 * - 100: url错误。传入的 live_url 为空，或者不是 https 开头的字符串
 * - 101: 其他错误
 *
 * 快手错误事件:
 * - 1000:尚未开始直播
 * - 1001:请求过快，请稍后重试。可能需要登录快手账号，或者重启路由器更换ip地址
 * - 1002: 没有找到直播间名，可能不会中断
 * @property {string} action_msg - 附加信息
 * @property {string} [live_url] - 传入的 url
 * @property {string} [room_name] - 直播间名
 * @property {string} room_id - 直播间id
 * @property {EnterLiveInfo} [enter_live_info] - 直播间详细信息。(action=0 有这个数据)
 *
 * */
