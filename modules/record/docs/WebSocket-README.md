# LiveApi WebSocket 服务

这个WebSocket服务允许你将LiveApi拦截到的直播数据通过WebSocket实时发送给客户端。

## 功能特性

- 🔄 实时转发直播事件数据
- 📊 统计信息广播
- 🔌 多客户端连接支持
- 📝 日志消息转发
- 💓 心跳检测
- 🎯 事件订阅/取消订阅
- 🔧 自定义消息广播
- 🎮 **客户端控制直播间连接** - 客户端可以动态选择连接哪个直播间
- 🔄 动态切换直播间
- 📡 实时连接状态监控
- 🚀 **多直播间同时监控** - 支持同时连接和监控多个直播间
- 🏷️ 连接ID管理 - 每个连接都有唯一ID便于管理
- 📋 连接列表管理 - 实时查看和管理所有活跃连接
- 🪟 **独立窗口架构** - 每个直播间创建独立的Electron窗口
- 👁️ 窗口可见性控制 - 可以显示/隐藏特定直播间的窗口
- 📊 窗口状态监控 - 实时获取所有窗口的状态信息

## 快速开始

### 1. 启动WebSocket服务器

```bash
# 启动带WebSocket功能的LiveApi服务
npm run start:websocket
```

这将会：
- 启动WebSocket服务器在端口8080
- **等待客户端发送连接直播间的请求**
- **为每个直播间创建独立的Electron窗口**
- 将拦截到的数据通过WebSocket发送给连接的客户端

### 2. 连接WebSocket客户端

#### 方式1：使用Node.js客户端
```bash
# 启动Node.js WebSocket客户端
npm run client:websocket
```

#### 方式2：使用HTML客户端
在浏览器中打开 `modules/record/demo/websocket-client.html`

#### 方式3：使用自定义客户端
连接到 `ws://localhost:8080`

### 3. 连接直播间

连接WebSocket后，发送以下消息来连接直播间：

```json
{
  "type": "connect_live",
  "liveUrl": "https://live.kuaishou.com/u/3xtzx7d2tpj4j2s"
}
```

### 4. 管理多个直播间（独立窗口架构）

你可以同时连接多个直播间，每个直播间会创建独立的Electron窗口：

```json
// 连接第一个直播间（创建窗口1）
{"type": "connect_live", "liveUrl": "https://live.kuaishou.com/u/user1"}

// 连接第二个直播间（创建窗口2）
{"type": "connect_live", "liveUrl": "https://live.douyin.com/room2"}

// 获取所有连接列表
{"type": "list_connections"}

// 获取窗口信息
{"type": "get_windows_info"}

// 显示特定窗口
{"type": "toggle_window", "connectionId": "abc123", "visible": true}

// 隐藏特定窗口
{"type": "toggle_window", "connectionId": "abc123", "visible": false}

// 断开特定连接（关闭对应窗口）
{"type": "disconnect_connection", "connectionId": "abc123"}

// 断开所有连接（关闭所有窗口）
{"type": "disconnect_live"}
```

支持的直播间链接：
- **快手**: `https://live.kuaishou.com/u/用户ID`
- **抖音**: `https://live.douyin.com/直播间ID`
- **TikTok**: `https://www.tiktok.com/@username/live`
- **微信视频号**: `weixin`

## API 文档

### 服务器发送的消息类型

#### 1. 欢迎消息
```json
{
  "type": "welcome",
  "message": "Connected to LiveApi WebSocket Server",
  "timestamp": 1234567890123
}
```

#### 2. 直播事件
```json
{
  "type": "live_events",
  "connectionId": "abc123",
  "liveUrl": "https://live.kuaishou.com/u/3xtzx7d2tpj4j2s",
  "data": {
    "events_data": [...],
    "timestamp": 1234567890123,
    "live_platform_type": "kuaishou",
    "context": "websocket-demo-context"
  },
  "timestamp": 1234567890123,
  "source": "liveApi"
}
```

#### 3. 日志消息
```json
{
  "type": "log",
  "message": "日志内容",
  "timestamp": 1234567890123,
  "source": "liveApi"
}
```

#### 4. 统计信息
```json
{
  "type": "stats",
  "data": {
    "totalEvents": 100,
    "eventsByType": {
      "comment_msg": 50,
      "gift_msg": 30,
      "enter_msg": 20
    },
    "uptime": 60000,
    "wsServerStatus": {
      "isRunning": true,
      "port": 8080,
      "clientCount": 2
    }
  },
  "timestamp": 1234567890123,
  "source": "bridge"
}
```

#### 5. 连接直播间响应
```json
{
  "type": "connect_live_response",
  "success": true,
  "connectionId": "abc123",
  "liveUrl": "https://live.kuaishou.com/u/3xtzx7d2tpj4j2s",
  "timestamp": 1234567890123
}
```

#### 6. 断开直播间响应
```json
{
  "type": "disconnect_live_response",
  "success": true,
  "connectionId": "abc123",
  "disconnectedCount": 1,
  "timestamp": 1234567890123
}
```

#### 7. 连接列表响应
```json
{
  "type": "connections_list_response",
  "connections": [
    {
      "id": "abc123",
      "liveUrl": "https://live.kuaishou.com/u/user1",
      "isConnected": true,
      "startTime": 1234567890123,
      "uptime": 60000,
      "eventCount": 150,
      "lastEventTime": 1234567890123
    }
  ],
  "totalCount": 1,
  "timestamp": 1234567890123
}
```

#### 8. 连接状态变化通知
```json
{
  "type": "live_connection_added",
  "connectionId": "abc123",
  "liveUrl": "https://live.kuaishou.com/u/3xtzx7d2tpj4j2s",
  "totalConnections": 2,
  "timestamp": 1234567890123,
  "source": "bridge"
}
```

#### 9. 状态信息响应
```json
{
  "type": "status_response",
  "status": {
    "bridge": {
      "totalConnections": 5,
      "activeConnections": 3,
      "connections": [
        {
          "id": "abc123",
          "liveUrl": "https://live.kuaishou.com/u/user1",
          "isConnected": true,
          "startTime": 1234567890123,
          "uptime": 60000,
          "stats": {
            "totalEvents": 150,
            "eventsByType": {"comment_msg": 100, "gift_msg": 50},
            "lastEventTime": 1234567890123
          }
        }
      ]
    },
    "websocket": {
      "isRunning": true,
      "port": 8080,
      "clientCount": 2
    },
    "globalStats": {
      "totalEvents": 500,
      "activeConnections": 3,
      "uptime": 120000
    }
  },
  "timestamp": 1234567890123
}
```

#### 9. 自定义消息
```json
{
  "type": "custom",
  "data": {
    "message": "自定义消息内容",
    "customData": {...}
  },
  "timestamp": 1234567890123,
  "source": "bridge"
}
```

### 客户端发送的消息类型

#### 1. 连接直播间
```json
{
  "type": "connect_live",
  "liveUrl": "https://live.kuaishou.com/u/3xtzx7d2tpj4j2s"
}
```

#### 2. 断开直播间连接
```json
{
  "type": "disconnect_live",
  "connectionId": "abc123"  // 可选，不提供则断开所有连接
}
```

#### 3. 获取连接列表
```json
{
  "type": "list_connections"
}
```

#### 4. 断开特定连接
```json
{
  "type": "disconnect_connection",
  "connectionId": "abc123"
}
```

#### 5. 窗口管理
```json
{
  "type": "toggle_window",
  "connectionId": "abc123",
  "visible": true
}
```

#### 6. 获取窗口信息
```json
{
  "type": "get_windows_info"
}
```

#### 7. 获取状态信息
```json
{
  "type": "get_status"
}
```

#### 8. Ping消息
```json
{
  "type": "ping",
  "timestamp": 1234567890123
}
```

#### 9. 订阅事件
```json
{
  "type": "subscribe",
  "eventTypes": ["live_events", "log", "stats"]
}
```

#### 10. 取消订阅
```json
{
  "type": "unsubscribe",
  "eventTypes": ["log"]
}
```

## 代码示例

### 方式1：客户端控制模式（推荐 - 独立窗口架构）

```javascript
const LiveWebSocketBridge = require('./modules/record/src/live-websocket-bridge');

// 创建WebSocket桥接器
const bridge = new LiveWebSocketBridge('my-context', 8080, false);

// 启动服务器，不需要传入主窗口（每个直播间会创建独立窗口）
bridge.startServer();

// 客户端通过WebSocket发送连接请求：
// {"type":"connect_live","liveUrl":"https://live.kuaishou.com/u/xxxxx"}
// 每个连接会自动创建独立的Electron窗口
```

### 方式2：传统模式（向后兼容）

```javascript
const LiveWebSocketBridge = require('./modules/record/src/live-websocket-bridge');

// 创建WebSocket桥接器
const bridge = new LiveWebSocketBridge('my-context', 8080, false);

// 直接启动监控指定直播间
bridge.startMonitorRoom(mainWindow, 'https://live.kuaishou.com/u/xxxxx');

// 发送自定义消息
bridge.broadcast({
  message: 'Hello from server',
  data: { key: 'value' }
});

// 获取统计信息
const stats = bridge.getStats();
console.log(stats);

// 释放资源
bridge.release();
```

### 客户端连接示例

```javascript
const WebSocket = require('ws');

const ws = new WebSocket('ws://localhost:8080');

ws.on('open', () => {
  console.log('连接成功');
  
  // 订阅事件
  ws.send(JSON.stringify({
    type: 'subscribe',
    eventTypes: ['live_events', 'log']
  }));
});

ws.on('message', (data) => {
  const message = JSON.parse(data);
  console.log('收到消息:', message);
});

// 连接直播间
ws.send(JSON.stringify({
  type: 'connect_live',
  liveUrl: 'https://live.kuaishou.com/u/3xtzx7d2tpj4j2s'
}));

// 断开直播间
ws.send(JSON.stringify({
  type: 'disconnect_live'
}));

// 获取状态
ws.send(JSON.stringify({
  type: 'get_status'
}));
```

## 配置选项

### LiveWebSocketBridge 构造函数参数

- `context` (string): 上下文标识，默认 'uuid-1234'
- `wsPort` (number): WebSocket服务器端口，默认 8080
- `test` (boolean): 测试模式，默认 false

### WebSocketServer 构造函数参数

- `port` (number): 服务器端口，默认 8080

## 支持的直播平台

- 抖音 (douyin.com)
- 快手 (kuaishou.com)
- TikTok (tiktok.com)
- 微信视频号 (传入 'weixin')

## 使用流程

### 客户端控制模式（推荐）

1. **启动服务器**
   ```bash
   npm run start:websocket
   ```

2. **连接WebSocket客户端**
   ```bash
   npm run client:websocket
   # 或在浏览器中打开 websocket-client.html
   ```

3. **连接多个直播间（每个创建独立窗口）**
   - 在Node.js客户端中输入：`connect https://live.kuaishou.com/u/user1`
   - 再输入：`connect https://live.douyin.com/room2`
   - 在HTML客户端中输入直播间链接并点击"添加直播间"
   - 或发送WebSocket消息：`{"type":"connect_live","liveUrl":"直播间链接"}`
   - **每个直播间会自动创建独立的Electron窗口**

4. **管理连接和窗口**
   - 查看所有连接：`list` 或点击"刷新列表"
   - 查看窗口信息：`windows` 或点击"窗口信息"
   - 显示窗口：`show 连接ID` 或点击"显示窗口"
   - 隐藏窗口：`hide 连接ID` 或点击"隐藏窗口"
   - 断开特定连接：`disconnect 连接ID` 或点击"断开"（会关闭对应窗口）
   - 断开所有连接：`disconnect` 或点击"断开所有"（会关闭所有窗口）

5. **接收直播数据**
   - 服务器会自动将所有直播间的事件转发给客户端
   - 每个事件都包含 `connectionId` 和 `liveUrl` 来标识来源
   - 数据通过独立窗口中的流量监听获取

6. **监控状态**
   - 获取详细状态：`status` 或点击"获取状态"
   - 查看统计信息：实时显示总连接数、事件数等
   - 监控窗口状态：显示/隐藏状态、窗口位置等

## 注意事项

1. 确保端口8080没有被其他程序占用
2. 如果需要修改端口，请同时修改服务器和客户端的配置
3. 在生产环境中，建议添加身份验证和错误处理
4. 客户端可以动态控制连接哪个直播间，无需重启服务器
5. 支持多个客户端同时连接，所有客户端都会收到相同的直播数据
6. 支持同时监控多个直播间，每个连接都有唯一ID
7. 可以独立管理每个直播间连接，支持选择性断开
8. **独立窗口架构**：每个直播间创建独立的Electron窗口进行数据监听
9. 窗口可见性控制：可以显示/隐藏特定直播间的监控窗口
10. 通过监听独立窗口的数据流量来区分不同直播间的数据

## 故障排除

### 连接失败
- 检查端口是否被占用
- 确认服务器已启动
- 检查防火墙设置

### 没有收到数据
- 确认直播间链接有效
- 检查直播间是否有活动
- 查看控制台日志

### 性能问题
- 监控客户端连接数量
- 检查消息处理速度
- 考虑添加消息缓冲机制
