const {douyin} = require("./release_webcast.min");
const {gunzip} = require("node:zlib");
const fs = require("fs");
const {packCtlEvent, initEvents} = require("./common");

/// @secureBegin

class LiveDouyin {
    constructor(context, test=false) {
        this._inline_context = context;
        this._inline_gifts = new Map(); // 存储礼物数据的 Map
        this._inline_TIMEOUT_SECONDS = 15 * 1000; // 15秒（以毫秒为单位）
        this._inline_txmll = false;
        this._inline_test_hex = test;
    }

    // 添加或更新礼物数据
    _inline_addOrUpdateGift(combo_id, giftCount, sendTime) {
        const now = Date.now();
        this._inline_gifts.set(combo_id, {
            giftCount,
            lastUpdated: now,
            sendTime: sendTime,
            timeout: setTimeout(() => {
                this._inline_gifts.delete(combo_id); // 15秒后删除未更新的数据
            }, this._inline_TIMEOUT_SECONDS)
        });
        return combo_id;
    }

    // 减去上一次的礼物个数
    _inline_decrementGift(combo_id, giftCount, sendTime) {
        if (!this._inline_gifts.has(combo_id)) {
            return null; // 如果 ID 不存在，返回 null
        }
        const gift = this._inline_gifts.get(combo_id);
        clearTimeout(gift.timeout); // 清除旧的定时器
        var ret_count = giftCount - gift.giftCount;
        gift.giftCount = giftCount;
        gift.lastUpdated = Date.now();
        gift.sendTime = sendTime;
        gift.timeout = setTimeout(() => {
            this._inline_gifts.delete(combo_id); // 15秒后删除未更新的数据
        }, this._inline_TIMEOUT_SECONDS);
        this._inline_gifts.set(combo_id, gift);
        return ret_count;
    }

    // 获取礼物数据
    _inline_getGift(combo_id) {
        return this._inline_gifts.get(combo_id) || null;
    }

    /**
     * 释放内部数据
     * */
    release() {
        this._inline_gifts.forEach(_gift => {
            clearTimeout(_gift.timeout);
            _gift.timeout = null;
        })
        this._inline_gifts.clear();
    }

    /**
     * 一次只能启动一个
     * */
    startMonitorRoom(mainWindow, liveUrl, eventCallback, logCallback) {
        if (liveUrl === '' || liveUrl === undefined || !liveUrl || !/^https?:\/\//.test(liveUrl)) {
            var eventsData = initEvents(this._inline_context, 'douyin');
            eventsData.events_data.push(packCtlEvent(100,  'liveUrl错误', liveUrl))
            eventCallback(eventsData);
            return;
        }
        try {
            this._inline_txmll = process.env.x234534trsegfsdc || false;
        } catch (err) {
            this._inline_txmll = false;
        }
        const closeWebSocket = false;// 是否禁用 ws
        const defaultSession = mainWindow.webContents.session;
        defaultSession.webRequest.onBeforeRequest((details, callback) => {
            if (details.url.startsWith(atob('d3NzOi8v')) || details.url.startsWith(atob('d3M6Ly8='))) {// cmp ws://,  wss://
                // this._inline_self_debug('拦截 WebSocket 请求:' + details.url);
                // 阻止 WebSocket 请求
                callback({cancel: closeWebSocket});
            } else if (details.url.startsWith(atob('aHR0cHM6Ly8='/* https:// */))) {
                // this._inline_self_debug('拦截HTTPS请求:' + details.url);
                // enter
                callback({});
            } else {
                // 允许其他请求
                callback({});
            }
        });
        try {
            mainWindow.webContents.debugger.attach()
        } catch (err) {
            this._inline_self_debug('Debugger attach failed : ' + err)
        }

        const gWebcastObj = {};
        const requestIds = {};
        mainWindow.webContents.debugger.on(atob('ZGV0YWNo'/* 'detach' */), (event, reason) => {
            // this._inline_self_debug('Debugger detached due to : ' + reason)
        })
        mainWindow.webContents.debugger.on(atob('bWVzc2FnZQ=='/* 'message' */), (event, method, params) => {
            // this._inline_self_debug('info:'+method)
            // this._inline_self_debug('info:'+method + ':' + JSON.stringify(params))
            if (method === atob('TmV0d29yay5sb2FkaW5nRmluaXNoZWQ='/* 'Network.loadingFinished' */)) {
                // 2.
                if (requestIds.hasOwnProperty(params.requestId)) {
                    mainWindow.webContents.debugger.sendCommand(atob('TmV0d29yay5nZXRSZXNwb25zZUJvZHk='/* 'Network.getResponseBody' */), {requestId: params.requestId}, params.sessionId).then((response) => {
                        if (requestIds[params.requestId].includes(atob('L3dlYmNhc3QvaW0vZmV0Y2gvPw=='/* '/webcast/im/fetch/?' */))) {
                            // 直播间数据
                            // try {
                                this._inline_decodePayload(response.body, liveUrl, eventCallback, logCallback);
                            // } catch (e) {
                            //     // event
                            //     console.log('decodePayload error:' + e.message);
                            // }
                        } else if (requestIds[params.requestId].includes(atob('L3dlYmNhc3Qvcm9vbS93ZWIvZW50ZXIvPw=='/* '/webcast/room/web/enter/?' */))) {
                            // 直播间已关闭
                            // {"data":{"data":[{"id_str":"7463265007518665522","status":4,"status_str":"4","title":"日常出摊中～","user_count_str":"0","mosaic_status":0,"mosaic_status_str":"","admin_user_ids":[],"admin_user_ids_str":[],"live_room_mode":0,"has_commerce_goods":false,"linker_map":{},"AnchorABMap":{},"like_count":0,"owner_user_id_str":"","admin_user_open_ids":[],"admin_user_open_ids_str":[],"owner_open_id_str":""}],"enter_room_id":"7463265007518665522","user":{"id_str":"1063979932718648","sec_uid":"MS4wLjABAAAAujZpL9rGkJudv-n1jC3lNy8BNHSzUSeYwEsyLoo7BFH273Ta2VLqQH_QPGCJ-Xp-","nickname":"豆皮大师兄","avatar_thumb":{"url_list":["https://p26.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_cc6ccbe7a15018027b8c1ad1635fe527.jpeg?from=**********","https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_cc6ccbe7a15018027b8c1ad1635fe527.jpeg?from=**********","https://p11.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_cc6ccbe7a15018027b8c1ad1635fe527.jpeg?from=**********"]},"follow_info":{"follow_status":0,"follow_status_str":"0"},"foreign_user":0,"open_id_str":""},"qrcode_url":"","enter_mode":0,"room_status":2,"partition_road_map":{},"similar_rooms":[],"shark_decision_conf":""},"extra":{"now":1737690252135},"status_code":0}

                            // 直播间正在直播
                            // {"data":{"data":[{"id_str":"7463311308587453219","status":2,"status_str":"2","title":"老魏书场","user_count_str":"3000+","cover":{"url_list":["https://p3-webcast-sign.douyinpic.com/image-cut-tos-priv/c0790a834da4de99d68f032da109a068~tplv-qz53dukwul-common-resize:0:0.image?biz_tag=app_6383_webcast\u0026from=webcast.room.pack\u0026l=20250124114559ED58FD8ABA2CB55315CB\u0026lk3s=39e7556e\u0026s=enter_room\u0026sc=webcast_cover\u0026x-expires=**********\u0026x-signature=Jl0XMFJ98MWczqiAIgN8qv1m760%3D","https://p11-webcast-sign.douyinpic.com/image-cut-tos-priv/c0790a834da4de99d68f032da109a068~tplv-qz53dukwul-common-resize:0:0.image?biz_tag=app_6383_webcast\u0026from=webcast.room.pack\u0026l=20250124114559ED58FD8ABA2CB55315CB\u0026lk3s=39e7556e\u0026s=enter_room\u0026sc=webcast_cover\u0026x-expires=**********\u0026x-signature=dHsxBxgDmDKlRWKURyIX712kAys%3D"]},"stream_url":{"flv_pull_url":{"FULL_HD1":"http://pull-flv-l26.douyincdn.com/third/stream-7463311045327735586_or4.flv?expire=679c4777\u0026major_anchor_level=common\u0026sign=826cd8a7c4e09a98df6d2f18facc4a8a\u0026unique_id=stream-7463311045327735586_486_flv_or4","HD1":"http://pull-flv-l26.douyincdn.com/third/stream-7463311045327735586_hd.flv?expire=679c4777\u0026major_anchor_level=common\u0026sign=58228b94f5cc1685de6b8f9bdada23dc\u0026unique_id=stream-7463311045327735586_486_flv_hd","SD1":"http://pull-flv-l26.douyincdn.com/third/stream-7463311045327735586_ld.flv?expire=679c4777\u0026major_anchor_level=common\u0026sign=2132906f95f13664c9975d16f18c5f0e\u0026unique_id=stream-7463311045327735586_486_flv_ld","SD2":"http://pull-flv-l26.douyincdn.com/third/stream-7463311045327735586_sd.flv?expire=679c4777\u0026major_anchor_level=common\u0026sign=d7550ce413aa8db6106be46f52d48bf4\u0026unique_id=stream-7463311045327735586_486_flv_sd"},"default_resolution":"HD1","hls_pull_url_map":{"FULL_HD1":"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_or4.m3u8?expire=679c4777\u0026sign=4ed9d1f06425251f4c56af95d7d130af\u0026major_anchor_level=common","HD1":"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_hd.m3u8?expire=679c4777\u0026sign=b6476de39a6374f898f4a236b04b2f6f\u0026major_anchor_level=common","SD1":"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_ld.m3u8?expire=679c4777\u0026sign=925e8705fa19c5f37bc64c96a971a82f\u0026major_anchor_level=common","SD2":"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_sd.m3u8?expire=679c4777\u0026sign=7398a32e96b38364283315719532690f\u0026major_anchor_level=common"},"hls_pull_url":"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_hd.m3u8?expire=679c4777\u0026sign=b6476de39a6374f898f4a236b04b2f6f\u0026major_anchor_level=common","stream_orientation":1,"live_core_sdk_data":{"pull_data":{"options":{"default_quality":{"name":"超清","sdk_key":"hd","v_codec":"264","resolution":"720x1280","level":3,"v_bit_rate":0,"additional_content":"","fps":0,"disable":0},"qualities":[{"name":"标清","sdk_key":"ld","v_codec":"264","resolution":"540x960","level":1,"v_bit_rate":1000000,"additional_content":"","fps":25,"disable":0},{"name":"高清","sdk_key":"sd","v_codec":"264","resolution":"720x1280","level":2,"v_bit_rate":2000000,"additional_content":"","fps":30,"disable":0},{"name":"超清","sdk_key":"hd","v_codec":"264","resolution":"720x1280","level":3,"v_bit_rate":4000000,"additional_content":"","fps":30,"disable":0},{"name":"蓝光","sdk_key":"origin","v_codec":"264","resolution":"1080x1920","level":4,"v_bit_rate":4355000,"additional_content":"","fps":0,"disable":0}]},"stream_data":"{\"common\":{\"ts\":\"1737690359\",\"session_id\":\"037-20250124114559ED58FD8ABA2CB55315CB\",\"stream\":\"7463311045327735586\",\"rule_ids\":\"{\\\"ab_version_trace\\\":null,\\\"sched\\\":\\\"{\\\\\\\"result\\\\\\\":{\\\\\\\"hit\\\\\\\":\\\\\\\"default\\\\\\\",\\\\\\\"cdn\\\\\\\":547}}\\\"}\",\"common_trace\":\"{\\\"StrategyTrace\\\":{\\\"Neptune\\\":{\\\"PlayStream\\\":{\\\"ids\\\":null}}},\\\"BusinessType\\\":\\\"default_business\\\",\\\"BigeventAnchorLevel\\\":\\\"\\\"}\",\"app_id\":\"100102\",\"major_anchor_level\":\"common\",\"mode\":\"Normal\",\"lines\":{\"main\":\"line_547\"},\"p2p_params\":{\"PcdnIsolationConfig\":{\"StunV6Domain\":\"vc-mirror-v6.ndcpp.com\",\"HoleV4Domain\":\"vcl-hole.ndcpp.com\",\"HoleV6Domain\":\"vcl-hole-v6.ndcpp.com\",\"IsolationName\":\"isolation1\",\"FsV4Domain\":\"vcl-brain.ndcpp.com\",\"FsV6Domain\":\"vcl-brain-v6.ndcpp.com\",\"StunV4Domain\":\"vc-mirror.ndcpp.com\"}},\"stream_data_content_encoding\":\"default\",\"common_sdk_params\":{\"main\":\"{}\"},\"stream_name\":\"stream-7463311045327735586\",\"main_push_id\":486,\"backup_push_id\":0},\"data\":{\"hd\":{\"main\":{\"flv\":\"http://pull-flv-l26.douyincdn.com/third/stream-7463311045327735586_hd.flv?expire=679c4777\u0026major_anchor_level=common\u0026sign=58228b94f5cc1685de6b8f9bdada23dc\u0026unique_id=stream-7463311045327735586_486_flv_hd\",\"hls\":\"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_hd.m3u8?expire=679c4777\u0026sign=b6476de39a6374f898f4a236b04b2f6f\u0026major_anchor_level=common\",\"cmaf\":\"\",\"dash\":\"\",\"lls\":\"http://pull-lls-l26.douyincdn.com/third/stream-7463311045327735586_hd.sdp?expire=679c4777\u0026major_anchor_level=common\u0026sign=b0f2a107ad54f26b2438c38bc31fe9f3\u0026unique_id=stream-7463311045327735586_486_lls_hd\",\"tsl\":\"\",\"tile\":\"\",\"http_ts\":\"\",\"ll_hls\":\"\",\"sdk_params\":\"{\\\"VCodec\\\":\\\"h264\\\",\\\"vbitrate\\\":4000000,\\\"resolution\\\":\\\"720x1280\\\",\\\"gop\\\":2,\\\"drType\\\":\\\"sdr\\\",\\\"fps\\\":30}\",\"enableEncryption\":false}},\"md\":{\"main\":{\"flv\":\"https://pull-flv-l26-admin.douyincdn.com/third/stream-7463311045327735586_md.flv?expire=679c4777\u0026major_anchor_level=common\u0026sign=00aa48f4a96a192ce0d90cfe2a712b3f\u0026unique_id=stream-7463311045327735586_486_flv_md\",\"hls\":\"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_md.m3u8?expire=679c4777\u0026sign=72355a78a7c3d65553d8d50ebf681bcf\u0026major_anchor_level=common\",\"cmaf\":\"\",\"dash\":\"\",\"lls\":\"http://pull-lls-l26.douyincdn.com/third/stream-7463311045327735586_md.sdp?expire=679c4777\u0026major_anchor_level=common\u0026sign=377bfaa14bfe112274b6489d14eed8a6\u0026unique_id=stream-7463311045327735586_486_lls_md\",\"tsl\":\"\",\"tile\":\"\",\"http_ts\":\"\",\"ll_hls\":\"\",\"sdk_params\":\"{\\\"VCodec\\\":\\\"h264\\\",\\\"vbitrate\\\":800000,\\\"resolution\\\":\\\"360x640\\\",\\\"gop\\\":2,\\\"drType\\\":\\\"sdr\\\",\\\"fps\\\":15}\",\"enableEncryption\":false}},\"origin\":{\"main\":{\"flv\":\"http://pull-flv-l26.douyincdn.com/third/stream-7463311045327735586_or4.flv?expire=679c4777\u0026major_anchor_level=common\u0026sign=826cd8a7c4e09a98df6d2f18facc4a8a\u0026unique_id=stream-7463311045327735586_486_flv_or4\",\"hls\":\"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_or4.m3u8?expire=679c4777\u0026sign=4ed9d1f06425251f4c56af95d7d130af\u0026major_anchor_level=common\",\"cmaf\":\"\",\"dash\":\"\",\"lls\":\"http://pull-lls-l26.douyincdn.com/third/stream-7463311045327735586_or4.sdp?expire=679c4777\u0026major_anchor_level=common\u0026sign=4330f3a6561138f10d396ace181a5dd5\u0026unique_id=stream-7463311045327735586_486_lls_or4\",\"tsl\":\"\",\"tile\":\"\",\"http_ts\":\"\",\"ll_hls\":\"\",\"sdk_params\":\"{\\\"VCodec\\\":\\\"h264\\\",\\\"vbitrate\\\":4355000,\\\"resolution\\\":\\\"1080x1920\\\",\\\"gop\\\":2,\\\"drType\\\":\\\"sdr\\\"}\",\"enableEncryption\":false}},\"ao\":{\"main\":{\"flv\":\"http://pull-flv-l26.douyincdn.com/third/stream-7463311045327735586.flv?expire=679c4777\u0026major_anchor_level=common\u0026only_audio=1\u0026sign=575331a294816c372b9cd2e9f0e3e203\u0026unique_id=stream-7463311045327735586_486_flv\",\"hls\":\"\",\"cmaf\":\"\",\"dash\":\"\",\"lls\":\"\",\"tsl\":\"\",\"tile\":\"\",\"http_ts\":\"\",\"ll_hls\":\"\",\"sdk_params\":\"{\\\"VCodec\\\":\\\"h264\\\",\\\"vbitrate\\\":0,\\\"resolution\\\":\\\"\\\",\\\"gop\\\":2,\\\"drType\\\":\\\"sdr\\\",\\\"fps\\\":0}\",\"enableEncryption\":false}},\"ld\":{\"main\":{\"flv\":\"http://pull-flv-l26.douyincdn.com/third/stream-7463311045327735586_ld.flv?expire=679c4777\u0026major_anchor_level=common\u0026sign=2132906f95f13664c9975d16f18c5f0e\u0026unique_id=stream-7463311045327735586_486_flv_ld\",\"hls\":\"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_ld.m3u8?expire=679c4777\u0026sign=925e8705fa19c5f37bc64c96a971a82f\u0026major_anchor_level=common\",\"cmaf\":\"\",\"dash\":\"\",\"lls\":\"http://pull-lls-l26.douyincdn.com/third/stream-7463311045327735586_ld.sdp?expire=679c4777\u0026major_anchor_level=common\u0026sign=34be0317a2239311efa215b6cb678390\u0026unique_id=stream-7463311045327735586_486_lls_ld\",\"tsl\":\"\",\"tile\":\"\",\"http_ts\":\"\",\"ll_hls\":\"\",\"sdk_params\":\"{\\\"VCodec\\\":\\\"h264\\\",\\\"vbitrate\\\":1000000,\\\"resolution\\\":\\\"540x960\\\",\\\"gop\\\":2,\\\"drType\\\":\\\"sdr\\\",\\\"fps\\\":25}\",\"enableEncryption\":false}},\"sd\":{\"main\":{\"flv\":\"http://pull-flv-l26.douyincdn.com/third/stream-7463311045327735586_sd.flv?expire=679c4777\u0026major_anchor_level=common\u0026sign=d7550ce413aa8db6106be46f52d48bf4\u0026unique_id=stream-7463311045327735586_486_flv_sd\",\"hls\":\"http://pull-hls-l26.douyincdn.com/third/stream-7463311045327735586_sd.m3u8?expire=679c4777\u0026sign=7398a32e96b38364283315719532690f\u0026major_anchor_level=common\",\"cmaf\":\"\",\"dash\":\"\",\"lls\":\"http://pull-lls-l26.douyincdn.com/third/stream-7463311045327735586_sd.sdp?expire=679c4777\u0026major_anchor_level=common\u0026sign=f0163116bc2728ef56ae6295b96b3580\u0026unique_id=stream-7463311045327735586_486_lls_sd\",\"tsl\":\"\",\"tile\":\"\",\"http_ts\":\"\",\"ll_hls\":\"\",\"sdk_params\":\"{\\\"VCodec\\\":\\\"h264\\\",\\\"vbitrate\\\":2000000,\\\"resolution\\\":\\\"720x1280\\\",\\\"gop\\\":2,\\\"drType\\\":\\\"sdr\\\",\\\"fps\\\":30}\",\"enableEncryption\":false}}}}"}},"extra":{"height":1920,"width":1080,"fps":0,"max_bitrate":0,"min_bitrate":0,"default_bitrate":0,"bitrate_adapt_strategy":0,"anchor_interact_profile":0,"audience_interact_profile":0,"hardware_encode":false,"video_profile":0,"h265_enable":false,"gop_sec":0,"bframe_enable":false,"roi":false,"sw_roi":false,"bytevc1_enable":false},"pull_datas":{}},"mosaic_status":0,"mosaic_status_str":"0","admin_user_ids":[4073060725165572,3240997518056493,2343787517201438,2985883764013115,2352543933662804,2304172413690023,4198408319934489,104185844199,58175093423,105013868442,4036995543743559,4066407759225428,111155545327,2590096536119431,1490539769765260,93247362213],"admin_user_ids_str":["4073060725165572","3240997518056493","2343787517201438","2985883764013115","2352543933662804","2304172413690023","4198408319934489","104185844199","58175093423","105013868442","4036995543743559","4066407759225428","111155545327","2590096536119431","1490539769765260","93247362213"],"owner":{"id_str":"2427347483241595","sec_uid":"MS4wLjABAAAApyBAI83gxuGiVvHYpriZzguJK9iVJyI8kWwsn9ord763KHVnpXOqsMViJd6gBQ_w","nickname":"老魏评书","avatar_thumb":{"url_list":["https://p11.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_f9f03ada4141535cae95dbf004c007f8.jpeg?from=**********","https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_f9f03ada4141535cae95dbf004c007f8.jpeg?from=**********","https://p26.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_f9f03ada4141535cae95dbf004c007f8.jpeg?from=**********"]},"follow_info":{"follow_status":0,"follow_status_str":"0"},"subscribe":{"is_member":false,"level":0,"identity_type":0,"buy_type":0,"open":1},"foreign_user":0,"open_id_str":""},"room_auth":{"Chat":true,"Danmaku":false,"Gift":true,"LuckMoney":true,"Digg":true,"RoomContributor":true,"Props":true,"UserCard":true,"POI":true,"MoreAnchor":1,"Banner":1,"Share":1,"UserCorner":0,"Landscape":1,"LandscapeChat":1,"PublicScreen":1,"GiftAnchorMt":0,"RecordScreen":2,"DonationSticker":0,"HourRank":0,"CommerceCard":1,"AudioChat":2,"DanmakuDefault":0,"KtvOrderSong":2,"SelectionAlbum":2,"Like":0,"MultiplierPlayback":0,"DownloadVideo":0,"Collect":2,"TimedShutdown":0,"Seek":0,"Denounce":0,"Dislike":0,"OnlyTa":0,"CastScreen":1,"CommentWall":1,"BulletStyle":0,"ShowGamePlugin":2,"VSGift":0,"VSTopic":0,"VSRank":0,"AdminCommentWall":0,"CommerceComponent":1,"DouPlus":1,"GamePointsPlaying":2,"Poster":2,"Highlights":2,"TypingCommentState":1,"StrokeUpDownGuide":1,"UpRightStatsFloatingLayer":1,"CastScreenExplicit":2,"Selection":2,"IndustryService":0,"VerticalRank":1,"EnterEffects":0,"FansClub":1,"EmojiOutside":0,"CanSellTicket":0,"DouPlusPopularityGem":0,"MissionCenter":0,"ExpandScreen":2,"FansGroup":2,"Topic":2,"AnchorMission":0,"Teleprompter":0,"LongTouch":2,"FirstFeedHistChat":1,"MoreHistChat":1,"TaskBanner":0,"SpecialStyle":{"Chat":{"UnableStyle":0,"Content":"主播已设置所有人可评论","OffType":0,"AnchorSwitchForPaidLive":0,"ContentForPaidLive":""},"Like":{"UnableStyle":0,"Content":"","OffType":0,"AnchorSwitchForPaidLive":0,"ContentForPaidLive":""}},"FixedChat":2,"QuizGamePointsPlaying":2},"live_room_mode":1,"stats":{"total_user_desp":"","like_count":0,"total_user_str":"7万+","user_count_str":"3113"},"has_commerce_goods":true,"linker_map":{},"linker_detail":{"linker_play_modes":[],"big_party_layout_config_version":0,"accept_audience_pre_apply":true,"linker_ui_layout":0,"enable_audience_linkmic":0,"function_type":"","linker_map_str":{},"ktv_lyric_mode":"","init_source":"","forbid_apply_from_other":false,"ktv_exhibit_mode":0,"enlarge_guest_turn_on_source":0,"playmode_detail":{},"client_ui_info":"","manual_open_ui":0,"feature_list":[]},"room_view_stats":{"is_hidden":false,"display_short":"3113","display_middle":"3113","display_long":"3113在线观众","display_value":3113,"display_version":1663849727,"incremental":false,"display_type":1,"display_short_anchor":"3113","display_middle_anchor":"3113","display_long_anchor":"3113在线观众"},"scene_type_info":{"is_union_live_room":false,"is_life":false,"is_protected_room":0,"is_lasted_goods_room":2,"is_desire_room":2,"commentary_type":false,"is_sub_orientation_vertical_room":2},"toolbar_data":{"entrance_list":[{"group_id":5,"component_type":24,"op_type":0,"text":"更多","schema_url":"","show_type":1,"data_status":1,"extra":""},{"group_id":6,"component_type":29,"op_type":0,"text":"快捷送礼","schema_url":"","icon":{"url_list":["https://p3-webcast.douyinpic.com/img/webcast/7ef47758a435313180e6b78b056dda4e.png~tplv-resize:0:0.image","https://p11-webcast.douyinpic.com/img/webcast/7ef47758a435313180e6b78b056dda4e.png~tplv-resize:0:0.image"],"uri":"webcast/7ef47758a435313180e6b78b056dda4e.png","height":0,"width":0,"avg_color":"_inline_FFFFFF","image_type":0,"open_web_url":"","is_animated":false,"flex_setting_list":[],"text_setting_list":[]},"show_type":3,"data_status":1,"extra":"{\"id\":463,\"name\":\"小心心\",\"type\":1,\"diamond_count\":1,\"combo\":true,\"image\":{\"url_list\":[\"https://p3-webcast.douyinpic.com/img/webcast/7ef47758a435313180e6b78b056dda4e.png~tplv-obj.image\",\"https://p11-webcast.douyinpic.com/img/webcast/7ef47758a435313180e6b78b056dda4e.png~tplv-obj.image\"],\"uri\":\"webcast/7ef47758a435313180e6b78b056dda4e.png\",\"avg_color\":\"_inline_CCB1A3\"}}"},{"group_id":7,"component_type":26,"op_type":0,"text":"连线","schema_url":"","show_type":3,"data_status":1,"extra":""},{"group_id":8,"component_type":28,"op_type":0,"text":"礼物","schema_url":"","icon":{"url_list":["https://p3-webcast.douyinpic.com/img/webcast/profit_gift_entrance_ab_v2.webp~tplv-resize:0:0.image","https://p11-webcast.douyinpic.com/img/webcast/profit_gift_entrance_ab_v2.webp~tplv-resize:0:0.image"],"uri":"webcast/profit_gift_entrance_ab_v2.webp","height":0,"width":0,"avg_color":"_inline_8F8F8F","image_type":0,"open_web_url":"","is_animated":false,"flex_setting_list":[],"text_setting_list":[]},"show_type":3,"data_status":1,"extra":""},{"group_id":12,"component_type":35,"op_type":0,"text":"清晰度","schema_url":"","show_type":2,"data_status":1,"extra":""}],"more_panel":[{"group_id":1,"component_type":20,"op_type":0,"text":"分享","schema_url":"","show_type":1,"data_status":1,"extra":"{\"ShareStory\":1}"},{"group_id":10,"component_type":31,"op_type":0,"text":"聊天频道","schema_url":"","show_type":1,"data_status":1,"extra":""}],"max_entrance_cnt":4,"landscape_up_right":[],"skin_resource":{},"max_entrance_cnt_landscape":4,"permutation":{"general":{"GroupPriority":[18,17,12,14,16,5,1,8,4,15,10,13,0,19,9,3,2,7,6],"ComponentSequence":[31,33,34,37,35,38,36,26,42,23,27,32,21,2,3,1,4,5,6,7,8,9,22,30,25,41,29,28,20,24]},"on_demand_component_list":[]},"extra_info":{"game_promotion_coexist":0}},"ecom_data":{"reds_show_infos":[],"instant_type":0},"room_cart":{"contain_cart":false,"total":0,"flash_total":0,"cart_icon":"","show_cart":0},"AnchorABMap":{"ab_admin_comment_on_wall":"0","ab_friend_chat":"4","admin_privilege_refine":"2","allow_shared_to_fans":"false","audience_linkmic_continue":"0","audio_double_enlarge_enable":"1","audio_room_subtitle_opt":"3","battle_match_rebuild_anchor":"2","big_party_enable_open_camera":"2","chat_intercommunicate_multi_anchor":"2","chat_intercommunicate_pk":"2","double_enlarge_enable":"1","ecom_room_disable_gift":"0","enable_enter_by_sharing":"0","enable_link_guest_enter":"0","enter_message_tip_relation":"0","enter_source_mark":"0","frequently_chat_ab_value":"2","friend_room_audio_tuning":"1","friend_room_support_ns_mode":"0","friend_share_video_feature_type":"3","game_link_entrance":"0","gift_hide_tip":"2","guest_battle_crown_upgrade":"0","guest_battle_expand":"0","guest_battle_score_expand":"2","guest_battle_upgrade":"2","interact_anchor_guide":"0","ktv_anchor_enable_add_all":"1","ktv_auto_mute_self":"0","ktv_challenge_minus_gift":"0","ktv_component_new_midi":"3","ktv_enable_avatar":"0","ktv_enable_open_camera":"0","ktv_fragment_song":"0","ktv_grab_guide_song":"0","ktv_guide_song_switch":"0","ktv_kick_when_linker_full":"0","ktv_mc_host_show_tag":"0","ktv_new_challenge":"1","ktv_room_atmosphere":"0","ktv_singing_hot_rank":"0","ktv_video_stream_optimize":"0","ktv_want_listen_enable":"2","linkmic_multi_chorus":"2","linkmic_order_sing_search_fingerprint":"1","linkmic_order_sing_upgrade":"1","linkmic_starwish":"1","live_anchor_enable_chorus":"false","live_anchor_enable_custom_position":"1","live_anchor_hit_new_audience_linkmic":"0","live_anchor_hit_position_opt":"2","live_anchor_hit_video_bid_paid":"0","live_anchor_hit_video_teamfight":"0","live_answer_on_wall":"0","live_audience_linkmic_pre_apply_v2":"1","live_dou_plus_enter":"0","live_ktv_enable_beat":"0","live_ktv_group":"0","live_ktv_show_singer_icon":"1","live_ktv_singing_challenge":"0","live_linkmic_battle_optimize":"1","live_linkmic_ktv_anchor_lyric_mode":"0","live_linkmic_order_sing_micro_opt":"3","live_linkmic_order_sing_v3":"2","live_pc_helper_new_layout":"1","live_room_manage_style":"0","live_team_fight_flexible":"1","live_video_enable_c_position":"2","live_video_enable_self_discipline":"2","live_video_host_identity_enable":"0","live_video_share":"0","lonely_room_enter_msg_unfold":"0","mark_user":"0","merge_ktv_mode_enable":"1","merge_ktv_optimize_enable":"2","opt_audience_linkmic":"3","opt_paid_link_feature_switch":"0","optran_paid_linkmic":"2","order_sing_mv":"1","play_mode_opt_24":"1","ps_use_new_panel":"0","radio_prepare_apply":"0","room_double_like":"0","self_discipline_v2":"1","self_discipline_v3":"1","social_share_video_adjust_volume":"0","support_multiple_add_price":"0","themed_competition_v2":"1","traffic_strategy":"0","video_equal_1v8fix_switch":"1","video_ktv_challenge":"0","video_talk_enable_avatar":"2"},"like_count":31483,"owner_user_id_str":"","paid_live_data":{"paid_type":0,"view_right":0,"duration":0,"delivery":0,"need_delivery_notice":false,"anchor_right":0,"pay_ab_type":0,"privilege_info":{},"privilege_info_map":{},"max_preview_duration":0},"basis":{"next_ping":300,"is_customize_audio_room":false,"need_request_luckybox":0,"secret_room":0,"foreign_user_room":0},"short_touch_area_config":{"elements":{"1":{"type":1,"priority":1},"2":{"type":2,"priority":1},"3":{"type":3,"priority":1},"4":{"type":4,"priority":3},"5":{"type":5,"priority":4},"6":{"type":6,"priority":3},"7":{"type":7,"priority":3},"8":{"type":8,"priority":3},"9":{"type":9,"priority":3},"10":{"type":10,"priority":3},"12":{"type":12,"priority":3},"22":{"type":22,"priority":1},"27":{"type":27,"priority":3},"30":{"type":30,"priority":2}},"forbidden_types_map":{},"temp_state_condition_map":{"1":{"type":{"strategy_type":1,"priority":30},"minimum_gap":900},"2":{"type":{"strategy_type":2,"priority":20},"minimum_gap":900},"3":{"type":{"strategy_type":3,"priority":10},"minimum_gap":900},"4":{"type":{"strategy_type":4,"priority":1},"minimum_gap":0},"5":{"type":{"strategy_type":5,"priority":5},"minimum_gap":0},"6":{"type":{"strategy_type":6,"priority":7},"minimum_gap":0},"7":{"type":{"strategy_type":7,"priority":6},"minimum_gap":0}},"temp_state_strategy":{"4":{"short_touch_type":4,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":10,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""},"3":{"type":{"strategy_type":3,"priority":10},"duration":10,"strategy_method":""},"6":{"type":{"strategy_type":6,"priority":7},"duration":10,"strategy_method":""},"7":{"type":{"strategy_type":7,"priority":6},"duration":10,"strategy_method":""}}},"7":{"short_touch_type":7,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":10,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""},"3":{"type":{"strategy_type":3,"priority":10},"duration":10,"strategy_method":""},"4":{"type":{"strategy_type":4,"priority":1},"duration":0,"strategy_method":""},"5":{"type":{"strategy_type":5,"priority":5},"duration":10,"strategy_method":""},"6":{"type":{"strategy_type":6,"priority":7},"duration":10,"strategy_method":""}}},"8":{"short_touch_type":8,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""}}},"97":{"short_touch_type":97,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""},"3":{"type":{"strategy_type":3,"priority":10},"duration":30,"strategy_method":""},"5":{"type":{"strategy_type":5,"priority":5},"duration":10,"strategy_method":"short_touch_tempstate_redpack_entry_type"},"6":{"type":{"strategy_type":6,"priority":7},"duration":10,"strategy_method":"short_touch_tempstate_redpack_match_amunt"},"7":{"type":{"strategy_type":7,"priority":6},"duration":10,"strategy_method":"short_touch_tempstate_redpack_user_wish_tobuy"}}},"136":{"short_touch_type":136,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""}}},"141":{"short_touch_type":141,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""},"3":{"type":{"strategy_type":3,"priority":10},"duration":10,"strategy_method":""}}},"149":{"short_touch_type":149,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""}}},"152":{"short_touch_type":152,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""}}},"153":{"short_touch_type":153,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":10,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""},"4":{"type":{"strategy_type":4,"priority":1},"duration":10,"strategy_method":""}}},"159":{"short_touch_type":159,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""}}},"161":{"short_touch_type":161,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""},"2":{"type":{"strategy_type":2,"priority":20},"duration":10,"strategy_method":""}}},"210":{"short_touch_type":210,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""}}},"306":{"short_touch_type":306,"strategy_map":{"3":{"type":{"strategy_type":3,"priority":10},"duration":30,"strategy_method":"test_temp_30"}}},"307":{"short_touch_type":307,"strategy_map":{"4":{"type":{"strategy_type":4,"priority":1},"duration":15,"strategy_method":"test_strategy_5"}}},"308":{"short_touch_type":308,"strategy_map":{"5":{"type":{"strategy_type":5,"priority":5},"duration":10,"strategy_method":"test_strategy_5"}}},"311":{"short_touch_type":311,"strategy_map":{"3":{"type":{"strategy_type":3,"priority":10},"duration":30,"strategy_method":""}}},"312":{"short_touch_type":312,"strategy_map":{"1":{"type":{"strategy_type":1,"priority":30},"duration":30,"strategy_method":""}}},"313":{"short_touch_type":313,"strategy_map":{"2":{"type":{"strategy_type":2,"priority":20},"duration":30,"strategy_method":"test_strategy_2"}}}},"strategy_feat_whitelist":["feat_coin_lottery_amount","feat_redpack_amount","live_short_touch_ecom_redpack_type","live_short_touch_ecom_redpack_sub_type","live_short_touch_ecom_redpack_total_amount","live_short_touch_ecom_redpack_total_stock","live_ecom_cart_click_twice","live_ecom_cart_stop_buy","live_watch_6_min"],"temp_state_global_condition":{"duration_gap":300,"allow_count":1,"ignore_strategy_types":[4]}},"req_user":{"user_share_room_score":0,"enter_user_device_type":0},"others":{"deco_detail":{},"more_panel_info":{"load_strategy":3},"appointment_info":{"appointment_id":0,"is_subscribe":false},"web_skin":{"enable_skin":false},"programme":{"enable_programme":false},"web_live_port_optimization":{"strategy_config":{"background":{"strategy_type":1,"use_config_duration":false,"pause_monitor_duration":"1800"},"detail":{"strategy_type":1,"use_config_duration":false,"pause_monitor_duration":"1800"},"tab":{"strategy_type":1,"use_config_duration":false,"pause_monitor_duration":"1800"}},"strategy_extra":""},"lvideo_item_id":0,"recognition_containers":{"recognition_candidates":[]},"anchor_together_live":{"is_together_live":0,"user_list":[],"title":"","schema_url":"","scene":1,"is_show":false},"mosaic_version":0,"metric_tracker_data_list":[]},"admin_user_open_ids":[],"admin_user_open_ids_str":[],"owner_open_id_str":""}],"enter_room_id":"7463311308587453219","extra":{"digg_color":"0","pay_scores":"0","is_official_channel":false,"signature":""},"user":{"id_str":"2427347483241595","sec_uid":"MS4wLjABAAAApyBAI83gxuGiVvHYpriZzguJK9iVJyI8kWwsn9ord763KHVnpXOqsMViJd6gBQ_w","nickname":"老魏评书","avatar_thumb":{"url_list":["https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_f9f03ada4141535cae95dbf004c007f8.jpeg?from=**********","https://p11.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_f9f03ada4141535cae95dbf004c007f8.jpeg?from=**********","https://p26.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_f9f03ada4141535cae95dbf004c007f8.jpeg?from=**********"]},"follow_info":{"follow_status":0,"follow_status_str":"0"},"foreign_user":0,"open_id_str":""},"qrcode_url":"https://p3-pc.douyinpic.com/img/aweme-qrcode/kbKKAB7463311514397574939~c5_720x720.webp?from=746027608","enter_mode":0,"room_status":0,"partition_road_map":{},"similar_rooms":[],"shark_decision_conf":"","web_stream_url":{"flv_pull_url":{},"default_resolution":"","hls_pull_url_map":{},"hls_pull_url":"","stream_orientation":0,"pull_datas":{}},"login_lead":{"is_login":false,"level":0,"items":{}}},"extra":{"now":1737690359544},"status_code":0}
                            var enter_room = JSON.parse(response.body);
                            // console.log(response.body)
                            var eventsData = initEvents(this._inline_context, 'douyin');
                            if (Number(enter_room.data.room_status) === 0) {
                                // 进入的直播间正在直播
                                var _event = packCtlEvent(0,  '正在直播', liveUrl)
                                _event.control_msg.room_id = enter_room.data.enter_room_id;
                                try {
                                    _event.control_msg.room_name = enter_room.data.user.nickname;
                                } catch (e){}

                                _event.control_msg.enter_live_info = {};
                                try {
                                    enter_room.data.data.forEach((_data) => {
                                        if (_data.id_str === enter_room.data.enter_room_id) {
                                            _event.control_msg.enter_live_info.room_tital = _data.title;
                                        }
                                    })
                                } catch (e) {}

                                try {
                                    _event.control_msg.enter_live_info.own_user_id_str = enter_room.data.user.id_str;
                                    _event.control_msg.enter_live_info.own_user_sec_uid = enter_room.data.user.sec_uid;
                                    _event.control_msg.enter_live_info.own_user_nickname = enter_room.data.user.nickname;
                                    if (enter_room.data.user.avatar_thumb && enter_room.data.user.avatar_thumb.url_list && enter_room.data.user.avatar_thumb.url_list.length > 0) {
                                        _event.control_msg.enter_live_info.own_user_head_url = enter_room.data.user.avatar_thumb.url_list[0];
                                    }
                                } catch (e) {}

                                // console.log(JSON.stringify(_event));
                                eventsData.events_data.push(_event);
                                eventCallback(eventsData);
                            } else if (Number(enter_room.data.room_status) === 2) {
                                // 进入未开播的直播间
                                var _event = packCtlEvent(1,  '未开播', liveUrl);
                                try {
                                    _event.control_msg.room_name = enter_room.data.user.nickname;
                                }catch (e) {
                                }
                                eventsData.events_data.push(_event);
                                eventCallback(eventsData);
                            }
                            // 进入直播间
                            // console.log('web/enter body'+response.body);
                        } else {
                            // live.douyin.com/1231231112
                            // 'room\\":{.*\\"id_str\\":\\"(\d+)\\".*,\\"status\\":(\d+).*"title\\":\\"([^"]*)\\"'
                            //room_status,room_title,
                        }
                    }).catch((error) => {
                        this._inline_self_debug(method+'Err['+error+']:'+requestIds[params.requestId]);
                    }).finally(() => {
                        delete requestIds[params.requestId];
                    });
                }
            } else if (method === atob('TmV0d29yay5yZXNwb25zZVJlY2VpdmVk'/* 'Network.responseReceived' */)) {
                // 1. 保存需要处理的 url和id,到 Network.loadingFinished 里去处理
                if (params.response.url.includes(atob('L3dlYmNhc3QvaW0vZmV0Y2gvPw=='/* '/webcast/im/fetch/?' */))
                    || params.response.url.includes(atob('L3dlYmNhc3Qvcm9vbS93ZWIvZW50ZXIvPw=='/* '/webcast/room/web/enter/?' */))) {
                    requestIds[params.requestId] = params.response.url;
                    // Start ==================== 校验逻辑
                    const im_now = params.response.headers[atob('aW0tbm93'/* 'im-now' */)];
                    if (im_now !== undefined) {
                        let serverTimestamp = parseInt(im_now);
                        // console.log('time:' + serverTimestamp);
                        // 时间如果超过 2026-01-20 15:09:23 则提示过期
                        if (1768892963000 < serverTimestamp) {
                            // console.log('过期');
                            mainWindow.webContents.debugger.detach();
                            // mainWindow.close();  // 关闭窗口
                        } else {
                            // console.log('ok');
                        }
                    } else {
                        // todo: 没有返回 im-now 的情况先忽略
                    }
                    // End ==================== 校验逻辑
                } else {
                    // console.log(params.response.url);
                }
            } else if (method === atob('TmV0d29yay53ZWJTb2NrZXRGcmFtZVJlY2VpdmVk'/* 'Network.webSocketFrameReceived' */)) {
                // console.log('webSocketFrameReceived:'+params.requestId);
                if (gWebcastObj.hasOwnProperty(params.requestId)) {
                    // 存在则解包

                    const PushFrameMsg = douyin.PushFrame.decode(Buffer.from(params.response.payloadData, 'base64'));
                    // console.log(PushFrameMsg)
                    if (LiveDouyin._inline_needUnzip(PushFrameMsg)) {
                        // 是否需要解包:
                        // console.log('_inline_needUnzip');
                        gunzip(PushFrameMsg.payload, (err, uncompressedData) => {
                            if (err) {
                                logCallback('解压缩失败:' + err);
                                return;
                            }
                            if (PushFrameMsg.payloadType === 'msg') {
                                try {
                                    this._inline_decodePayload(uncompressedData, liveUrl, eventCallback, logCallback);
                                } catch (e) {
                                    logCallback('decodePayload ws error:' + e.message);
                                }
                            } else if (PushFrameMsg.payloadType === 'hb') {
                                // ignore
                            } else {
                                // console.log('unknown type:'+PushFrameMsg.payloadType);
                            }
                        });
                    }
                } else {
                    logCallback('recive warn not found '+params.requestId);
                }

                // params.response.payload;
                // CAIYuEUgCCocCgtzZXJ2ZXJfdGltZRINMTczNjMyMTU5NTg3MioVCg1jb21wcmVzc190eXBlEgRnemlwOgJoYkIcH4sIAAAAAAAA/wAAAP//AQAA//8AAAAAAAAAAA==
            } else if (method === atob('TmV0d29yay53ZWJTb2NrZXRDcmVhdGVk'/* 'Network.webSocketCreated' */)) {
                if (params.url.startsWith(atob('d3NzOi8v'/* 'wss://' */)) && params.url.includes(atob('L3dlYmNhc3QvaW0vcHVzaC8='/* '/webcast/im/push/' */))) {
                    gWebcastObj[params.requestId] = params.url; // 提取 roomId
                }
                // console.log('webSocketCreated'+params.requestId+','+params.url);
            } else if (method === atob('TmV0d29yay53ZWJTb2NrZXRDbG9zZWQ='/* 'Network.webSocketClosed' */)) {
                // console.log('webSocketClosed:'+params.requestId);
                delete gWebcastObj[params.requestId];
            }
            // else if (method.includes('webSocket')) {
            //   console.log(method);
            // }
        })

        mainWindow.loadURL('about:blank').then(r => {
        })
        // 必须要有页面才会 enable 成功
        mainWindow.webContents.debugger.sendCommand(atob('TmV0d29yay5lbmFibGU='/* 'Network.enable' */)).then(()=>{
            this._inline_self_debug('enable ok')
            // this._inline_self_debug('to loadURL')
            mainWindow.loadURL(liveUrl).catch((err) => {
                logCallback('Failed to load URL:', err);
            }).finally(()=>{
                // this._inline_self_debug('loadURL finally')
            })
        });

        // this.socket.addEventListener("message", e)
        mainWindow.webContents.on('did-finish-load', () => {
            // console.log('Page finished loading');
            // 获取页面内容
            // <div class="default-page-title">暂时无法在该页面观看</div>
            mainWindow.webContents.executeJavaScript('document.documentElement.outerHTML').then((html) => {
                // <html><head><meta name="color-scheme" content="light dark"></head><body><pre style="word-wrap: break-word; white-space: pre-wrap;">404 Page not found</pre></body></html>
                if (html.includes('error-page') || html.includes('404 Page not found')) {
                    var eventsData = initEvents(this._inline_context, 'douyin');
                    eventsData.events_data.push(packCtlEvent(100,  '404 Page not found', liveUrl))
                    eventCallback(eventsData);
                } else if (html.includes("<div class=\"default-page-title\">暂时无法在该页面观看</div>")) {
                    var eventsData = initEvents(this._inline_context, 'douyin');
                    eventsData.events_data.push(packCtlEvent(100,  '暂时无法在该页面观看', liveUrl))
                    eventCallback(eventsData);
                }
            });

            mainWindow.webContents.executeJavaScript(`
     (function() {
    'use strict';
 
    let interval = 1 * 60 * 1000; // 每1分钟模拟一次鼠标移动
    let lastMove = Date.now();
 
    function simulateMouseMove() {
        let now = Date.now();
        if (now - lastMove > interval) {
            let event = new MouseEvent('mousemove', {
                'view': window,
                'bubbles': true,
                'cancelable': true,
                'clientX': Math.random() * window.innerWidth,
                'clientY': Math.random() * window.innerHeight
            });
            document.dispatchEvent(event);
            lastMove = now;
        }
    }
 
    setInterval(simulateMouseMove, 60 * 1000); // 每分钟检查一次是否需要模拟鼠标移动
})();
    `).catch((err) => {
                console.error('注入脚本失败:', err);
            });
        });

        // Open the DevTools.
        // mainWindow.webContents.openDevTools()
        // mainWindow.hide();
    }

    // tools
    static _inline_needUnzip(t) {
        for (const e of Object.values(t.headers)) if ("compress_type" === e.key && "gzip" === e.value) return !0;
        return !1
    }
    _inline_self_debug(msg) {
        // console.log(msg)
    }

    // 主要解包处理
    _inline_decodePayload(payload, live_url, eventCallback, logCallback) {
        if (typeof payload === 'string') {
            payload = Buffer.from(payload, 'base64');
        }
        var eventsData = initEvents(this._inline_context, 'douyin');
        if (this._inline_txmll) {
            eventsData.origin = payload.toString('base64');
        }
        const response = douyin.priv.Response.decode(payload)
        response.messages.forEach((msg) => {
            var _decodeMessage;
            try {
                if (msg.method === "WebcastLikeMessage") {
                    // 点赞
                    _decodeMessage = douyin.webcast.LikeMessage.decode(msg.payload);
                    // let formatMsg = parseFormatText(_decodeMessage.common.displayText);
                    // 记录点赞个数
                    // debug_log(_decodeMessage.common,  formatMsg + "x" + _decodeMessage.count);
                    eventsData.like_total = _decodeMessage.total;  // 外部
                    let _eventData = this._inline_packCommon(1, 'like_msg');
                    _eventData.like_msg = {};
                    _eventData.like_msg.user = this._inline_packUser(_decodeMessage.user);
                    _eventData.like_msg.like_count = _decodeMessage.count;
                    eventsData.events_data.push(_eventData);
                    // debug_log(_decodeMessage.common, JSON.stringify(_decodeMessage));
                    // logCallback(JSON.stringify(_decodeMessage))
                }
                // 评论数据
                else if (msg.method === "WebcastChatMessage") {
                    _decodeMessage = douyin.webcast.ChatMessage.decode(msg.payload);
                    // log(_decodeMessage.common,  _decodeMessage.user.nickname + ":" + _decodeMessage.content);
                    // _newMessage.common.msgId = _decodeMessage.common.msgId;
                    // _newMessage.eventTime = _decodeMessage.eventTime;
                    // _newMessage.publicAreaCommon = copyPublicAreaCommon(_decodeMessage.publicAreaCommon);
                    let _eventData = this._inline_packCommon(2, 'comment_msg');
                    _eventData.comment_msg = {};
                    _eventData.comment_msg.user = this._inline_packUser(_decodeMessage.user);
                    _eventData.comment_msg.content = _decodeMessage.content;
                    eventsData.events_data.push(_eventData);
                    // console.log(JSON.stringify(_decodeMessage));
                    // debug_log(_decodeMessage.common, JSON.stringify(_decodeMessage));
                } else if (msg.method === "WebcastGiftMessage") {
                    _decodeMessage = douyin.webcast.GiftMessage.decode(msg.payload);
                    // debug_log(_decodeMessage.common, JSON.stringify(_decodeMessage));
                    if (_decodeMessage.gift.combo === true && _decodeMessage.repeatEnd === 1) {
                        // 忽略最后一个汇总
                        return;
                    }
                    var str_groupId = String(_decodeMessage.groupId)+'_'+_decodeMessage.user.displayId;
                    var local_gift = this._inline_getGift(str_groupId);
                    var cur_count = _decodeMessage.comboCount*_decodeMessage.groupCount;
                    if (local_gift === null) {
                        // 初始化
                        this._inline_addOrUpdateGift(str_groupId, cur_count, _decodeMessage.sendTime);
                    } else {
                        if (_decodeMessage.sendTime < local_gift.sendTime) {
                            // 有可能时间顺序不一样，忽略旧的
                            return;
                        }
                        // 服务器发来的总数是对的，但是组数和个数不一定一样，所以需要记录上一次的总数。
                        cur_count = this._inline_decrementGift(str_groupId, cur_count, _decodeMessage.sendTime);
                    }
                    let _eventData = this._inline_packCommon(3, 'gift_msg');
                    _eventData.gift_msg = {};
                    _eventData.gift_msg.user = this._inline_packUser(_decodeMessage.user);
                    if (_decodeMessage.toUser) {
                        _eventData.gift_msg.to_user = this._inline_packUser(_decodeMessage.toUser);
                    }
                    _eventData.gift_msg.gift_id = _decodeMessage.giftId;
                    _eventData.gift_msg.gift_name = _decodeMessage.gift.name;
                    if (_decodeMessage.gift.image !== undefined && _decodeMessage.gift.image !== null) {
                        _eventData.gift_msg.gift_url = _decodeMessage.gift.image.urlList[0];
                    }
                    _eventData.gift_msg.gift_price = _decodeMessage.gift.diamondCount;
                    _eventData.gift_msg.gift_msg_key = str_groupId;
                    _eventData.gift_msg.batch_size = _decodeMessage.groupCount;
                    _eventData.gift_msg.count = cur_count;
                    _eventData.gift_msg.total_count = _decodeMessage.comboCount;
                    // // origin
                    // _eventData.gift_msg.origin = {}
                    // _eventData.gift_msg.origin.combo = _decodeMessage.gift.combo;// 是否连击
                    // _eventData.gift_msg.origin.comboCount = _decodeMessage.comboCount;// 连击次数
                    // _eventData.gift_msg.origin.groupCount = _decodeMessage.groupCount;// 一组礼物个数
                    // _eventData.gift_msg.origin.repeatCount = _decodeMessage.repeatCount;// 送出的礼物数量
                    // _eventData.gift_msg.origin.repeatEnd = _decodeMessage.repeatEnd;// 1 表示连击结束，如果为true,可以忽略这次的礼物数据。 （combo === true,这个值才有效）
                    // _eventData.gift_msg.origin.diamondCount = _decodeMessage.gift.diamondCount;
                    // _eventData.gift_msg.origin.giftName = _decodeMessage.gift.name;
                    eventsData.events_data.push(_eventData);
                    if (_decodeMessage.gift.combo) {
                        // 如果礼物有 combo，触发第一个礼物之后，无论后面有没有发送，一定会收到一个 repeatEnd=1 的消息，而且traceId和上一条消息是一样的
                        // 1. 有时候 comboCount 会跳过一条，比如从1跳到3：
                        // [2025-1-23 18:29:3:655][7463048413089631030][WebcastGiftMessage]gift:1,1,人气票,品容💙,1,1,af1b1862054b11b5847768184335a08e,0,true
                        // [2025-1-23 18:29:4:775][7463048413089631030][WebcastGiftMessage]gift:3,3,人气票,品容💙,1,3,677e2b7e6643f982d4ee4d2d4b72999e,0,true
                        // [2025-1-23 18:29:8:179][7463048413089631030][WebcastGiftMessage]gift:3,3,人气票,品容💙,1,3,677e2b7e6643f982d4ee4d2d4b72999e,1,true

                        // 2. 有些礼物 combo 是 false,但是他 comboCount 也会递增,由于没有repeatEnd，所以不知道何时结束.(如果这种礼物也像1一样会跳过一条，这种无法处理，只有记录缓存)
                        // [2025-1-23 18:33:28:51][7463048413089631030][WebcastGiftMessage]gift:1,1,爆竹响新春,品容💙,1,1,7fadc23db9d4b1c9b366bb748619fcf4,0,false
                        // [2025-1-23 18:33:28:51][7463048413089631030][WebcastGiftMessage]gift:2,2,爆竹响新春,品容💙,1,2,f1148d7e858e96085f0f81503c288eca,0,false
                        // [2025-1-23 18:33:29:166][7463048413089631030][WebcastGiftMessage]gift:3,3,爆竹响新春,品容💙,1,3,721153b5d6c398ad20ee2dcc75512b4e,0,false
                        // [2025-1-23 18:33:33:706][7463048413089631030][WebcastGiftMessage]gift:4,4,爆竹响新春,品容💙,1,4,127aabd2bed0624bf913f25da503eac0,0,false
                    }
                    // 礼物有连击，会多次显示，最后一次 repeatEnd = 1 会重复.
                    // debug_log(_decodeMessage.common, 'gift:'+_decodeMessage.totalCount + ','+_decodeMessage.repeatCount
                    //     + ',' + _decodeMessage.gift.name + ',' + _decodeMessage.user.nickname + ',' + _decodeMessage.groupCount + ',' + _decodeMessage.comboCount
                    //     +',' + _decodeMessage.traceId + ',' + _decodeMessage.repeatEnd + ',' + _decodeMessage.gift.combo + ',' + _decodeMessage.sendTime);
                } else if (msg.method === 'WebcastFansclubMessage') {
                    // 没有displayText
                    // [2025-1-17 15:0:22:32][7460736793282562857][WebcastFansclubMessage]{...}
                    // "恭喜 {0:user} 加入粉丝团成为第{1:string}名"{2:string}"
                    // _decodeMessage = douyin.webcast.FansclubMessage.decode(msg.payload);
                    // ...（省略部分注释，见原始大文件）
                } else if (msg.method === "WebcastSocialMessage") {
                    // 关注主播 type == 1
                    // 分享直播 type == 3
                    // ...（省略部分注释，见原始大文件）
                } else if (msg.method === "WebcastMemberMessage") {
                    _decodeMessage = douyin.webcast.MemberMessage.decode(msg.payload);
                    eventsData.watching_total = _decodeMessage.memberCount;
                    let _eventData = this._inline_packCommon(4, 'member_msg');
                    _eventData.member_msg = {};
                    _eventData.member_msg.user = this._inline_packUser(_decodeMessage.user);
                    eventsData.events_data.push(_eventData);
                } else if (msg.method === 'WebcastControlMessage') {
                    _decodeMessage = douyin.webcast.ControlMessage.decode(msg.payload);
                    this._inline_self_debug(JSON.stringify(_decodeMessage));
                    if (Number(_decodeMessage.action) === 3) {  // 直播结束
                        let _eventData = this._inline_packCommon(5, 'control_msg');
                        _eventData.control_msg = {};
                        _eventData.control_msg.action = 1;
                        _eventData.control_msg.action_msg = _decodeMessage.tips;
                        _eventData.control_msg.live_url = live_url;
                        eventsData.events_data.push(_eventData);
                    }
                } else if (msg.method === 'WebcastNotifyEffectMessage') {
                    // 升级
                    // 恭喜RupRupRup刚刚升级至Lv.26
                    // {"common":{"method":"WebcastNotifyEffectMessage","msgId":"7519443080039854887","roomId":"7519418049368296192","isShowMsg":true},"background":{"backgroundImage":{"urlList":["https://p11-webcast.douyinpic.com/img/webcast/visual_degrade_3x_new3.png~tplv-obj.image","https://p3-webcast.douyinpic.com/img/webcast/visual_degrade_3x_new3.png~tplv-obj.image"],"uri":"webcast/visual_degrade_3x_new3.png","avgColor":"_inline_FFF8EB","flexSettingList":{"settingList":["0","0","42","42"]}}},"dynamicConfig":{"stayTime":2000,"maxStayTime":5001,"displayEffectType":2},"textV2":{"displayItems":[{"displayItemType":2,"textItem":{"text":{"key":"privilege_grade_level_up","defaultPattern":"{0:user} 升级至Lv.{1:string}","defaultFormat":{"color":"_inline_ffffff","weight":400,"useRemoteClor":true},"pieces":[{"type":11,"format":{"color":"_inline_ffffff","weight":400,"useRemoteClor":true},"userValue":{"user":{"id":"2823979785520535","shortId":"69339825904","nickname":"RupRupRup","gender":2,"avatarThumb":{"urlList":["https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_fc79b50fd51bb8ca259d27456713d560.jpeg?from=**********","https://p11.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_fc79b50fd51bb8ca259d27456713d560.jpeg?from=**********","https://p26.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_fc79b50fd51bb8ca259d27456713d560.jpeg?from=**********"],"uri":"100x100/aweme-avatar/tos-cn-avt-0015_fc79b50fd51bb8ca259d27456713d560"},"badgeImageList":[{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_26.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_26.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_26.png","height":"16","width":"32","imageType":1,"content":{"level":"26","alternativeText":"荣誉等级26级勋章"}}],"followInfo":{"followingCount":"29","followerCount":"24","followStatus":"1","followerCountStr":"24","followingCountStr":"29"},"userAttr":{},"displayId":"69339825904","secUid":"MS4wLjABAAAAtxwVB3mtQDkNeYR4r83N40UaXnsyz6KQr8ktfI9V__WCuS0LhiYFBwlRsrfSRzJX","authorizationInfo":3,"badgeImageListV2":[{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_26.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_26.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_26.png","height":"16","width":"32","imageType":1,"content":{"level":"26","alternativeText":"荣誉等级26级勋章"}}],"mysteryMan":1,"webcastUid":"MS4wLjM3F9kee9PitJBEgHNhkPhJ7lT5notbSVrK73ei_zqqHxdgqvzR0y6KzTWpL-Y5dKQ"}}},{"type":1,"format":{"color":"_inline_ffffff","weight":400,"useRemoteClor":true},"stringValue":"26"}]}}}],"schemaInfo":{"schemaUrl":"sslocal://webcast_profile?user_id=2823979785520535&type=half&source=19&click_user_position=uplevel_message"}},"sceneConfig":{"scene":"privilege_grade_level_up_new_high_level","priority":"50000","aggregateNum":"1","maxWaitTime":"300"},"buriedPoint":{"user_id":"2823979785520535","grade_level":"26","is_uplevel":"1"}}
                    // _decodeMessage = douyin.webcast.NotifyEffectMessage.decode(msg.payload);
                    // console.log( JSON.stringify(_decodeMessage));
                } else {
                    // console.log('msg.method: '+msg.method);
                }

                // 其它类型消息的注释见back.txt
                // ...
            } catch (e) {
                logCallback(_decodeMessage && _decodeMessage.common ? _decodeMessage.common.method : '' + ' message error:' + e.message + '\n' + JSON.stringify(_decodeMessage));
            }
        })
        if (eventsData.events_data.length > 0) {
            eventCallback(eventsData);
        }
    }

    _inline_packUser(ori_user) {
        var _user = {
            user_name: ori_user.displayId,
            nick_name: ori_user.nickname
        };
        if (ori_user.avatarThumb && ori_user.avatarThumb.urlList && ori_user.avatarThumb.urlList.length > 0) {
            _user.head_url = ori_user.avatarThumb.urlList[0];
        }
        if (ori_user.payGrade && ori_user.payGrade.level !== 0) {
            _user.level = ori_user.payGrade.level;
        }
        if (ori_user.fansClub && ori_user.fansClub.data && ori_user.fansClub.data.level !== 0) {
            _user.fansclub = {};
            _user.fansclub.level = ori_user.fansClub.data.level;
            if (ori_user.fansClub.data.clubName) {
                _user.fansclub.name = ori_user.fansClub.data.clubName;
            }
            if (ori_user.fansClub.data.userFansClubStatus) {
                if (Number(ori_user.fansClub.data.userFansClubStatus) === 1) {
                    _user.fansclub.status = true;
                } else {
                    _user.fansclub.status = false;
                }
            }
        }
        return _user;
    }

    _inline_packCommon(msg_type, msg_type_str) {
        var _eventData = {};
        _eventData.msg_type = msg_type;
        _eventData.msg_type_str = msg_type_str;
        return _eventData;
    }

    // 测试
    testParseRaw(payload, live_url, eventCallback, logCallback) {
        if (this._inline_test_hex && payload !== undefined) {
            this._inline_decodePayload(payload, live_url, eventCallback, logCallback);
        }
    }
}

/// @secureEnd

module.exports = LiveDouyin;
