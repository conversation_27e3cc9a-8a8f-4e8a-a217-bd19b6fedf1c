const {packCtlEvent, initEvents} = require("./common");

/// @secureBegin

class LiveWeixin {
    constructor(context, test=false) {
        this._inline_context = context;
        this._inline_gifts = new Map(); // 存储礼物数据的 Map
        this._inline_TIMEOUT_SECONDS = 15 * 1000; // 15秒（以毫秒为单位）
        this._inline_txmll = false;
        this._inline_log_finder_id = '';
        this._inline_test_hex = test;
    }

    // 添加或更新礼物数据
    _inline_addOrUpdateGift(combo_id, giftCount) {
        const now = Date.now();
        this._inline_gifts.set(combo_id, {
            giftCount,
            lastUpdated: now,
            timeout: setTimeout(() => {
                this._inline_gifts.delete(combo_id); // 30秒后删除未更新的数据
            }, this._inline_TIMEOUT_SECONDS)
        });
        return combo_id;
    }

    // 增加礼物个数
    _inline_incrementGift(combo_id, giftCount) {
        if (!this._inline_gifts.has(combo_id)) {
            return null; // 如果 ID 不存在，返回 null
        }
        const gift = this._inline_gifts.get(combo_id);
        clearTimeout(gift.timeout); // 清除旧的定时器
        gift.giftCount += giftCount;
        gift.lastUpdated = Date.now();
        gift.timeout = setTimeout(() => {
            this._inline_gifts.delete(combo_id); // 30秒后删除未更新的数据
        }, this._inline_TIMEOUT_SECONDS);
        this._inline_gifts.set(combo_id, gift);
        return gift;
    }

    // 获取礼物数据
    _inline_getGift(combo_id) {
        return this._inline_gifts.get(combo_id) || null;
    }

    /**
     * 释放内部数据
     * */
    release() {
        this._inline_gifts.forEach(_gift => {
            clearTimeout(_gift.timeout);
            _gift.timeout = null;
        })
        this._inline_gifts.clear();
    }

    /**
     * 一次只能启动一个
     * */
    startMonitorRoom(mainWindow, empty_url, eventCallback, logCallback) {
        try {
            mainWindow.webContents.debugger.attach()
        } catch (err) {
            this._inline_self_debug('Debugger attach failed : ' + err)
        }

        try {
            this._inline_txmll = process.env.x234534trsegfsdc || false;
        } catch (err) {
            this._inline_txmll = false;
        }
        mainWindow.webContents.debugger.on(atob('ZGV0YWNo'/* 'detach' */), (event, reason) => {
            // this._inline_self_debug('Debugger detached due to : ' + reason)
        })

        var getResponseDecodeBody = async (requestId) => {
            const res = await mainWindow.webContents.debugger.sendCommand(atob('RmV0Y2guZ2V0UmVzcG9uc2VCb2R5'/* 'Fetch.getResponseBody' */), {requestId: requestId})
            // return res.body
            return res.base64Encoded ? Buffer.from(res.body, 'base64') : res.body
        }
        var parseUrl = async (params) => {
            try {
                var resBody = await getResponseDecodeBody(params.requestId);
                if (resBody === undefined || resBody.length <= 0) {
                    return
                }
                // 只需要处理 msg
                if (params.request.url.includes(atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2xpdmUvbXNn'/* 'mmfinderassistant-bin/live/msg' */))) {
                    // cgi-bin/mmfinderassistant-bin/live/get_live_info
                    // cgi-bin/mmfinderassistant-bin/live/msg
                    // cgi-bin/mmfinderassistant-bin/live/check_live_status

                    // reward_gains // 礼物整体数据
                    // online_member    // 在线人数 live_online_count_str
                    // gift_enum_list   // 礼物列表，但是返回 {"errCode":300020,"errMsg":"request failed"}
                    // join_live        // 不知道什么用
                    //
                    // write_msg_debug('modules/weixin/samples/weixin-msg.txt', params.request.url+'\t'+resBody+'\n')
                    let resJson = JSON.parse(resBody);
                    if (resJson.errCode !== 0) {
                        // 错误
                        logCallback(atob('bXNnIGVyckNvZGU6'/* 'msg errCode:' */) + resJson.errCode)
                        return;
                    }
                    if (resJson.data !== undefined && resJson.data.respJsonStr !== undefined) {
                        // write_msg_debug('modules/weixin/samples/weixin-msg.txt', '\t'+resJson.data.respJsonStr+'\n');
                        this._inline_decodeDataFromResponse(eventCallback, logCallback,
                            JSON.parse(resJson.data.respJsonStr),
                            params.request.url);
                    }
                    // this._inline_self_debug('self_d:'+params.request.url);
                } else if (params.request.url.includes(atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2xpdmUvY2hlY2tfbGl2ZV9zdGF0dXM='/* 'mmfinderassistant-bin/live/check_live_status' */))) {
                    // 开播状态
                    // write_msg_debug('modules/weixin/samples/check_live_status.txt', params.request.url+'\t'+resBody+'\n')
                    // 未开播
                    // {"errCode":0,"errMsg":"request successful","data":{"coverImgList":[],"status":0,"liveObjectId":"14673298438405163535","wxNickname":"perftest13903057396","optHeadImg":"","switchStatus":{"comment":1,"reward":1,"likelive":1,"mic":0,"boxchat":1,"askProductQuestion":1,"showMicSwitch":false,"showBoxchatSwitch":false,"showAskProductQuestionSwitch":true},"rewardAvailable":1,"liveId":"2042791728073674922","operatorRole":1,"liveLogoAvailable":0,"functionGuideEnabledBitset":9,"isLiveRequiredDescription":true}}
                    let resJson = JSON.parse(resBody);
                    if (resJson.errCode !== 0) {
                        // 错误
                        logCallback(atob('Y2xzIGVyckNvZGU6'/* 'cls errCode:' */) + resJson.errCode)
                        return;
                    }
                    if (resJson.data !== undefined) {
                        // var weixinCheckLiveStatus = {
                        //     liveId: resJson.data.liveId,
                        //     liveObjectId: resJson.data.liveObjectId,
                        //     wxNickname: resJson.data.wxNickname,
                        //     wechatUin: params.request.headers['X-WECHAT-UIN'],
                        //     status: resJson.data.status
                        // };
                        try {
                            // 未开播 {"type":1,"check_live_status":{"liveId":"2042791728073674922","liveObjectId":"14673298438405163535","wxNickname":"perftest13903057396","wechatUin":"533608124","status":0,"log_finder_id":"v2_060000231003b20faec8c5eb8110c6d2cb05ec3cb077ce5e13651ed8d4b49c1bb9e4eb8842b7@finder"}}
                            // 开播中 {"type":1,"check_live_status":{"liveId":"2042793096621379766","liveObjectId":"14676101225087961630","wxNickname":"perftest13903057396","wechatUin":"533608124","status":1,"log_finder_id":"v2_060000231003b20faec8c5eb8110c6d2cb05ec3cb077ce5e13651ed8d4b49c1bb9e4eb8842b7@finder"}}
                            // payload: {"getLiveStreamContext":{"useNewLogic":true,"scene":3,"protocolType":0,"videoCodecType":1,"rateType":3},"timestamp":"1749526115867","_log_finder_uin":"","_log_finder_id":"v2_060000231003b20faec8c5eb8110c6d2cb05ec3cb077ce5e13651ed8d4b49c1bb9e4eb8842b7@finder","rawKeyBuff":null,"pluginSessionId":null,"scene":7,"reqScene":7}
                            if (params.request.postData !== undefined) {
                                var postDataJson = JSON.parse(params.request.postData);
                                // TODO: 保存到全局 postDataJson._log_finder_id
                                this._inline_log_finder_id = postDataJson._log_finder_id;
                                // weixinCheckLiveStatus.log_finder_id = postDataJson._log_finder_id;
                            }
                        } catch (e) {// SyntaxError
                        }
                        if (resJson.data.status === 0) {// 0,未开播 1, 开播中
                            var eventsData = initEvents(this._inline_context, 'weixin');
                            var _event = packCtlEvent(1, '未开播', '');
                            _event.control_msg.room_name = resJson.data.wxNickname;
                            eventsData.events_data.push(_event);
                            eventCallback(eventsData);
                        } else if (resJson.data.status === 1) {
                            var eventsData = initEvents(this._inline_context, 'weixin');
                            var _event = packCtlEvent(0, '正在直播', '');
                            _event.control_msg.room_name = resJson.data.wxNickname;

                            _event.control_msg.enter_live_info = {};
                            _event.control_msg.room_id = resJson.data.liveId;

                            _event.control_msg.enter_live_info.room_tital = resJson.data.description;
                            if (this._inline_log_finder_id !== '') {
                                _event.control_msg.enter_live_info.own_user_sec_uid = this._inline_log_finder_id;
                            }
                            _event.control_msg.enter_live_info.own_user_nickname = resJson.data.wxNickname;
                            _event.control_msg.enter_live_info.own_user_head_url = resJson.data.coverImg.imgUrl;

                            eventsData.events_data.push(_event);
                            eventCallback(eventsData);
                        }
                    }
                } else if (params.request.url.includes(atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2F1dGgvYXV0aF9kYXRh'/* 'mmfinderassistant-bin/auth/auth_data' */))) {
                    // {"errCode":0,"errMsg":"request successful","data":{"userAttr":{"nickname":"perftest13903057396","username":"AAQZNIEEAAABAAAAAABtXF/Vo3czGo1MCtRHaCAAAADUIFil2tHAi3Ht9daYi9FbIzdpXWfj430M3ReGqf41+5DKjsNqD3kk8qwV+OHF7w09Bs3VavPvp6vQf6/khh5Q6UxRRcDrAtkNR2CAQJc=","encryptedUsername":"per****t13903057396","encryptedHeadImage":"","city":"","sex":0,"country":"","province":"","spamflag":0,"spamflag2":0},"finderUser":{"finderUsername":"v2_060000231003b20faec8c5eb8110c6d2cb05ec3cb077ce5e13651ed8d4b49c1bb9e4eb8842b7@finder","nickname":"来坐我这桌","headImgUrl":"https://wx.qlogo.cn/finderhead/HRFxUW2CSP8msGPSSFs0QHrLicDqMIqJ9cv984xV6ZSQ/0","coverImgUrl":"","spamFlag":0,"acctType":1,"authIconType":0,"adminNickname":"perftest13903057396","feedsCount":1,"fansCount":1,"categoryFlag":"0","uniqId":"sphUYexIInFXxFn","liveStatus":1,"anchorStatusFlag":"33556608","isMasterFinder":true},"datacenterEntarnce":1,"envInfo":{"cdnHostList":["finderassistancea.video.qq.com","finderassistanceb.video.qq.com","finderassistancec.video.qq.com","finderassistanced.video.qq.com","finderassistancee.video.qq.com"],"spareCdnHostList":["snsaispuploada.video.qq.com","snsaispuploadb.video.qq.com","snsaispuploadc.video.qq.com","snsaispuploadd.video.qq.com","snsaispuploade.video.qq.com"],"invalidVideoList":["av1"],"productEnv":0,"cdnHost":"finderassistancea.video.qq.com","uploadVersion":1},"livesvrEnter":0,"switchInfo":{"commentSelection":0,"asyncClipPostSwitch":1,"originalsoundSwitch":0,"personalmsgFlag":1,"selectionSwitch":1,"editCommentAuthSwitch":1,"userAttrReplayPrivilege":1,"canCreateReplayTransition":1,"enableH265Upload":1,"enableAllowAstraThumbCover":1,"enablePost20gVideo":0},"entranceInfo":{"commentManage":1,"pullstreamliveManage":0,"s1sFamousEntrance":1,"authEntrance":1,"adEntrance":0,"liveShopEntrance":1,"livePurchaseEntrance":0,"liveIncomeEntrance":0,"liveEcdataEntrance":0,"eventManageEntrance":1,"commentSelectionEntrance":1,"liveroomManageEntrance":1,"liveNoticeManageEntrance":1,"collectionEntrance":1,"originalEntranceInfo":{"contactAdditionalFlag":0},"personalColumnManageEntrance":1,"shortTitleEntrance":1,"collectionEntranceInfo":{"audioCollectionEntranceInfo":1,"updateNameMaxCount":5},"musicEntranceInfo":{"musicManagerEntrance":1,"takedownSongButtonEntrance":1,"takedownAlbumButtonEntrance":1,"bindButtonEntrance":1},"replayEntrance":1,"promotionEntrance":1,"liveleadsEntrance":1,"mpUrlPostEntrance":1,"memberEntranceInfo":{"memberManagerEntrance":0},"openMenu":1,"tencentVideoPostEntrance":0,"liveReplayTransferFeedEntrance":1,"audioEntranceInfo":{"audioManagerEntrance":1},"thirdpartyPushStreamEntranceInfo":0,"openUpdateWwkf":0,"courseEntranceInfo":{"courseManagerEntrance":0,"dramaManagerEntrance":0},"fanClubEntranceInfo":{"fanClubEntrance":0},"emotionUrlPostEntrance":0,"coursePostEntrance":0,"dramaPostEntrance":0,"newlifeEntranceInfo":{"newlifeEntrance":1},"registrationRecordEntrance":0},"authInfo":{"authIconType":0,"authVerifyIdentity":0,"currentYearAuthTimes":0,"simpleAuthStatus":0,"authAnnualReview":{"status":0},"authIconInfo":{"authenticationGrey":"http://dldir1.qq.com/weixin/checkresupdate/authentication_grey_d07164856ca34c82b660c2d26e02789b.svg","authenticationOutline":"http://dldir1.qq.com/weixin/checkresupdate/authentication_outline_f7b553a310eb4e58bdbe915505a70a3e.svg","authenticationYellow":"http://dldir1.qq.com/weixin/checkresupdate/authentication_yellow_b2aab444b7084a11989fbd7840a45196.svg","authenticationBlue":"http://dldir1.qq.com/weixin/checkresupdate/authentication_blue_56334b1fc48446e2bd57dd3c9172f3d1.svg","authenticationShow":""}},"signature":"","proxyUid":"3452467422895011203","redDotInfo":{"redDotList":[],"totalCount":0},"txvideoOpenId":"o-5UCAEHswFdp_xJleDvgTfvDo78","originInfo":{"items":[{"supportMediaTypes":[2,4],"originalType":24,"displayName":"生活"},{"supportMediaTypes":[2,4],"originalType":2,"displayName":"萌娃"},{"supportMediaTypes":[4],"originalType":1,"displayName":"音乐","mediaLimit":{"minDuration":30}},{"supportMediaTypes":[2,4],"originalType":3,"displayName":"知识"},{"supportMediaTypes":[2,4],"originalType":4,"displayName":"情感"},{"supportMediaTypes":[2,4],"originalType":5,"displayName":"旅行风景"},{"supportMediaTypes":[2,4],"originalType":6,"displayName":"时尚"},{"supportMediaTypes":[2,4],"originalType":7,"displayName":"美食"},{"supportMediaTypes":[2,4],"originalType":8,"displayName":"生活技巧"},{"supportMediaTypes":[2,4],"originalType":9,"displayName":"舞蹈"},{"supportMediaTypes":[2,4],"originalType":10,"displayName":"影视综艺"},{"supportMediaTypes":[2,4],"originalType":11,"displayName":"运动"},{"supportMediaTypes":[2,4],"originalType":12,"displayName":"搞笑"},{"supportMediaTypes":[2,4],"originalType":13,"displayName":"明星名人"},{"supportMediaTypes":[2,4],"originalType":14,"displayName":"新闻资讯"},{"supportMediaTypes":[2,4],"originalType":15,"displayName":"游戏"},{"supportMediaTypes":[2,4],"originalType":16,"displayName":"车"},{"supportMediaTypes":[2,4],"originalType":17,"displayName":"二次元"},{"supportMediaTypes":[2,4],"originalType":18,"displayName":"才艺"},{"supportMediaTypes":[2,4],"originalType":19,"displayName":"萌宠"},{"supportMediaTypes":[2,4],"originalType":20,"displayName":"工业/机械/施工"},{"supportMediaTypes":[2,4],"originalType":21,"displayName":"动物"},{"supportMediaTypes":[2,4],"originalType":22,"displayName":"育儿"},{"supportMediaTypes":[2,4],"originalType":23,"displayName":"科技"}]}}}
                    // write_msg_debug('modules/weixin/samples/weixin-auth.txt', params.request.url+'\t'+resBody+'\n')
                } else if (params.request.url.includes(atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2xpdmUvb25saW5lX21lbWJlcg=='/* 'mmfinderassistant-bin/live/online_member' */))) {
                    // write_msg_debug('modules/weixin/samples/online_member.txt', params.request.url+'\t'+resBody+'\n')
                    let resJson = JSON.parse(resBody);
                    if (resJson.errCode !== 0) {
                        // 错误
                        logCallback(atob('b20gZXJyQ29kZTo='/* 'om errCode:' */) + resJson.errCode)
                        return;
                    }
                    if (resJson.data !== undefined && resJson.data.respJsonStr !== undefined) {
                        // write_msg_debug('modules/weixin/samples/online_member.txt', '\t'+resJson.data.respJsonStr+'\n');
                        this._inline_packageOnlineMember(eventCallback, logCallback, JSON.parse(resJson.data.respJsonStr));
                    }
                } else if (params.request.url.includes(atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2xpdmUvZ2lmdF9lbnVtX2xpc3Q='/* 'mmfinderassistant-bin/live/gift_enum_list' */))) {
                    // write_msg_debug('modules/weixin/samples/gift_enum_list.txt', params.request.url+'\t'+resBody+'\n')
                    // let resJson = JSON.parse(resBody);
                    // if (resJson.errCode !== 0) {
                    //     // 错误
                    //     logCallback('gel errCode:'+resJson.errCode)
                    //     return;
                    // }
                }
                // console.log(params.request.url);
            } catch (e) {
                this._inline_self_debug(method + 'Err[' + e + ']' + params.requestId + ':' + params.request.url);
            }

            // Start ==================== 校验逻辑
            if (Array.isArray(params.responseHeaders)) {
                for (let i = 0; i < params.responseHeaders.length; i++) {
                    if (params.responseHeaders[i].name.toLowerCase() === atob('ZGF0ZQ=='/* 'date' */)) {
                        const date = params.responseHeaders[i].value;
                        if (date !== undefined) {
                            const serverTimestamp = Date.parse(date);
                            // console.log('time:' + serverTimestamp);
                            // 时间如果超过 2026-01-20 15:09:23 则提示过期
                            if (1768892963000 < serverTimestamp) {
                                // console.log('过期');
                                mainWindow.webContents.debugger.detach();
                                // mainWindow.close();  // 关闭窗口
                            } else {
                                // console.log('ok');
                            }
                        } else {
                            // todo: 没有返回 im-now 的情况先忽略
                        }
                        break
                    }
                }
            } else {
                // 突然断开代理可能会没有返回数据
            }
            // End ==================== 校验逻辑
        }

        mainWindow.webContents.debugger.on(atob('bWVzc2FnZQ=='/* 'message' */), async (event, method, params) => {
            // this._inline_self_debug('info:' + params.requestId+method)
            // this._inline_self_debug('info:'+method + ':' + JSON.stringify(params))
            if (method === atob('RmV0Y2gucmVxdWVzdFBhdXNlZA=='/* 'Fetch.requestPaused' */)) {
                parseUrl(params);

                mainWindow.webContents.debugger.sendCommand(atob('RmV0Y2guY29udGludWVSZXF1ZXN0'/* 'Fetch.continueRequest' */), {requestId: params.requestId})
            }
        })

        let _str_response = atob('UmVzcG9uc2U='/* 'Response' */)
        mainWindow.webContents.debugger.sendCommand(atob('RmV0Y2guZW5hYmxl'/* 'Fetch.enable' */), {
            patterns: [
                {
                    urlPattern: '*'+atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2xpdmUvbXNn'/* 'mmfinderassistant-bin/live/msg' */)+'*',
                    requestStage: _str_response
                },
                {
                    urlPattern: '*'+atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2xpdmUvY2hlY2tfbGl2ZV9zdGF0dXM='/* 'mmfinderassistant-bin/live/check_live_status' */)+'*',
                    requestStage: _str_response
                },
                {
                    urlPattern: '*'+atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2F1dGgvYXV0aF9kYXRh'/* 'mmfinderassistant-bin/auth/auth_data' */)+'*',
                    requestStage: _str_response
                },
                {
                    urlPattern: '*'+atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2xpdmUvb25saW5lX21lbWJlcg=='/* 'mmfinderassistant-bin/live/online_member' */)+'*',
                    requestStage: _str_response
                },
                {
                    urlPattern: '*'+atob('bW1maW5kZXJhc3Npc3RhbnQtYmluL2xpdmUvZ2lmdF9lbnVtX2xpc3Q='/* 'mmfinderassistant-bin/live/gift_enum_list' */)+'*',
                    requestStage: _str_response
                },
                //
            ]
        })

        mainWindow.loadURL('about:blank').then(r => {
        })
        // 必须要有页面才会 enable 成功
        mainWindow.webContents.debugger.sendCommand(atob('TmV0d29yay5lbmFibGU='/* 'Network.enable' */)).then(()=>{
            this._inline_self_debug('enable ok')
            // this._inline_self_debug('to loadURL')
            // https://channels.weixin.qq.com/login.html
            // https://channels.weixin.qq.com/platform/live/liveBuild
            mainWindow.loadURL('https://channels.weixin.qq.com/platform/live/liveBuild');
        });
    }

    _inline_self_debug() {
        
    }
    //
    // function commonMsg(msg) {
    //     return '['+msg.seq +']'+ msg.nickname;
    // }
    // //t("div", {staticClass: "name"}, [e._v(e._s(s.nickname))]), 3 === s.liveIdentity ? t("svg-icon", {
    // //                                     staticClass: "logo",
    // //                                     attrs: {name: "logo"}
    // //                                 }) : e._e(), t("FinderLiveIntimacyBadge", {
    // //                                     staticClass: "level",
    // //                                     attrs: {level: s.level, isSuper: !0, name: e.clubName}
    // //                                 })], 1)
    // function commonAppMsgBase(msg) {
    //     // msg.from_user_contact.contact.username
    //     // msg.from_user_contact.contact.head_url
    //     // msg.from_user_contact.contact.nickname
    //     // msg.from_user_contact.contact.ext_info   // 可能为空 country, province, city, sex
    //     //  sex 1: boy, 2: girl
    //     // msg.from_user_contact.live_identity      // 不知道什么含义，有1， 有3。 3会显示粉丝等级和粉丝团名字，其他显示logo, channel?
    //     // msg.from_user_contact.live_contact_ext_info  // 不知道含义
    //     // msg.from_user_contact.badge_infos    // 消费等级之类的
    //     return '['+msg.seq+','+msg.msg_type +']';
    // }
    //
    // function userContactMsg(user_contact) {
    //     return user_contact.display_nickname;//+','+user_contact.live_identity+','+badgeInfos(user_contact.badge_infos);
    // }
    //
    // function commonAppMsg(msg) {
    //     // msg.from_user_contact.contact.username
    //     // msg.from_user_contact.contact.head_url
    //     // msg.from_user_contact.contact.nickname
    //     // msg.from_user_contact.contact.ext_info   // 可能为空 country, province, city, sex
    //     //  sex 1: boy, 2: girl
    //     // msg.from_user_contact.live_identity      // 不知道什么含义，有1， 有3。 3会显示粉丝等级和粉丝团名字，其他显示logo, channel?
    //     // msg.from_user_contact.live_contact_ext_info  // 不知道含义
    //     // msg.from_user_contact.badge_infos    // 消费等级之类的
    //     // 消息 id    msg.client_msg_id
    //     return commonAppMsgBase(msg)+ userContactMsg(msg.from_user_contact);
    // }
    //

    // function self_log(msg, message, ...optionalParams) {
    //
    //     // if (msg.msg_type === 20013 || msg.msg_type === 20009 || msg.msg_type === 20050){
    //     //     console.log(message, ...optionalParams);
    //     //     // console.log(JSON.stringify(msg));
    //     // }
    //     // if (msg.msg_type < 20091 || msg.msg_type >20096) {
    //     //     return// 20093
    //     // }
    //     console.log(message, ...optionalParams);
    //
    //     // console.log(JSON.stringify(msg));
    // }

    _inline_b64Payload(payload) {
        if (payload && payload.length > 0) {
            const decode_payload = Buffer.from(payload, 'base64').toString('utf8');
            return JSON.parse(decode_payload);
        }
        return "";
    }

    // const BadgeTypeEnum = {
    //     Unknown: 0,
    //     Reward: 1,
    //     GlobalReward: 2, // 消费等级，如：{"badge_type":2,"badge_level":22}
    //     GameRank: 3,
    //     Intimacy: 4, // 亲密关系，如：{"badge_type":4,"badge_img_url":"https://dldir1v6.qq.com/weixin/checkresupdate/one_f6c74c5b11b84353b248dc952644c58e.png","badge_level":1,"badge_name":"阶猛兽"}
    //     Role: 5, // 主播，如：{"badge_type":5,"badge_level":5,"badge_name":"主播"}
    //     GameEsport: 6,
    //     Concert: 7,
    //     Invisiable: 8,
    //     Mysterious: 10, // 神秘观众，如：{"badge_type":10,"badge_name":"神秘观众 113"}
    //     GloryList: 11,
    //     SuperFans: 12,
    //     PayCnt: 13,
    //     OrdinaryBuyer: 14,
    //     Follower: 15, // 粉丝，如：{"badge_type":15,"badge_name":"粉丝"}
    //     FrequentWatch: 16,
    //     SeniorBuyer: 17,
    //     UNRECOGNIZED: -1
    // };
    _inline_packUser(contact) {
        var _user = {
            user_name: contact.contact.username,
            nick_name: contact.contact.nickname,
            head_url: contact.contact.head_url
        };
        contact.badge_infos.forEach(info => {
            if (info.badge_type === 2) {// 消费等级
                _user.level = info.badge_level;
            } else if (info.badge_type === 10 && info.badge_name !== undefined) {// 神秘观众需要改一下username
                _user.user_name = '<*>'+info.badge_name;
            } else if (info.badge_type === 4) {// 粉丝团
                _user.fansclub = {};
                _user.fansclub.level = info.badge_level;
                if (info.badge_name) {
                    _user.fansclub.name = info.badge_name;
                }
                // console.log(JSON.stringify(info))
            }
        })
        return _user;
    }

    _inline_packCommon(msg_type, msg_type_str) {
        var _eventData = {};
        _eventData.msg_type = msg_type;
        _eventData.msg_type_str = msg_type_str;
        return _eventData;
    }

    _inline_createContact(contact) {
        return {
            username: contact.contact.username,
            nickname: contact.contact.nickname,
            head_url: contact.contact.head_url,
            signature: contact.signature,
            ext_info: contact.contact.ext_info,
            badge_infos: contact.badge_infos,
            live_identity: contact.live_identity,
        }
    }

    _inline_packageOnlineMember(eventCallback, logCallback, respJsonStr) {
        var weixinOnlineMember = {};
        weixinOnlineMember.live_id = respJsonStr.live_id;
        weixinOnlineMember.online_member_count = respJsonStr.online_member_count;
        weixinOnlineMember.live_contacts_max_display_count = respJsonStr.live_contacts_max_display_count;
        weixinOnlineMember.live_contacts = [];
        if (respJsonStr.live_contacts !== undefined) {
            respJsonStr.live_contacts.forEach(contact => {
                var _contact = this._inline_createContact(contact);
                _contact.live_heat_value = contact.live_heat_value;
                _contact.reward_amount_in_heat = contact.reward_amount_in_heat;
                weixinOnlineMember.live_contacts.push(_contact);
            })
        }
        weixinOnlineMember.recent_reward_contacts = [];
        if (respJsonStr.recent_reward_contacts !== undefined) {
            respJsonStr.recent_reward_contacts.forEach(contact_items => {
                var recent_reward_contact = {};
                recent_reward_contact.contact = this._inline_createContact(contact_items.contact);
                recent_reward_contact.items = [];
                contact_items.items.forEach(item => {
                    if (item.gift !== undefined) {
                        recent_reward_contact.items.push({
                            reward_product_id: item.gift.reward_product_id,
                            product_count: item.product_count,
                        });
                    }
                })
                recent_reward_contact.reward_amount_in_heat = contact_items.reward_amount_in_heat;
                weixinOnlineMember.recent_reward_contacts.push(recent_reward_contact);
            })
        }
    }

    _inline_decodeDataFromResponse(eventCallback,
                                    logCallback,
                                    respJsonStr,
                                    requestUrl) {
        var eventsData = initEvents(this._inline_context, 'weixin');
        const _weixinEventData = {};
        if (this._inline_txmll) {
            _weixinEventData.origin = respJsonStr;
        }

        // 直播间信息
        if (respJsonStr.live_info !== undefined) {
            // respJsonStr.live_info.live_id
            eventsData.watching_total = respJsonStr.live_info.online_cnt;
            eventsData.like_total = respJsonStr.live_info.like_cnt;
            if (respJsonStr.live_info.live_status !== 1) {    // 1 表示直播中，2 表示直播结束。
                var _eventData = this._inline_packCommon(5, 'control_msg');
                _eventData.control_msg = {};
                _eventData.control_msg.action = 1;
                _eventData.control_msg.action_msg = '直播结束';
                _eventData.control_msg.room_id = respJsonStr.live_info.live_id;
                eventsData.events_data.push(_eventData);
                // console.log(JSON.stringify(respJsonStr.live_info))
            }
        }
        // _weixinEventData.live_info.cur_forward_count = respJsonStr.cur_forward_count;
        // _weixinEventData.live_info.cur_participant_count = respJsonStr.cur_participant_count;
        // 消息列表
        _weixinEventData.app_msg_list = [];
        if (respJsonStr.msg_list !== undefined) {
            respJsonStr.msg_list.forEach(_msg => {
                if (_msg.type === 1) {  // 评论
                    let _eventData = this._inline_packCommon(2, 'comment_msg');
                    _eventData.comment_msg = {};
                    _eventData.comment_msg.user = this._inline_packUser(_msg.finder_live_contact);
                    _eventData.comment_msg.content = _msg.content;
                    eventsData.events_data.push(_eventData);
                } else if (_msg.type === 10005) { // 来人
                    let _eventData = this._inline_packCommon(4, 'member_msg');
                    _eventData.member_msg = {};
                    _eventData.member_msg.user = this._inline_packUser(_msg.finder_live_contact);
                    eventsData.events_data.push(_eventData);
                } else {
                    // TODO:
                    // console.log(commonMsg(msg), ' unknown type:', msg.type);
                }
            });
        }

        // App消息列表
        if (respJsonStr.app_msg_list !== undefined) {
            respJsonStr.app_msg_list.forEach(_msg => {
                // 通用的数据:
                // var _app_msg = {};
                // if (_msg.to_user_contact !== undefined) {
                //     _app_msg.to_user_contact = this._inline_createContact(_msg.to_user_contact);
                // }
                // if (_msg.from_user_contact !== undefined) {
                //     _app_msg.from_user_contact = this._inline_createContact(_msg.from_user_contact);
                // }
                // _app_msg.client_msg_id = _msg.client_msg_id;
                // _app_msg.msg_type = _msg.msg_type;
                // _app_msg.seq = _msg.seq;
                // console.log(_msg.client_msg_id);
                const decode_payload = this._inline_b64Payload(_msg.payload);
                if (_msg.msg_type === 20002) {   // replayComment 回复消息
                    // // {"content":"又是你啊  又抓到你了呢","refer_product_question_card_id":""}
                    // // _msg.quoted_msg_seq，在pc微信可以看到回复的消息编号
                    // if (decode_payload.content !== undefined) {
                    //     _app_msg.replay_comment_content = decode_payload.content;
                    // } else {
                    //     _app_msg.unknown_info = _msg;
                    //     // self_log(_msg, commonAppMsg(_msg), '未处理 ',decode_payload);
                    // }
                    // _weixinEventData.app_msg_list.push(_app_msg);
                }
                else if (_msg.msg_type === 20006) {    // 长按点赞，会出现这种
                    // {"to_user_contact":{"contact":{"bind_info":[],"menu":[]},"badge_infos":[]},"msg_type":20006,"client_msg_id":"finderlive_appmsg_c1d98beb231f201796a2d3b053f23d1b_dc27d0a081c1e5ceef6b0dc37f23e532_o9hHn5c1HFHJ3g1zpM_lkRs9NfXo","quoted_msg_seq":"0","payload":"e30=","option":{},"recipient_contact_list":[],"session_id":"","is_floatmsg":0,"float_type":0,"refer_float_product_id":"","seq":"38853","from_user_contact":{"contact":{"username":"v8_0600005bdcd427c6012ca720ada34132c767f7495adab9ffde64a736fb469385dbf3f356970663a768ab8f026c93e0f53f87cb5e439e112a1ead8f322a81a33ade1af3@flstranger","nickname":"E**","head_url":"http://wx.qlogo.cn/mmhead/M63rS1kveYfdBdLNZuWLUPBdCBiaS2mcet19jowhgHKMWDUydEAkPwFMd7xGuH7jtFLMDiciaGL9Ro/132","bind_info":[],"menu":[]},"display_nickname":"E**","live_identity":1,"live_contact_ext_info":"CAA=","badge_infos":[{"badge_type":2,"badge_level":0},{"badge_type":4,"badge_img_url":"https://dldir1v6.qq.com/weixin/checkresupdate/two_d211f23621f24079a89f13ad089fe423.png","badge_level":6,"badge_name":"倍爱叁"}],"location":{}}}
                    // 不知道什么意思， payload {}
                    // self_log(_msg, commonAppMsg(_msg), 20006);
                    // _weixinEventData.app_msg_list.push(_app_msg);
                    let _eventData = this._inline_packCommon(1, 'like_msg');
                    _eventData.like_msg = {};
                    _eventData.like_msg.user = this._inline_packUser(_msg.from_user_contact);
                    _eventData.like_msg.like_count = 1;  // 默认1
                    eventsData.events_data.push(_eventData);
                }
                else if (_msg.msg_type === 20009) {  // liveReward 礼物
                    // {"reward_product_id":"MMFinderLiveGift100001","reward_product_count":1,"reward_amount_in_wecoin":1,"reward_gift":{"reward_product_id":"MMFinderLiveGift100001","thumbnail_file_url":"https://wxapp.tc.qq.com/292/20304/stodownload?m=d0417da4176100fd9e3ae863822f81af&filekey=30350201010421301f02020124040253480410d0417da4176100fd9e3ae863822f81af0203009ac9040d00000004627466730000000132&hy=SH&storeid=267f8b1df00071d57000000000000012400004f5053482679b1715682cdb3c&bizid=1023&spefile=1&web=1","preview_pag_url":"https://wxapp.tc.qq.com/292/20252/stodownload?m=1223d8378ae038c61ffeefa585932f8b&filekey=30340201010420301e020201240402534804101223d8378ae038c61ffeefa585932f8b02023572040d00000004627466730000000132&hy=SH&storeid=267f8b1dc00022e31000000000000012400004f1c53480c57e0d1568efcfd6&bizid=1023&spefile=1&web=1","animation_pag_url":"","thumbnail_file_md5":"d0417da4176100fd9e3ae863822f81af","preview_pag_md5":"1223d8378ae038c61ffeefa585932f8b","animation_pag_md5":"","name":"爱心","price":1.0,"gift_type":1,"unlock_intimacy_level":0,"flag":32768,"landscape_animation_pag_url":"","landscape_animation_pag_md5":"","custom_info":"","batch_give_config_list":{"config":[{"number":66},{"number":88},{"number":99},{"number":520},{"number":999}],"gift_drop_batch_size":22},"disable_combo":false,"multi_animation_list":[],"foreground_info":{"foreground_resource":{}},"use_rfx_pag":false,"pag_config":{"ios_use_rfx_pag":true,"android_use_rfx_pag":true,"pc_use_rfx_pag":false}},"content":"闪闪送了主播1个爱心","combo_product_count":0,"combo_id":"c72064588fb424636ce3437650205a89_a847a697-269e-44c3-8ab8-0fe665155178","reward_ext_context":{"reward_ext_info":"eyJzY2VuZSI6MCwid2lzaF9saXN0X2lkIjoiMTQ2NjgyNDAwNzU1OTc5NDA5NzNfMTc0ODU5MDQ5ODc2NyIsImlzX2JhdGNoX3Jld2FyZCI6ZmFsc2UsImdpZnRfd2FsbF9hY3Rpb25fdHlwZSI6NDI5NDk2NzI5NSwiYXR0YWNrX2ZpbmRlcl91c2VybmFtZV9saXN0IjpbXX0=","reward_type":0},"gift_item":[],"pk_extra_times_multi_100":0,"attack_item_list":[],"pk_extra_times_flag":0,"extra_pk_value":0}
                    // 连麦的时候 其他主播的礼物数据也会收到这个,而且 decode_payload.combo_product_count 这个值表示总数？，但是不会收到 20013
                    // reward_product_count
                    // reward_amount_in_wecoin
                    // combo_product_count === 0
                    // combo_id
                    // extra_pk_value
                    // reward_ext_context.reward_ext_info   -> {"scene":0,"wish_list_id":"","is_batch_reward":false,"gift_wall_action_type":4294967295,"attack_finder_username_list":[]}
                    // 如果 combo_product_count === 1,则不会收到 20013
                    // 送给谁，to_user_contact.display_nickname
                    if (decode_payload.reward_gift !== undefined) {
                        // TODO: 很重要，连麦送给别人也会收到这个，所以需要判断当前主播id：to_user_contact.display_nickname
                        // check
                        // if (this._inline_log_finder_id !== '') {
                        //     if (_msg.to_user_contact.contact.username !== this._inline_log_finder_id) {
                        //         // 不处理送给其他主播的
                        //         return;
                        //     }
                        // }
                        var local_gift = this._inline_getGift(decode_payload.combo_id);
                        var total_count = decode_payload.reward_product_count;
                        if (local_gift === null) {
                            // 初始化
                            this._inline_addOrUpdateGift(decode_payload.combo_id, decode_payload.reward_product_count);
                        } else {
                            total_count = this._inline_incrementGift(decode_payload.combo_id, decode_payload.reward_product_count).giftCount;
                        }
                        let _eventData = this._inline_packCommon(3, 'gift_msg');
                        _eventData.gift_msg = {};
                        _eventData.gift_msg.user = this._inline_packUser(_msg.from_user_contact);
                        _eventData.gift_msg.to_user = this._inline_packUser(_msg.to_user_contact);
                        _eventData.gift_msg.gift_id = decode_payload.reward_gift.reward_product_id;
                        _eventData.gift_msg.gift_name = decode_payload.reward_gift.name;
                        _eventData.gift_msg.gift_url = '';// TODO:
                        _eventData.gift_msg.gift_price = decode_payload.reward_gift.price;
                        _eventData.gift_msg.batch_size = 1;// TODO: check
                        _eventData.gift_msg.total_count = total_count;// 微信[decode_payload.combo_product_count]是0,必须本地计算
                        _eventData.gift_msg.count = decode_payload.reward_product_count;
                        _eventData.gift_msg.gift_msg_key = decode_payload.combo_id;
                        eventsData.events_data.push(_eventData);
                        // console.log(JSON.stringify(decode_payload));
                    }

                    // self_log(_msg, commonAppMsg(_msg), '给',userContactMsg(_msg.to_user_contact), '送了',
                    //     decode_payload.reward_gift.name+'x'+decode_payload.reward_product_count, 'price:', decode_payload.reward_gift.price,// decode_payload.reward_amount_in_wecoin,
                    //     'combo_id:',decode_payload.combo_id, decode_payload.combo_product_count, respJsonStr.live_info.reward_total_amount_in_wecoin);// decode_payload.extra_pk_value

                    // console.log(JSON.stringify(decode_payload));
                    // self_log(_msg, commonAppMsg(_msg), parsePayload(_msg.payload));
                } else if (_msg.msg_type === 20013) {    // comboLiveReward 礼物, 结束连击，作为20009的结束
                    // 忽略
                    // // combo_product_count !== 0
                    // // combo_id
                    // // self_log(_msg, commonAppMsg(_msg), parsePayload(_msg.payload));
                    // if (decode_payload.reward_gift !== undefined) {
                    //     _app_msg.content = decode_payload.content;
                    //     _app_msg.gift = {
                    //         reward_product_id: decode_payload.reward_gift.reward_product_id,
                    //         name: decode_payload.reward_gift.name,
                    //         price: decode_payload.reward_gift.price,
                    //         unlock_intimacy_level: decode_payload.reward_gift.unlock_intimacy_level,
                    //         reward_product_count: decode_payload.reward_product_count,
                    //         combo_product_count: decode_payload.combo_product_count,
                    //         combo_id: decode_payload.combo_id,
                    //     };
                    // } else {
                    //     _app_msg.unknown_info = _msg;
                    // }
                    // _weixinEventData.app_msg_list.push(_app_msg);
                    // // self_log(_msg, commonAppMsg(_msg), '给',userContactMsg(_msg.to_user_contact), '一共送了',
                    // //     decode_payload.reward_gift.name+'x'+decode_payload.combo_product_count, 'price:', decode_payload.reward_gift.price,
                    // //     'combo_id:',decode_payload.combo_id, respJsonStr.live_info.reward_total_amount_in_wecoin);
                    // // console.log(JSON.stringify(decode_payload));
                } else if (_msg.msg_type === 20031) { // globalLevelUpgrade 等级提升   {"type":1,"from_level":11,"to_level":12}
                    // if (decode_payload.to_level !== undefined) {
                    //     _app_msg.level_upgrade = {
                    //         from_level: decode_payload.from_level,
                    //         to_level: decode_payload.to_level,
                    //         type: decode_payload.type,
                    //     }
                    //     // self_log(_msg, commonAppMsg(_msg), '等级提升:',decode_payload.from_level, ' -> ', decode_payload.to_level);
                    // } else {
                    //     _app_msg.unknown_info = _msg;
                    //     // self_log(_msg, commonAppMsg(_msg), '未处理',JSON.stringify(decode_payload));
                    // }
                    // _weixinEventData.app_msg_list.push(_app_msg);
                }
                else if (_msg.msg_type === 20062) {    // 点赞评论, 引用消息 quoted_msg_seq
                    // if (decode_payload.total_like_num !== undefined) {
                    //     // payload: {"msg_seq":4119,"op_type":1,"total_like_num":2,"is_show_like_num":true}
                    //     _app_msg.like_comment = {
                    //         msg_seq: decode_payload.msg_seq,
                    //         op_type: decode_payload.op_type,
                    //         total_like_num: decode_payload.total_like_num,
                    //     }
                    //     // self_log(_msg, commonAppMsg(_msg), '点赞评论:', decode_payload.msg_seq, 'x', decode_payload.total_like_num);
                    // } else {
                    //     _app_msg.unknown_info = _msg;
                    //     // self_log(_msg, commonAppMsg(_msg), JSON.stringify(decode_payload));
                    // }
                    // _weixinEventData.app_msg_list.push(_app_msg);
                } else if (_msg.msg_type === 20078) {  // 关注
                    // //  {"toUserContact": {"contact": {"headUrl": "https://res.wx.qq.com/t/fed_upload/176e4129-5a90-49e2-914f-ee620ddbb782/%E5%BE%AE%E4%BF%A1%E8%BA%AB%E4%BB%BD%E5%A4%B4%E5%83%8F.png", "bindInfo": [], "menu": []}, "badgeInfos": []}, "msgType": 20078, "clientMsgId": "finderlive_appmsg_2e24b7114e4cfdec30d6c6ec84a41d8a_507fe4f5dc3b0b19951502baaff57d76", "quotedMsgSeq": "0", "payload": "eyJmb2xsb3dfdGltZSI6MTc0OTA5MDc4OCwid29yZGluZyI6IuWFs+azqOS6huS4u+aSrSIsInNob3dfYXJlYSI6MX0=", "option": {}, "recipientContactList": [], "sessionId": "", "isFloatmsg": 0, "floatType": 0, "referFloatProductId": "", "seq": "3", "fromUserContact": {"contact": {"username": "v8_06000047ca942cae0b3d80b1db4a5246568fcf2d1078c1579134885ed75768412581ff45b874471909cb240fbff383ce@flstranger", "nickname": "i", "headUrl": "https://res.wx.qq.com/t/fed_upload/176e4129-5a90-49e2-914f-ee620ddbb782/%E5%BE%AE%E4%BF%A1%E8%BA%AB%E4%BB%BD%E5%A4%B4%E5%83%8F.png", "bindInfo": [], "menu": []}, "displayNickname": "i", "liveIdentity": 1, "liveContactExtInfo": "CAA=", "badgeInfos": [{"badgeType": 2, "badgeLevel": 0}], "location": {}}}
                    // // payload: {"follow_time":1749090788,"wording":"关注了主播","show_area":1}
                    // if (decode_payload.wording !== undefined) {
                    //     _app_msg.follow_info = {
                    //         follow_time: decode_payload.follow_time,
                    //         wording: decode_payload.wording,
                    //     }
                    //     // self_log(_msg, commonAppMsg(_msg), decode_payload.wording,'follow_time:', decode_payload.follow_time);
                    // } else {
                    //     _app_msg.unknown_info = _msg;
                    //     // self_log(_msg, commonAppMsg(_msg), '未处理',JSON.stringify(decode_payload));
                    // }
                    // _weixinEventData.app_msg_list.push(_app_msg);
                }
                else if (_msg.msg_type === 20122) {  // 赞了直播, 为你点赞超过20次
                    // payload {"type":1,"wording":"赞了直播","show_area":1}
                    // if (decode_payload.wording !== undefined) {
                    //     _app_msg.common_wording_info = {
                    //         wording: decode_payload.wording,
                    //         type: decode_payload.type,
                    //     }
                    //     // self_log(_msg, commonAppMsg(_msg), decode_payload.wording,'type:', decode_payload.type);
                    // } else {
                    //     _app_msg.unknown_info = _msg;
                    //     // self_log(_msg, commonAppMsg(_msg), '未处理',JSON.stringify(decode_payload));
                    // }
                    // _weixinEventData.app_msg_list.push(_app_msg);
                    let _eventData = this._inline_packCommon(1, 'like_msg');
                    _eventData.like_msg = {};
                    _eventData.like_msg.user = this._inline_packUser(_msg.from_user_contact);
                    _eventData.like_msg.like_count = 1;  // 默认1
                    eventsData.events_data.push(_eventData);
                }
                else if (_msg.msg_type === 20125) {    // 分享了直播
                    // // payload {"type":1,"wording":"分享了直播","show_area":1}
                    // if (decode_payload.wording !== undefined) {
                    //     _app_msg.common_wording_info = {
                    //         wording: decode_payload.wording,
                    //         type: decode_payload.type,
                    //     }
                    //     // self_log(_msg, commonAppMsg(_msg), decode_payload.wording,'type:', decode_payload.type);
                    // } else {
                    //     _app_msg.unknown_info = _msg;
                    //     // self_log(_msg, commonAppMsg(_msg), '未处理',JSON.stringify(decode_payload));
                    // }
                    // _weixinEventData.app_msg_list.push(_app_msg);
                }
                else {
                    // TODO: 不支持的消息
                    // console.log(JSON.stringify(_msg));
                    // write_msg_debug('modules/weixin/samples/else_type.txt', JSON.stringify(_msg))
                    // self_log(_msg, commonAppMsg(_msg), ' unknown msg_type:', _msg.msg_type+", " + b64Payload(_msg.payload));
                }
            })
        }

        if (eventCallback !== null) {
            eventCallback(eventsData);
        }
    }
    // 测试
    testParseRaw(payload, live_url, eventCallback, logCallback) {
        if (this._inline_test_hex && payload !== undefined) {
            this._inline_decodeDataFromResponse(eventCallback, logCallback, payload, live_url);
        }
    }
}

/// @secureEnd

module.exports = LiveWeixin;
