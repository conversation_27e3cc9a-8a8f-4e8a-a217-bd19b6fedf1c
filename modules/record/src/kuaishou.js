// const { version } = require('../package.json');
//
// const pb_webcast = require("./bundle2.min");
// const zlib = require("zlib");

const {kuaishou} = require("./release_webcast.min");
// const pb_webcast = require("./auto_gen_webcast");
const {gunzipSync} = require("zlib");
const {JSDOM} = require("jsdom");
const {packCtlEvent, initEvents} = require("./common");

/// @secureBegin

class LiveKuaishou {
    constructor(context, test=false) {
        this._inline_context = context;
        this._inline_gifts = new Map(); // 存储礼物数据的 Map
        this._inline_TIMEOUT_SECONDS = 15 * 1000; // 15秒（以毫秒为单位）
        this._inline_txmll = false;
        this._inline_test_hex = test;
        this._inline_allGiftsJson = null;
    }

    // 添加或更新礼物数据
    _inline_addOrUpdateGift(combo_id, giftCount) {
        const now = Date.now();
        this._inline_gifts.set(combo_id, {
            giftCount,
            lastUpdated: now,
            timeout: setTimeout(() => {
                this._inline_gifts.delete(combo_id); // 30秒后删除未更新的数据
            }, this._inline_TIMEOUT_SECONDS)
        });
        return combo_id;
    }

    // 减去上一次的礼物个数
    _inline_decrementGift(combo_id, giftCount) {
        if (!this._inline_gifts.has(combo_id)) {
            return null; // 如果 ID 不存在，返回 null
        }
        const gift = this._inline_gifts.get(combo_id);
        clearTimeout(gift.timeout); // 清除旧的定时器
        var ret_count = giftCount - gift.giftCount;
        if (ret_count < 0) {
            ret_count = giftCount;
        }
        gift.giftCount = giftCount;
        gift.lastUpdated = Date.now();
        gift.timeout = setTimeout(() => {
            this._inline_gifts.delete(combo_id); // 30秒后删除未更新的数据
        }, this._inline_TIMEOUT_SECONDS);
        this._inline_gifts.set(combo_id, gift);
        return ret_count;
    }

    // 获取礼物数据
    _inline_getGift(combo_id) {
        return this._inline_gifts.get(combo_id) || null;
    }

    /**
     * 释放内部数据
     * */
    release() {
        this._inline_gifts.forEach(_gift => {
            clearTimeout(_gift.timeout);
            _gift.timeout = null;
        })
        this._inline_gifts.clear();
    }

    /**
     * 一次只能启动一个
     * */
    startMonitorRoom(mainWindow, liveUrl, eventCallback, logCallback) {
        if (liveUrl === '' || liveUrl === undefined || !liveUrl || !/^https?:\/\//.test(liveUrl)) {
            var eventsData = initEvents(this._inline_context, 'kuaishou');
            eventsData.events_data.push(packCtlEvent(100,  'liveUrl错误', liveUrl))
            eventCallback(eventsData);
            return;
        }
        try {
            this._inline_txmll = process.env.x234534trsegfsdc || false;
        } catch (err) {
            this._inline_txmll = false;
        }
        const closeWebSocket = false;// 是否禁用 ws
        const defaultSession = mainWindow.webContents.session;
        defaultSession.webRequest.onBeforeRequest((details, callback) => {
            if ( (details.url.startsWith(atob('d3NzOi8v')) || details.url.startsWith(atob('d3M6Ly8=')))) {// cmp ws://,  wss://
                // this._inline_self_debug('拦截 WebSocket 请求:' + details.url);
                // 阻止 WebSocket 请求
                callback({cancel: closeWebSocket});
            } else if (details.url.startsWith(atob('aHR0cHM6Ly8='/* https:// */))) {
                // this._inline_self_debug('拦截HTTPS请求:' + details.url);
                // enter
                callback({});
            } else {
                // 允许其他请求
                callback({});
            }
        });
        try {
            mainWindow.webContents.debugger.attach()
        } catch (err) {
            this._inline_self_debug('Debugger attach failed : ' + err)
        }

        mainWindow.webContents.debugger.on(atob('ZGV0YWNo'/* 'detach' */), (event, reason) => {
            // this._inline_self_debug('Debugger detached due to : ' + reason)
        })
        const gWebcastObj = {};

        var getResponseDecodeBody = async (requestId) => {
            const res = await mainWindow.webContents.debugger.sendCommand(atob('RmV0Y2guZ2V0UmVzcG9uc2VCb2R5'/* 'Fetch.getResponseBody' */), {requestId: requestId})
            // return res.body
            return res.base64Encoded ? Buffer.from(res.body, 'base64') : res.body
        }
        var getInitialState = (document, logCallback) => {
            // 获取所有 <script> 标签
            const scripts = document.getElementsByTagName('script');
            for (let script of scripts) {
                // 检查是否包含 window.__INITIAL_STATE__
                if (script.textContent.includes('window.__INITIAL_STATE__')) {
                    // 提取 window.__INITIAL_STATE__= 后面的内容
                    const match = script.textContent.match(/window\.__INITIAL_STATE__\s*=\s*(\{.*?});/s);
                    if (match && match[1]) {
                        try {
                            // 解析 JSON 字符串, 可能会有异常字段 undefined
                            const cleanedJsonString = match[1].replace(/undefined/g, 'null');
                            const jsonData = JSON.parse(cleanedJsonString);
                            return jsonData;
                        } catch (error) {
                            logCallback('解析 state 失败:'+error);
                            return null;
                        }
                    }
                }
            }
            return null;
        }

        var parseUrl = async (params) => {
            var resBody = await getResponseDecodeBody(params.requestId);//.then(function (resBody) {
            if (resBody.length <= 0) {
                return
            }
            if (params.request.url.includes(atob('L2xpdmVfYXBpL2xpdmVyb29tL3JlY2FsbA=='/* '/live_api/liveroom/recall' */))) {
                // 进入直播间之前的消息数据
                this._inline_parseRecall(resBody, liveUrl, eventCallback, logCallback);
            } else if (params.request.url.includes(atob('L2xpdmVfYXBpL2Vtb2ppL2FsbGdpZnRz'/* '/live_api/emoji/allgifts' */))) {
                // 所有礼物id对应的名字和链接
                try {
                    this._inline_allGiftsJson = JSON.parse(resBody);
                } catch (e) {
                }
            } else if (params.request.url.includes(atob('aHR0cHM6Ly9saXZlLmt1YWlzaG91LmNvbS91Lw=='/* 'https://live.kuaishou.com/u/' */))) {
                const dom = new JSDOM(resBody);
                const {document} = dom.window;

                // 主播尚未开播，可以观看其他直播
                const tipElement = document.querySelector('.state .desc .tip span');
                var text = '';
                var eventsData = initEvents(this._inline_context, 'kuaishou');
                if (tipElement) {
                    text = tipElement.textContent;
                    eventsData.events_data.push(packCtlEvent(1000, text + ',' + params.request.url, liveUrl));
                } else {
                    // 请求过快，请稍后重试
                    const maskElement = document.querySelector('.state .mask .tip span');
                    if (maskElement) {
                        text = maskElement.textContent;
                        eventsData.events_data.push(packCtlEvent(1001, text + ',' + params.request.url, liveUrl));
                    } else {
                        // 正在直播
                        const nameElement = document.querySelector('.name-group .name');
                        if (nameElement) {
                            text = nameElement.textContent;// 直播名
                            var _event = packCtlEvent(0, '正在直播', liveUrl)
                            _event.control_msg.room_name = text;

                            var initialState = getInitialState(document, logCallback)
                            if (initialState !== null) {
                                // {"user":{"name":"","age":0,"banned":"BANNED","socialBanned":"SOCIALBANNED","isolate":"ISOLATE","cleanState":"CLEAN","bannedErrMsg":"由于违反社区规定，账号封禁，请前往快手APP进行解封申诉","socialBannedErrMsg":"账号异常，请前往快手APP激活","isolateErrMsg":"该链接指向页面不存在","bannedErrMsgByOther":"该用户因违反社区规定，账号封禁","qrLoginInfo":{},"userInfoQuery":{"ownerInfo":{"id":"3xsn62c5wm6jnts","name":"int3239","description":"","avatar":"https://p4-pro.a.yximgs.com/uhead/AB/2019/11/17/13/BMjAxOTExMTcxMzIzNThfMTU3NDk1MTEyM18wX2hkNjQ5XzM1OA==_s.jpg","sex":"M","constellation":"","cityName":"广东 广州市","originUserId":1574951123,"privacy":false,"isNew":false,"timestamp":1751264874418,"verifiedStatus":{"verified":false,"description":"","type":0,"new":false},"bannedStatus":{"banned":false,"socialBanned":false,"isolate":false,"defriend":false},"counts":{"fan":"5","follow":"10","photo":1,"playback":0,"liked":259,"private":1,"review":0,"open":1},"isAdult":true},"kshellBalance":{"result":1,"kshell":80,"host-name":"public-bjx-c6-kce-node77.idchb1az1.hb1.kwaidc.com"}},"loginMutation":{},"$isServerPretch":true},"pcConfig":{"pcConfig":{"result":1,"did":"web_930efa5a54e5694dd0f34e4d25688cee","config":{"pcLive.webConfig.needLoginToWatchHD":1,"pcLive.webConfig.needShieldWatchingCount":1,"pcLive.webConfig.showRegister":1,"pcLive.campaignEntrance.topSidebar":[],"pcLive.campaignEntrance.sidebar":[],"pcLive.campaignEntrance.loginPage":{"superImageUrl":"https://static.yximgs.com/udata/pkg/kuaishou-front-end-live/loginreward_login_banner.png","smallImageUrl":"https://static.yximgs.com/udata/pkg/kuaishou-front-end-live/loginreward_login_banner.png","link":"/loginreward"},"pcLive.campaignEntrance.sharePage":{"superImageUrl":"https://static.yximgs.com/udata/pkg/kuaishou-front-end-live/loginreward_shortvideo_player_pendant.gif","smallImageUrl":"https://static.yximgs.com/udata/pkg/kuaishou-front-end-live/loginreward_shortvideo_player_pendant.gif","link":"/loginreward"},"pcLive.webConfig.liveWebAdaptiveConfig":{"consistent_bitrate_threshold":2,"lower_buffered_time":2000,"upper_buffered_time":3500,"timeout_minutes":10,"buffer_up_ahead":3,"buffer_down_ahead":5,"start_up_duration":3,"speed_heartbeat_limit":9},"pcLive.webConfig.liveGiftShowInBarrage":[94],"pcLive.webConfig.showBet":0,"pcLive.webConfig.hideFullScreenGiftList":["lol1314666"],"pcLive.webConfig.useGrayTheme":{"useGray":false},"pcLive.webConfig.liveMaskTabList":[{"index":1,"type":"DQRM","name":"热门"},{"index":2,"type":"WYJJ","name":"网游"},{"index":3,"type":"DJRY","name":"单机"},{"index":4,"type":"SYXX","name":"手游"},{"index":5,"type":"QP","name":"棋牌"},{"index":6,"type":"YL","name":"娱乐"},{"index":7,"type":"ZH","name":"综合"},{"index":8,"type":"KJ","name":"文化"}],"pcLive.webConfig.limitToPlay":["3x9t2xhc7p7c9sq","3xm48rvgpbgbzs6"],"pcLive.webConfig.defaultPreference":{"1":{"name":"英雄联盟","id":1,"totalStayLength":100},"25":{"name":"CS:GO","id":25,"totalStayLength":100},"1001":{"name":"王者荣耀","id":1001,"totalStayLength":100},"22008":{"name":"和平精英","id":22008,"totalStayLength":100},"22146":{"name":"无畏契约","id":22146,"totalStayLength":100},"22181":{"name":"原神","id":22181,"totalStayLength":100}},"pcLive.webConfig.headerConfig":[{"name":"ACL","path":"race","seo":"英雄亚冠ACL","startTime":"2025-5-8","endTime":"2025-5-19","raceId":"nB1uqX31G8Q"},{"name":"暗区突围","path":"activity","logo":"https://static.yximgs.com/udata/pkg/KS-GAME-WEB/pc-live-next/images/logo.fc801e3cb5504216.png","logoAct":"https://static.yximgs.com/udata/pkg/KS-GAME-WEB/pc-live-next/images/logo-act.png","startTime":"2025-4-29 08:00:00","endTime":"2025-6-4 08:00:00","website":"https://aqtwwx.qq.com/?ADTAG=media.buy.baidukeyword.pc_6&bd_vid=11610422377984626911","download":"https://cdn-miniloader.ca.qq.com/live/abae911e82fdb13c/d7285f00e4363e00/ABIMiniLoader.6.4.22.2257.19000168.exe"}],"gameZone.competition.electronicSportsWorldCupConfig":{"startTime":1719473756052,"endTime":3239838294166,"authorIdSet":[1684967274,1499501607,1378025568,1079586696,450856650,2604645168,1334505455,1291811058,1815060185,2202053688,3300285478,1664540263,4216552301,4216581895,748376029,1798695301,3621548682,1501313063,4216573015,4216592817,40300010,1815060146,40300023,704668133,705149572,1317959611,120500130,964081109,1966711595,1319131758,1154478708,1451755736,2347760478,408630309,727058716,969454496]},"pcLive.webConfig.emojiPanel":{"[666]":"//ali2.a.yximgs.com/bs2/emotion/1704763447505third_party_s1296657489.png","[奸笑]":"//ali2.a.yximgs.com/bs2/emotion/1704776059816third_party_s1296748052.png","[捂脸]":"//bd.a.yximgs.com/bs2/emotion/1704776021202third_party_s1296747457.png","[龇牙]":"//bd.a.yximgs.com/bs2/emotion/1704775978144third_party_s1296746882.png","[哼]":"//ali2.a.yximgs.com/bs2/emotion/1704775935917third_party_s1296746407.png","[哦]":"//ali2.a.yximgs.com/bs2/emotion/1704775888472third_party_s1296745888.png","[微笑]":"//ali2.a.yximgs.com/bs2/emotion/1704775840073third_party_s1296745254.png","[老铁]":"//ali2.a.yximgs.com/bs2/emotion/1704766004548third_party_s1296668542.png","[双鸡]":"//ali2.a.yximgs.com/bs2/emotion/1704765943430third_party_s1296668337.png","[调皮]":"//ali2.a.yximgs.com/bs2/emotion/1704775798640third_party_s1296744691.png","[呆住]":"//ali2.a.yximgs.com/bs2/emotion/1704775755675third_party_s1296744198.png","[星星眼]":"//ali2.a.yximgs.com/bs2/emotion/1704775705283third_party_s1296743591.png","[爱心]":"//ali2.a.yximgs.com/bs2/emotion/1704765876094third_party_s1296667852.png","[疑问]":"//ali2.a.yximgs.com/bs2/emotion/1704775660342third_party_s1296742992.png","[生气]":"//ali2.a.yximgs.com/bs2/emotion/1704775610489third_party_s1296742417.png","[难过]":"//ali2.a.yximgs.com/bs2/emotion/1704775560543third_party_s1296741805.png","[撇嘴]":"//ali2.a.yximgs.com/bs2/emotion/1704775517486third_party_s1296741380.png","[惊讶]":"//ali2.a.yximgs.com/bs2/emotion/1704775445666third_party_s1296740420.png","[羞涩]":"//ali2.a.yximgs.com/bs2/emotion/1704772289790third_party_s1296705324.png","[色]":"//ali2.a.yximgs.com/bs2/emotion/1704772231770third_party_s1296704846.png","[汗]":"//ali2.a.yximgs.com/bs2/emotion/1704772177310third_party_s1296704283.png","[呕吐]":"//ali2.a.yximgs.com/bs2/emotion/1704772118979third_party_s1296703810.png","[老司机]":"//ali2.a.yximgs.com/bs2/emotion/1704772048844third_party_s1296703216.png","[头盔]":"//ali2.a.yximgs.com/bs2/emotion/1704771654524third_party_s1296700611.png","[酷]":"//ali2.a.yximgs.com/bs2/emotion/1704771603139third_party_s1296700263.png","[笑哭]":"//ali2.a.yximgs.com/bs2/emotion/1704771554781third_party_s1296699971.png","[愉快]":"//ali2.a.yximgs.com/bs2/emotion/1704771498312third_party_s1296699382.png","[委屈]":"//ali2.a.yximgs.com/bs2/emotion/1704771450694third_party_s1296699070.png","[鄙视]":"//ali2.a.yximgs.com/bs2/emotion/1704771395457third_party_s1296698891.png","[白眼]":"//ali2.a.yximgs.com/bs2/emotion/1704771346135third_party_s1296698660.png","[安排]":"//ali2.a.yximgs.com/bs2/emotion/1704771294552third_party_s1296698182.png","[点点关注]":"//ali2.a.yximgs.com/bs2/emotion/1704771236940third_party_s1296697797.png","[鼓掌]":"//ali2.a.yximgs.com/bs2/emotion/1704771184363third_party_s1296697359.png","[抱抱]":"//ali2.a.yximgs.com/bs2/emotion/1704766474596third_party_s1296670536.png","[哈欠]":"//ali2.a.yximgs.com/bs2/emotion/1704771131055third_party_s1296696977.png","[骂你]":"//bd.a.yximgs.com/bs2/emotion/1704771079728third_party_s1296696446.png","[大哭]":"//ali2.a.yximgs.com/bs2/emotion/1704771028940third_party_s1296696033.png","[闭嘴]":"//ali2.a.yximgs.com/bs2/emotion/1704770709451third_party_s1296694128.png","[惊恐]":"//bd.a.yximgs.com/bs2/emotion/1704770650664third_party_s1296693781.png","[红脸蛋]":"//ali2.a.yximgs.com/bs2/emotion/1704770591150third_party_s1296693499.png","[亲亲]":"//bd.a.yximgs.com/bs2/emotion/1704770453439third_party_s1296692624.png","[冷汗]":"//ali2.a.yximgs.com/bs2/emotion/1704770395028third_party_s1296692231.png","[晕]":"//ali2.a.yximgs.com/bs2/emotion/1704770038072third_party_s1296690336.png","[皇冠]":"//ali2.a.yximgs.com/bs2/emotion/1704765811100third_party_s1296667470.png","[火]":"//ali2.a.yximgs.com/bs2/emotion/1704763504359third_party_s1296657659.png","[坏笑]":"//ali2.a.yximgs.com/bs2/emotion/1704769906157third_party_s1296689515.png","[爆炸]":"//ali2.a.yximgs.com/bs2/emotion/1704763342710third_party_s1296657261.png","[大便]":"//ali2.a.yximgs.com/bs2/emotion/1704763289677third_party_s1296657112.png","[可怜]":"//ali2.a.yximgs.com/bs2/emotion/1704769849712third_party_s1296689281.png","[抠鼻]":"//ali2.a.yximgs.com/bs2/emotion/1704769802638third_party_s1296689012.png","[再见]":"//ali2.a.yximgs.com/bs2/emotion/1704769742626third_party_s1296688705.png","[摄像机]":"//ali2.a.yximgs.com/bs2/emotion/1704763241589third_party_s1296656998.png","[赞]":"//ali2.a.yximgs.com/bs2/emotion/1704762153449third_party_s1296652881.png","[平底锅]":"//ali2.a.yximgs.com/bs2/emotion/1704769687542third_party_s1296688270.png","[囧]":"//bd.a.yximgs.com/bs2/emotion/1704769626325third_party_s1296687863.png","[大哥]":"//ali2.a.yximgs.com/bs2/emotion/1704769568039third_party_s1296687559.png","[玫瑰]":"//bd.a.yximgs.com/bs2/emotion/1704763142635third_party_s1296656612.png","[抓狂]":"//ali2.a.yximgs.com/bs2/emotion/1704769507289third_party_s1296687303.png","[嘘]":"//ali2.a.yximgs.com/bs2/emotion/1704769442407third_party_s1296686829.png","[快哭了]":"//ali2.a.yximgs.com/bs2/emotion/1704769347108third_party_s1296686284.png","[骷髅]":"//ali2.a.yximgs.com/bs2/emotion/1704768884061third_party_s1296683458.png","[偷笑]":"//ali2.a.yximgs.com/bs2/emotion/1704768706672third_party_s1296682428.png","[落泪]":"//bd.a.yximgs.com/bs2/emotion/1704768657909third_party_s1296681906.png","[挑逗]":"//ali2.a.yximgs.com/bs2/emotion/1704768600457third_party_s1296681529.png","[困]":"//ali2.a.yximgs.com/bs2/emotion/1704768550467third_party_s1296681259.png","[睡觉]":"//ali2.a.yximgs.com/bs2/emotion/1704768494268third_party_s1296681034.png","[右哼哼]":"//ali2.a.yximgs.com/bs2/emotion/1704768448493third_party_s1296680688.png","[左哼哼]":"//ali2.a.yximgs.com/bs2/emotion/1704768392482third_party_s1296680332.png","[打招呼]":"//ali2.a.yximgs.com/bs2/emotion/1704762100195third_party_s1296652661.png","[流鼻血]":"//ali2.a.yximgs.com/bs2/emotion/1704768330183third_party_s1296680037.png","[偷瞄]":"//ali2.a.yximgs.com/bs2/emotion/1704768270249third_party_s1296679827.png","[吃瓜]":"//ali2.a.yximgs.com/bs2/emotion/1704768220721third_party_s1296679581.png","[黑脸问]":"//ali2.a.yximgs.com/bs2/emotion/1704768168791third_party_s1296679234.png","[旋转]":"//bd.a.yximgs.com/bs2/emotion/1704768117560third_party_s1296678783.png","[憨笑]":"//ali2.a.yximgs.com/bs2/emotion/1704768054223third_party_s1296678500.png","[吐彩虹]":"//ali2.a.yximgs.com/bs2/emotion/1704767994674third_party_s1296678327.png","[擦鼻涕]":"//ali2.a.yximgs.com/bs2/emotion/1704767940928third_party_s1296677841.png","[怒言]":"//ali2.a.yximgs.com/bs2/emotion/1704767890021third_party_s1296677678.png","[拜托]":"//ali2.a.yximgs.com/bs2/emotion/1704767838535third_party_s1296677360.png","[加油]":"//bd.a.yximgs.com/bs2/emotion/1704767780611third_party_s1296677057.png","[暴汗]":"//ali2.a.yximgs.com/bs2/emotion/1704767728861third_party_s1296676729.png","[想吃]":"//ali2.a.yximgs.com/bs2/emotion/1704767667849third_party_s1296676449.png","[打脸]":"//ali2.a.yximgs.com/bs2/emotion/1704767617056third_party_s1296676208.png","[吐血]":"//ali2.a.yximgs.com/bs2/emotion/1704767567911third_party_s1296675878.png","[尴尬]":"//bd.a.yximgs.com/bs2/emotion/1704767506569third_party_s1296675528.png","[出魂儿]":"//ali2.a.yximgs.com/bs2/emotion/1704767441130third_party_s1296675286.png","[大鼻孔]":"//ali2.a.yximgs.com/bs2/emotion/1704767388991third_party_s1296674947.png","[嘣]":"//ali2.a.yximgs.com/bs2/emotion/1704767331411third_party_s1296674616.png","[天啊]":"//ali2.a.yximgs.com/bs2/emotion/1704767271335third_party_s1296674239.png","[石化]":"//bd.a.yximgs.com/bs2/emotion/1704767221776third_party_s1296674013.png","[皱眉]":"//bd.a.yximgs.com/bs2/emotion/1704767005855third_party_s1296672942.png","[装傻]":"//ali2.a.yximgs.com/bs2/emotion/1704766945094third_party_s1296672714.png","[酸了]":"//ali2.a.yximgs.com/bs2/emotion/1704761625171third_party_s1296650672.png","[柴犬]":"//bd.a.yximgs.com/bs2/emotion/1704761562366third_party_s1296650505.png","[狗粮]":"//bd.a.yximgs.com/bs2/emotion/1704762211958third_party_s1296653006.png","[期待]":"//ali2.a.yximgs.com/bs2/emotion/1704766537316third_party_s1296670779.png","[红包]":"//ali2.a.yximgs.com/bs2/emotion/1704762630876third_party_s1296654958.png","[干杯]":"//ali2.a.yximgs.com/bs2/emotion/1704762976549third_party_s1296656073.png","[祈祷]":"//ali2.a.yximgs.com/bs2/emotion/1704762918273third_party_s1296655765.png","[花谢了]":"//bd.a.yximgs.com/bs2/emotion/1704763094568third_party_s1296656398.png","[跪下]":"//ali2.a.yximgs.com/bs2/emotion/1704762867899third_party_s1296655667.png","[南]":"//ali2.a.yximgs.com/bs2/emotion/1704762761234third_party_s1296655362.png","[发]":"//ali2.a.yximgs.com/bs2/emotion/1704762689267third_party_s1296655142.png","[板砖]":"//ali2.a.yximgs.com/bs2/emotion/1704763039720third_party_s1296656165.png","[灯笼]":"//bd.a.yximgs.com/bs2/emotion/1704762583000third_party_s1296654733.png","[福字]":"//ali2.a.yximgs.com/bs2/emotion/1704762521906third_party_s1296654390.png","[鞭炮]":"//ali2.a.yximgs.com/bs2/emotion/1704762474616third_party_s1296654064.png","[烟花]":"//ali2.a.yximgs.com/bs2/emotion/1704762425707third_party_s1296653845.png","[元宝]":"//ali2.a.yximgs.com/bs2/emotion/1704762370541third_party_s1296653559.png","[钱]":"//ali2.a.yximgs.com/bs2/emotion/1704762316380third_party_s1296653319.png","[气球]":"//ali2.a.yximgs.com/bs2/emotion/1704762265510third_party_s1296653172.png","[庆祝]":"//ali2.a.yximgs.com/bs2/emotion/1704770972968third_party_s1296695686.png","[礼花]":"//ali2.a.yximgs.com/bs2/emotion/1704766060554third_party_s1296668711.png","[爱你]":"//ali2.a.yximgs.com/bs2/emotion/1704766815314third_party_s1296671857.png","[摸头]":"//bd.a.yximgs.com/bs2/emotion/1704766657395third_party_s1296671198.png","[雾霾]":"//ali2.a.yximgs.com/bs2/emotion/1704766594591third_party_s1296670988.png","[化妆]":"//ali2.a.yximgs.com/bs2/emotion/1704766889905third_party_s1296672534.png","[涂指甲]":"//ali2.a.yximgs.com/bs2/emotion/1704761682759third_party_s1296650876.png","[欢迎]":"//ali2.a.yximgs.com/bs2/emotion/1704762048343third_party_s1296652504.png","[左拳]":"//ali2.a.yximgs.com/bs2/emotion/1704761978093third_party_s1296652142.png","[右拳]":"//ali2.a.yximgs.com/bs2/emotion/1704761920283third_party_s1296651882.png","[我爱你]":"//ali2.a.yximgs.com/bs2/emotion/1704761864978third_party_s1296651546.png","[比心]":"//ali2.a.yximgs.com/bs2/emotion/1704761810228third_party_s1296651375.png","[肌肉]":"//ali2.a.yximgs.com/bs2/emotion/1704761751472third_party_s1296651098.png","[狮子]":"//ali2.a.yximgs.com/bs2/emotion/1704761502720third_party_s1296650143.png","[龙]":"//ali2.a.yximgs.com/bs2/emotion/1704761427407third_party_s1296649954.png","[狗]":"//bd.a.yximgs.com/bs2/emotion/1704761371094third_party_s1296649726.png","[网红猫]":"//ali2.a.yximgs.com/bs2/emotion/1704761307262third_party_s1296649487.png","[猫]":"//ali2.a.yximgs.com/bs2/emotion/1704761245827third_party_s1296649373.png","[老鼠]":"//ali2.a.yximgs.com/bs2/emotion/1704761189331third_party_s1296649156.png","[不看]":"//ali2.a.yximgs.com/bs2/emotion/1704761136865third_party_s1296649061.png","[不听]":"//ali2.a.yximgs.com/bs2/emotion/1704761074831third_party_s1296648807.png","[不说]":"//ali2.a.yximgs.com/bs2/emotion/1704761014678third_party_s1296648623.png","[猪头]":"//ali2.a.yximgs.com/bs2/emotion/1704760952536third_party_s1296648309.png","[猪鼻子]":"//ali2.a.yximgs.com/bs2/emotion/1704760884297third_party_s1296648188.png","[猪蹄]":"//ali2.a.yximgs.com/bs2/emotion/1704760771304third_party_s1296648058.png","[羊驼]":"//ali2.a.yximgs.com/bs2/emotion/1704760711037third_party_s1296647965.png","[麦克风]":"//ali2.a.yximgs.com/bs2/emotion/1704760647218third_party_s1296647852.png","[跳舞]":"//ali2.a.yximgs.com/bs2/emotion/1704760582888third_party_s1296647756.png","[蛋糕]":"//bd.a.yximgs.com/bs2/emotion/1704760405763third_party_s1296647250.png","[口红]":"//ali2.a.yximgs.com/bs2/emotion/1704760472494third_party_s1296647403.png","[水枪]":"//ali2.a.yximgs.com/bs2/emotion/1704760254873third_party_s1296646780.png","[空投]":"//bd.a.yximgs.com/bs2/emotion/1704760193986third_party_s1296646575.png","[手柄]":"//ali2.a.yximgs.com/bs2/emotion/1704760091452third_party_s1296646374.png","[坑]":"//ali2.a.yximgs.com/bs2/emotion/1704759985787third_party_s1296646211.png","[八倍镜]":"//ali2.a.yximgs.com/bs2/emotion/1704759889785third_party_s1296646065.png","[网红]":"//bd.a.yximgs.com/bs2/emotion/1704759766583third_party_s1296645553.png","[优秀]":"//ali2.a.yximgs.com/bs2/emotion/1704759660137third_party_s1296645301.png","[减1]":"//ali2.a.yximgs.com/bs2/emotion/1704720665766third_party_s1296409266.png","[必胜]":"//bd.a.yximgs.com/bs2/emotion/1704759542034third_party_s1296644941.png","[戴口罩]":"//ali2.a.yximgs.com/bs2/emotion/1704704107430third_party_s1295984389.png","[勤洗手]":"//ali2.a.yximgs.com/bs2/emotion/1704703259238third_party_s1295977084.png","[心心]":"//ali2.a.yximgs.com/bs2/emotion/1704772346276third_party_s1296705763.png","[哭笑]":"//ali2.a.yximgs.com/bs2/emotion/1704702678707third_party_s1295972366.png","[点赞]":"//ali2.a.yximgs.com/bs2/emotion/1704701773745third_party_s1295965382.png","[菜刀]":"//ali2.a.yximgs.com/bs2/emotion/1704762819173third_party_s1296655522.png","[扎心]":"//ali2.a.yximgs.com/bs2/emotion/1704763391921third_party_s1296657345.png","[拍一拍]":"//ali2.a.yximgs.com/bs2/emotion/1704699252327third_party_s1295948859.png","[稳]":"//ali2.a.yximgs.com/bs2/emotion/1704780742645third_party_s1296794020.png","[收到]":"//ali2.a.yximgs.com/bs2/emotion/1704782800053third_party_s1296805478.png","[加1]":"//bd.a.yximgs.com/bs2/emotion/1704782930366third_party_s1296806744.png","[叉号]":"//ali2.a.yximgs.com/bs2/emotion/1704782991766third_party_s1296807145.png","[对号]":"//ali2.a.yximgs.com/bs2/emotion/1704783055649third_party_s1296807447.png","[no]":"//ali2.a.yximgs.com/bs2/emotion/1704783096203third_party_s1296807744.png","[yes]":"//ali2.a.yximgs.com/bs2/emotion/1704783149616third_party_s1296808385.png","[湿巾]":"//ali2.a.yximgs.com/bs2/emotion/1704783721396third_party_s1296812504.png","[吃饭]":"//ali2.a.yximgs.com/bs2/emotion/1704783768169third_party_s1296812801.png","[莫吉托]":"//ali2.a.yximgs.com/bs2/emotion/1704783816852third_party_s1296812989.png","[香草蛋糕]":"//ali2.a.yximgs.com/bs2/emotion/1704783879438third_party_s1296813277.png","[健身]":"//ali2.a.yximgs.com/bs2/emotion/1704783968269third_party_s1296813760.png","[思考]":"//ali2.a.yximgs.com/bs2/emotion/1704784017068third_party_s1296813974.png","[疯狂工作]":"//bd.a.yximgs.com/bs2/emotion/1704784063813third_party_s1296814234.png","[充满干劲]":"//ali2.a.yximgs.com/bs2/emotion/1704785056742third_party_s1296820406.png","[放轻松]":"//ali2.a.yximgs.com/bs2/emotion/1704785103740third_party_s1296820865.png","[卷]":"//ali2.a.yximgs.com/bs2/emotion/1704786813813third_party_s1296831667.png","[熬夜]":"//ali2.a.yximgs.com/bs2/emotion/1704786853592third_party_s1296831948.png","[先睡了]":"//ali2.a.yximgs.com/bs2/emotion/1704786910336third_party_s1296832209.png","[emo]":"//ali2.a.yximgs.com/bs2/emotion/1704786943774third_party_s1296832458.png","[听音乐]":"//bd.a.yximgs.com/bs2/emotion/1704786984951third_party_s1296832685.png","[好运来]":"//ali2.a.yximgs.com/bs2/emotion/1704787279186third_party_s1296834761.png","[元气满满]":"//ali2.a.yximgs.com/bs2/emotion/1704787606436third_party_s1296836908.png","[学习]":"//ali2.a.yximgs.com/bs2/emotion/1704787673918third_party_s1296837372.png","[打电话]":"//ali2.a.yximgs.com/bs2/emotion/1704787717756third_party_s1296837621.png","[熬夜工作]":"//ali2.a.yximgs.com/bs2/emotion/1704787761789third_party_s1296837795.png","[一起嗨皮]":"//ali2.a.yximgs.com/bs2/emotion/1704787815614third_party_s1296838146.png","[难受]":"//ali2.a.yximgs.com/bs2/emotion/1704787901952third_party_s1296838607.png","[上吊]":"//ali2.a.yximgs.com/bs2/emotion/1704787935223third_party_s1296838745.png","[吓]":"//ali2.a.yximgs.com/bs2/emotion/1704787981957third_party_s1296839028.png","[发呆]":"//bd.a.yximgs.com/bs2/emotion/1704788025440third_party_s1296839215.png","[疲惫]":"//ali2.a.yximgs.com/bs2/emotion/1704788904736third_party_s1296845175.png","[裂开]":"//ali2.a.yximgs.com/bs2/emotion/1704788951325third_party_s1296845410.png","[强颜欢笑]":"//ali2.a.yximgs.com/bs2/emotion/1704788997306third_party_s1296845817.png","[翻白眼]":"//ali2.a.yximgs.com/bs2/emotion/1704789037112third_party_s1296846026.png","[难过至极]":"//ali2.a.yximgs.com/bs2/emotion/1704789078980third_party_s1296846236.png","[美滋滋]":"//ali2.a.yximgs.com/bs2/emotion/1704789133176third_party_s1296846710.png","[马上安排]":"//ali2.a.yximgs.com/bs2/emotion/1704789177867third_party_s1296847087.png","[摸鱼]":"//ali2.a.yximgs.com/bs2/emotion/1704789217352third_party_s1296847412.png","[敬礼]":"//ali2.a.yximgs.com/bs2/emotion/1704789256009third_party_s1296847689.png","[求求了]":"//ali2.a.yximgs.com/bs2/emotion/1704789289580third_party_s1296847957.png","[赢麻了]":"//ali2.a.yximgs.com/bs2/emotion/1704789337176third_party_s1296848206.png","[遥遥领先]":"//ali2.a.yximgs.com/bs2/emotion/1704789382578third_party_s1296848447.png","[辣眼睛]":"//ali2.a.yximgs.com/bs2/emotion/1704789443283third_party_s1296848822.png","[ok]":"//ali2.a.yximgs.com/bs2/emotion/1704789999083third_party_s1296852648.png","[握手]":"//ali2.a.yximgs.com/bs2/emotion/1704790046552third_party_s1296852850.png","[抱拳]":"//bd.a.yximgs.com/bs2/emotion/1704790113221third_party_s1296853443.png","[早上好]":"//ali2.a.yximgs.com/bs2/emotion/1704790167344third_party_s1296853922.png","[胡思乱想]":"//ali2.a.yximgs.com/bs2/emotion/1704790657229third_party_s1296858542.png"},"pcLive.webConfig.canUseDoubleScreen":{"can":true},"enableWebQosLog":false,"enableDetailedWebQosLog":false},"abTest":{}},"$isServerPretch":true},"categoryMask":{"config":[],"list":[],"hotList":[],"hasMore":false,"hasMoreHot":false,"$isServerPretch":true},"historyMask":{"list":[],"$isServerPretch":true},"follow":{"currentFollowStatus":"UN_FOLLOWED","needToFollow":false,"authorId":"","data":3},"liveroom":{"activeIndex":0,"websocketUrls":[],"token":"","noticeList":[{"userId":90041,"userName":"快手平台帐号","userGender":"F","content":"欢迎来到直播间！快手禁止未成年直播或打赏、严禁诱导未成年消费。如发现违法违规、色情低俗、抽烟喝酒等情况请及时举报。如主播以不当方式诱导打赏、私下交易等请谨慎判断，以防人身财产损失。"}],"playList":[{"liveStream":{"id":"CRDvSYhXYC8","poster":"https://p2-pro.a.yximgs.com/uhead/AB/2025/06/30/11/BMjAyNTA2MzAxMTEyNDhfMjAzNzQ4MTY5XzEzNzU5OTgzNzYyX2x2.jpg","playUrls":{"h264":{"hideAuto":false,"autoDefaultSelect":true,"cdnFeature":[],"businessType":0,"freeTrafficCdn":false,"version":"2.0","type":"dynamic","adaptationSet":{"gopDuration":2000,"representation":[{"id":0,"url":"https://tx-origin.pull.yximgs.com/gifshow/CRDvSYhXYC8_ShowAvcHdL0-AuditAvcOriginL3.flv?txSecret=b86bee84ddd3cd0f274de6be93d2e9dd&txTime=68637fea&stat=j7g2JVwDj%2F0ALFP9Uct%2BD5mo1Y4TyTh6Z5pkVOLneZzLdizlSW%2BxKgmS4KCUK%2Fe0&tsc=origin&oidc=watchmen&sidc=2006&no_script=1&srcStrm=CRDvSYhXYC8&fd=0&ss=s20&tfc_buyer=0","bitrate":2000,"qualityType":"HIGH","level":50,"name":"超清","shortName":"超清","hidden":false,"enableAdaptive":true,"defaultSelect":true}]}},"hevc":{}},"url":"https://m.gifshow.com/fw/live/3xtzx7d2tpj4j2s","hlsPlayUrl":"https://ws-origin.hlspull.yximgs.com/gifshow/CRDvSYhXYC8_ShowAvcHdL0-AuditAvcOriginL3.m3u8?wsTime=68637fea&wsSecret=688d7a39db8a812766af25133e7f0c2e&stat=j7g2JVwDj%2F0ALFP9Uct%2BD5mo1Y4TyTh6Z5pkVOLneZzLdizlSW%2BxKgmS4KCUK%2Fe0&tsc=origin&oidc=watchmen&sidc=2006tsc=origin","location":null,"type":"live","liveGuess":false,"expTag":"1_v/0_unknown0","privateLive":false,"dynamicLayoutEnable":false},"author":{"id":"xiaomei870625","name":"小美姐姐625生日","description":"每天中午11点\n晚上8点","avatar":"https://p5-pro.a.yximgs.com/uhead/AB/2021/01/22/22/BMjAyMTAxMjIyMjU2MjJfMjAzNzQ4MTY5XzFfaGQ4NzVfNDA3_s.jpg","sex":"F","living":false,"followStatus":"UN_FOLLOWED","constellation":"","cityName":"","originUserId":*********,"privacy":false,"isNew":false,"timestamp":1751264874508,"verifiedStatus":{"verified":false,"description":"","type":0,"new":false},"bannedStatus":{"banned":false,"socialBanned":false,"isolate":false,"defriend":false},"counts":{"fan":"348.9w","follow":"6627","liked":"19.7w"}},"gameInfo":{"id":"1000024","name":"其他","poster":"http://js-gamezone.static.yximgs.com/bs2/imageGameZone/5xphycykeia8q9q.jpg","description":"快手，拥抱每一种生活。","categoryAbbr":"ZH","categoryName":"综合","roomCount":"0"},"isLiving":true,"authToken":null,"config":{"canSendGift":true,"needLoginToWatchHD":false,"isWorldCup":false,"noticeDisplayDelay":3000,"enableWebHistoryFeed":true},"websocketInfo":{},"status":{"forbiddenState":1}}],"loading":false,"$isServerPretch":true},"emoji":{"iconUrls":{},"allGifts":{},"giftList":[],"giftPanelList":[],"token":"","panelToken":"","longSendGiftType":"bar","$isServerPretch":true},"giftSendStore":{"prePayQuery":{},"payQuery":{},"styleType":"","polling":false,"pollFn":{"startPoll":null,"stopPoll":null},"prePayInput":{"ksCoin":0,"fen":0,"timeStamp":0},"payResult":-1,"rechargeSource":1,"$isServerPretch":true},"feedbackStore":{"$isServerPretch":true},"reportStore":{"reportType":"","reportList":[],"status":"filling","$isServerPretch":true},"followBtn":{"followStatus":"UN_FOLLOWED","$isServerPretch":true},"category":{"simpleCategoryList":{"hotCategoryList":[],"recommendCategoryList":[]},"$isServerPretch":true},"headerSearch":{"searchSuggestQuery":{},"searchHotUserListQuery":[],"localHistory":[],"defaultKeyword":"","$isServerPretch":true},"interestMaskStore":{"list":[],"profileList":[],"$isServerPretch":true}}
                                // console.log(JSON.stringify(initialState));
                                _event.control_msg.enter_live_info = {};
                                initialState.liveroom.playList.forEach((_liveroom) => {
                                    // _liveroom.isLiving === true 正在直播
                                    _event.control_msg.room_id = _liveroom.liveStream.id;
                                    _event.control_msg.room_name = _liveroom.author.name;

                                    _event.control_msg.enter_live_info.room_tital = _liveroom.author.name;
                                    // // https://www.kuaishou.com/profile/3xtzx7d2tpj4j2s
                                    _event.control_msg.enter_live_info.own_user_uniqueId = _liveroom.author.id;
                                    // _event.control_msg.enter_live_info.own_user_sec_uid = _liveroom.author.originUserId;// string number
                                    _event.control_msg.enter_live_info.own_user_nickname = _liveroom.author.name;
                                    _event.control_msg.enter_live_info.own_user_head_url = _liveroom.author.avatar;
                                })
                            }
                            // console.log(JSON.stringify(_event));
                            eventsData.events_data.push(_event);
                        } else {
                            eventsData.events_data.push(packCtlEvent(1002, '没有找到直播间名,' + ',' + params.request.url, liveUrl));
                        }
                    }
                }
                eventCallback(eventsData);
            } else {
                // try {
                //     resBody = Buffer.from(resBody, 'base64');
                //     const fs = require('fs');
                //     fs.appendFileSync('debug_ks_https-'+liveUrl+'.txt',  'surl:'+params.request.url+'\n');
                //     fs.appendFileSync('debug_ks_https-'+liveUrl+'.txt', 'surl_x:\t' +resBody+'\n');
                // } catch (err) {
                //     console.error('同步追加出错:', err);
                // }
            }
            // TODO: 关掉ws是什么连接弹幕
            // console.log(params.request.url);
            // }).catch(function (error) {
            //     this._inline_self_debug(method + 'Err[' + error + ']' + params.requestId + ':' + params.request.url);
            // }).finally(function () {
            // });

            // Start ==================== 校验逻辑
            if (Array.isArray(params.responseHeaders)) {
                for (let i = 0; i < params.responseHeaders.length; i++) {
                    if (params.responseHeaders[i].name.toLowerCase() === atob('ZGF0ZQ=='/* 'date' */)) {
                        const date = params.responseHeaders[i].value;
                        if (date !== undefined) {
                            const serverTimestamp = Date.parse(date);
                            // console.log('time:' + serverTimestamp);
                            // 时间如果超过 2026-01-20 15:09:23 则提示过期
                            if (1768892963000 < serverTimestamp) {
                                // console.log('过期');
                                mainWindow.webContents.debugger.detach();
                                // mainWindow.close();  // 关闭窗口
                            } else {
                                // console.log('ok');
                            }
                        } else {
                            // todo: 没有返回 im-now 的情况先忽略
                        }
                        break
                    }
                }
            } else {
                // 突然断开代理可能会没有返回数据
            }
            // End ==================== 校验逻辑
        }

        mainWindow.webContents.debugger.on(atob('bWVzc2FnZQ=='/* 'message' */), async (event, method, params) => {
            // this._inline_self_debug('info:' + params.requestId+method)
            // this._inline_self_debug('info:'+method + ':' + JSON.stringify(params))
            if (method === atob('RmV0Y2gucmVxdWVzdFBhdXNlZA=='/* 'Fetch.requestPaused' */)) {
                parseUrl(params);

                mainWindow.webContents.debugger.sendCommand(atob('RmV0Y2guY29udGludWVSZXF1ZXN0'/* 'Fetch.continueRequest' */), {requestId: params.requestId})
            } else if (method === atob('TmV0d29yay53ZWJTb2NrZXRGcmFtZVJlY2VpdmVk'/* 'Network.webSocketFrameReceived' */)) {
                // console.log('webSocketFrameReceived:'+params.requestId);
                if (gWebcastObj.hasOwnProperty(params.requestId)) {
                    // params.response.payloadData
                    this._inline_parseKsPayload(params.response.payloadData, liveUrl, eventCallback, logCallback);
                    // console.log(params.response.payloadData);
                    // Buffer.from(params.response.payloadData, 'base64');
                } else {
                    // 可能是其他 ws 协议数据,可以忽略
                    // logCallback('recive warn not found ' + params.requestId);
                }
            } else if (method === atob('TmV0d29yay53ZWJTb2NrZXRDcmVhdGVk'/* 'Network.webSocketCreated' */)) {
                if (params.url.startsWith(atob('d3NzOi8v'/* 'wss://' */))
                    // ? 需要固定吗? socket链接是 live_api/liveroom/websocketinfo 下发的
                    && params.url.includes(atob('L3dlYnNvY2tldA=='/* '/websocket' */))
                ) {
                    gWebcastObj[params.requestId] = params.url; // 提取 roomId
                }
                // console.log('webSocketCreated'+params.requestId+','+params.url);
            } else if (method === atob('TmV0d29yay53ZWJTb2NrZXRDbG9zZWQ='/* 'Network.webSocketClosed' */)) {
                // console.log('webSocketClosed:'+params.requestId);
                delete gWebcastObj[params.requestId];
            }
            // else if (method.includes('webSocket')) {
            //   console.log(method);
            // }
        })

        var _str_response = atob('UmVzcG9uc2U='/* 'Response' */)
        mainWindow.webContents.debugger.sendCommand(atob('RmV0Y2guZW5hYmxl'/* 'Fetch.enable' */), {
            patterns: [
                {
                    urlPattern: '*' + atob('L2xpdmVfYXBpL2xpdmVyb29tL3JlY2FsbA=='/* '/live_api/liveroom/recall' */) + "*",
                    requestStage: _str_response
                },
                {
                    urlPattern: '*' + atob('aHR0cHM6Ly9saXZlLmt1YWlzaG91LmNvbS91Lw=='/* 'https://live.kuaishou.com/u/' */) + "*",
                    requestStage: _str_response
                },
                {
                    urlPattern: '*' + atob('L2xpdmVfYXBpL2Vtb2ppL2FsbGdpZnRz'/* '/live_api/emoji/allgifts' */) + "*",
                    requestStage: _str_response
                }//
            ]
        })
        mainWindow.loadURL('about:blank').then(r => {
        })
        // 必须要有页面才会 enable 成功
        mainWindow.webContents.debugger.sendCommand(atob('TmV0d29yay5lbmFibGU='/* 'Network.enable' */)).then(()=>{
            this._inline_self_debug('enable ok')
            // this._inline_self_debug('to loadURL')
            mainWindow.loadURL(liveUrl).catch((err) => {
                logCallback('Failed to load URL:', err);
            }).finally(()=>{
                // this._inline_self_debug('loadURL finally')
            })
        });
    }

    // tools

    _inline_self_debug(msg) {
        // console.log(msg)
    }

    _inline_parseRecall(body, liveUrl, eventCallback, logCallback) {
        try {
            var jsonRecall = JSON.parse(body);
            // {1: "SC_WEB_COMMENT_FEED", 2: "SC_WEB_COMMENT_RICH_TEXT_MESSAGE"};
            if ("1" in jsonRecall.data.backTraceFeedMap) {
                var historyFeedList = jsonRecall.data.backTraceFeedMap["1"]["historyFeedList"];
                historyFeedList.forEach(historyFeed => {
                    // console.log(historyFeed);
                    var decodeOutPayload = Buffer.from(historyFeed, 'base64');
                    this._inline_decodePayload(decodeOutPayload, kuaishou.PayloadType.SC_WEB_COMMENT_FEED, liveUrl, eventCallback, logCallback);
                })
            }

            if ("2" in jsonRecall.data.backTraceFeedMap) {
                var historyFeedList2 = jsonRecall.data.backTraceFeedMap["2"]["historyFeedList"];
                historyFeedList2.forEach(historyFeed => {
                    // console.log(historyFeed);
                    var decodeOutPayload = Buffer.from(historyFeed, 'base64');
                    this._inline_decodePayload(decodeOutPayload, kuaishou.PayloadType.SC_WEB_COMMENT_RICH_TEXT_MESSAGE, liveUrl, eventCallback, logCallback);
                })
            }
        } catch (e) {
            logCallback("recall json Error:"+body);
        }
    }

    _inline_parseKsPayload(payloadData, liveUrl, eventCallback, logCallback) {
        // 最外层包
        var decodeOutPayload = Buffer.from(payloadData, 'base64');
        const _socketMessage = kuaishou.SocketMessage.decode(decodeOutPayload);
        // 第二层包
        if (_socketMessage.compressionType === kuaishou.SocketMessage.CompressionType.GZIP) {
            var uncompressedData = gunzipSync(_socketMessage.payload);
            // logCallback(_socketMessage.payloadType+', '+_socketMessage.compressionType);
            try {
                this._inline_decodePayload(uncompressedData, _socketMessage.payloadType, liveUrl, eventCallback, logCallback, payloadData);
            } catch (e) {
                logCallback('parseKsPayload ws error:' + e.message);
            }
        } else if (_socketMessage.compressionType === kuaishou.SocketMessage.CompressionType.NONE) {
            // logCallback(_socketMessage.payloadType+', '+_socketMessage.compressionType);
            this._inline_decodePayload(_socketMessage.payload, _socketMessage.payloadType, liveUrl, eventCallback, logCallback, payloadData);
        } else {
            // TODO: 支持更多的压缩类型
            logCallback('unsupported type:'+_socketMessage.compressionType+','+_socketMessage.payload);
        }
    }

    _inline_packUser(user) {
        return {
            user_name: user.principalId,
            nick_name: user.userName,
            head_url: user.headUrl,// remove
        }
    }
    _inline_packCommon(msg_type, msg_type_str) {
        var _eventData = {};
        _eventData.msg_type = msg_type;
        _eventData.msg_type_str = msg_type_str;
        return _eventData;
    }

    _inline_decodePayload(payload, payloadType, liveUrl, eventCallback, logCallback, origin = null) {
        // console.log(payload);
        var eventsData = initEvents(this._inline_context, 'kuaishou');
        if (this._inline_txmll && origin !== null) {
            eventsData.origin = origin;
        }
        switch (payloadType) {
            case kuaishou.PayloadType.SC_ENTER_ROOM_ACK: {// 300
                // 只返回了一个 token,可以忽略. {"token":"\u0010'\u0018\u0001"}
                var _decode = kuaishou.CSWebEnterRoom.decode(payload);
                // console.log(JSON.stringify(_decode));
                break;
            }
            case kuaishou.PayloadType.SC_LIVE_WATCHING_LIST: {// 340
                // 观众列表 {
                //             "user": {
                //                 "principalId": "a29942171",
                //                 "userName": "梨枝白℡",
                //                 "headUrl": "https://p2-pro.a.yximgs.com/uhead/AB/2025/06/08/14/BMjAyNTA2MDgxNDQ4MTRfNjUwNDAxODA4XzJfaGQ4MjRfNDQx_s.jpg"
                //             },
                //             "tuhao": true,
                //             "liveAssistantType": "JUNIOR",
                //             "displayKsCoin": "13"
                //         },
                // _eventData.payload_type = payloadType;
                // _eventData.web_live_watching_users = {};
                // _eventData.web_live_watching_users.watching_user = [];
                // var _decode = kuaishou.SCWebLiveWatchingUsers.decode(payload);
                // _decode.watchingUser.forEach((item) => {
                //     _eventData.web_live_watching_users.watching_user.push({
                //         user: {
                //             principal_id: item.user.principalId,
                //             user_name: item.user.userName,
                //             head_url: item.user.headUrl,
                //         },
                //         offline: item.offline,
                //         tuhao: item.tuhao,
                //         live_assistant_type: item.liveAssistantType,
                //         display_ks_coin: item.displayKsCoin,
                //     });
                // })
                // this._inline_self_debug(userList);
                // console.log(JSON.stringify(_decode));
                break;
            }
            case kuaishou.PayloadType.SC_FEED_PUSH: {// 310
                // 直播间弹幕数据
                var _decode = kuaishou.SCWebFeedPush.decode(payload);
                // console.log(JSON.stringify(_decode));
                this._inline_feedPush(_decode, eventCallback);
                break;
            }
            case kuaishou.PayloadType.SC_HEARTBEAT_ACK: {// 101
                // 心跳, 如: {"timestamp":"1749697176270","clientTimestamp":"1749697176270"}
                var _decode = kuaishou.SCWebHeartbeatAck.decode(payload);
                // console.log(JSON.stringify(_decode));
                break;
            }
            case kuaishou.PayloadType.SC_INFO: {// 105
                // {"code":100}
                var _decode = kuaishou.SCInfo.decode(payload);
                // this._inline_self_debug('SC_INFO:'+JSON.stringify(_decode));
                break;
            }
            case kuaishou.PayloadType.SC_WEB_COMMENT_FEED: {// 111
                var _decode = kuaishou.WebCommentFeed.decode(payload);
                // console.log(JSON.stringify(_decode));
                let _eventData = this._inline_packCommon(2, 'comment_msg');
                _eventData.comment_msg = {};
                _eventData.comment_msg.user = this._inline_packUser(_decode.user);
                _eventData.comment_msg.content = _decode.content;
                if (_decode.senderState && _decode.senderState.wealthGrade) {
                    _eventData.comment_msg.user.level = _decode.senderState.wealthGrade;
                    if (_decode.senderState.liveFansGroupState) {
                        _eventData.comment_msg.user.fansclub = {};
                        // 这个等级不知道是什么意思 _decode.senderState.fansGroupIntimacyLevel
                        _eventData.comment_msg.user.fansclub.level = _decode.senderState.liveFansGroupState.intimacyLevel;
                    }
                }
                eventsData.events_data.push(_eventData);
                // if (_decode.senderState) {
                //     _eventData.web_comment_feed.sender_state = {
                //         assistant_type: _decode.senderState.assistantType,
                //         fans_group_intimacy_level: _decode.senderState.fansGroupIntimacyLevel,
                //         wealth_grade: _decode.senderState.wealthGrade
                //     };
                //     if (_decode.senderState.liveFansGroupState) {
                //         _eventData.web_comment_feed.sender_state.live_fans_group_state = {
                //             intimacy_level: _decode.senderState.liveFansGroupState.intimacyLevel,
                //             enter_room_special_effect: _decode.senderState.liveFansGroupState.enterRoomSpecialEffect,
                //         }
                //     }
                // }
                // this._inline_self_debug(_decode.user.userName+' 说: ' + _decode.content);
                break;
            }
            case kuaishou.PayloadType.SC_WEB_COMMENT_RICH_TEXT_MESSAGE: {// 112
                // var _decode = kuaishou.CommentRichTextMessage.decode(payload);
                // console.log(JSON.stringify(_decode));
                break;
            }
            case kuaishou.PayloadType.SC_INTERACTIVE_CHAT_SWITCH_BIZ: {// 944
                // {"roomInfo":{"bizIdentity":"1","version":"6"},"timestamp":"1749709368351","version":"6"}
                // var _decode = kuaishou.SCInteractiveChatSwitchBiz.decode(payload);
                // console.log(JSON.stringify(_decode));
                break;
            }
            case kuaishou.PayloadType.SC_INTERACTIVE_CHAT_CLOSED: {// 776
                // 关闭互动聊天
                // {"bizIdentity":{"bizType":"1","chatId":"h7LaemFv-c0","bizId":"6SVuPowDs0c"},"userInfo":{"userId":"4654586197","userName":"斯斯•桂哥🖤","userGender":"M","headUrls":[{"cdn":"p4-pro.a.yximgs.com","url":"https://p4-pro.a.yximgs.com/uhead/AB/2025/06/07/22/BMjAyNTA2MDcyMjAwMjdfNDY1NDU4NjE5N18xX2hkNjQ3XzQyMA==_s.jpg"}]},"timestamp":"1749710736240"}
                // var _decode = kuaishou.SCInteractiveChatClosed.decode(payload);
                // console.log('776,'+JSON.stringify(_decode));
                break;
            }
            case kuaishou.PayloadType.SC_COMMENT_ZONE_RICH_TEXT: {// 829
                // {"message":[{"serverTimestamp":"1749716897295","segment":[{"textSegment":{"text":"wzh","textStyle":{"color":4294957683}}},{"textSegment":{"text":"送出了","textStyle":{"color":4294967295}}},{"textSegment":{"text":"1","textStyle":{"color":4294957683}}},{"textSegment":{"text":"个","textStyle":{"color":4294967295}}},{"textSegment":{"text":"幸运神器","textStyle":{"color":4294957683}}},{"textSegment":{"text":"，开出了","textStyle":{"color":4294967295}}},{"textSegment":{"text":"1","textStyle":{"color":4294957683}}},{"textSegment":{"text":"个","textStyle":{"color":4294967295}}},{"textSegment":{"text":"盛典票","textStyle":{"color":4294957683}}},{"textSegment":{"text":"，热度值+","textStyle":{"color":4294967295}}},{"textSegment":{"text":"0.1","textStyle":{"color":4294957683}}},{"iconSegment":{"resPackId":"4618746","iconName":"live_try_luck.png"}}]}]}
                // var _decode = kuaishou.SCCommentZoneRichText.decode(payload);
                // var _rich_text = "";
                // _decode.message.forEach(msg => {
                //     msg.segment.forEach((item) => {
                //         if (item.textSegment) {
                //             _rich_text += item.textSegment.text;
                //         }
                //         if (item.iconSegment) {
                //             _rich_text += item.iconSegment.iconName;
                //         }
                //         if (item.giftSegment) {
                //             _rich_text += item.giftSegment.giftId;
                //         }
                //         if (item.imageSegment && item.imageSegment.url.length !== 0) {
                //             _rich_text += item.imageSegment.url[0].url
                //         }
                //     })
                // });
                // console.log('富文本:'+_rich_text);
                // console.log('829,'+JSON.stringify(_decode));
                break;
            }
            case kuaishou.PayloadType.SC_ERROR: {    // 103
                // {"code":601,"msg":"直播已停止"}
                let _eventData = this._inline_packCommon(5, 'control_msg');
                _eventData.control_msg = {};
                var _decode = kuaishou.SCWebError.decode(payload);
                if (_decode.code === 601) {
                    _eventData.control_msg.action = 1;
                    _eventData.control_msg.msg = _decode.msg;
                } else {
                    _eventData.control_msg.action = 101;
                    _eventData.control_msg.msg = JSON.stringify(_decode);
                }
                eventsData.events_data.push(_eventData);
                // this._inline_self_debug('SCWebError:'+JSON.stringify(_decode));
                break;
            }
            //
            case kuaishou.PayloadType.SC_LIVE_MULTI_PK_STATISTIC: {// 950
                // 950,{"pkId":"OxCx3n-rO-I","time":"1749721798145","statisticVersion":"1"} 2025-06-12 17:50:00
                // 950,{"pkId":"OxCx3n-rO-I","time":"1749721815987","statisticVersion":"10"} 2025-06-12 17:50:15
                // 950,{"pkId":"OxCx3n-rO-I","time":"1749721835819","statisticVersion":"20"} 2025-06-12 17:50:35
                // 950,{"pkId":"OxCx3n-rO-I","time":"1749721996218","statisticVersion":"100"} 2025-06-12 17:53:16
                // var _decode = kuaishou.SCLiveMultiPkStatistic.decode(payload);
                // this._inline_self_debug('950,'+JSON.stringify(_decode));
                break;
            }
            case 943:
            case 953:
            case 510: {// 不知道什么含义，比较多，是不是进入直播间的消息
                // console.log(payloadType,':',JSON.stringify(_decode));
                break;
            }
            default: {
                // TODO:
                this._inline_self_debug('unsupported payloadType:'+payloadType);
                break
            }
        }

        if (eventCallback !== null) {
            if (eventsData.events_data.length !== 0 || eventsData.origin !== undefined) {
                eventCallback(eventsData);
            }
        }
    }

    _inline_feedPush(payload, eventCallback) {
        var eventsData = initEvents(this._inline_context, 'kuaishou');
        eventsData.watching_total = payload.displayWatchingCount;
        eventsData.like_total = payload.displayLikeCount;

        // 评论：
        payload.commentFeeds.forEach(comment => {
            // {"user":{"principalId":"3x9eddibxcdpmha","userName":"丁元英"},"content":"22222222","deviceHash":"a7EKcw==","showType":"FEED_SHOW_NORMAL","senderState":{"fansGroupIntimacyLevel":3,"liveFansGroupState":{"intimacyLevel":11,"enterRoomSpecialEffect":1},"wealthGrade":50}}
            // console.log(JSON.stringify(comment))
            let _eventData = this._inline_packCommon(2, 'comment_msg');
            _eventData.comment_msg = {};
            _eventData.comment_msg.user = this._inline_packUser(comment.user);
            _eventData.comment_msg.content = comment.content;
            if (comment.senderState && comment.senderState.wealthGrade) {
                _eventData.comment_msg.user.level = comment.senderState.wealthGrade;
                if (comment.senderState.liveFansGroupState) {
                    _eventData.comment_msg.user.fansclub = {};
                    // 这个等级不知道是什么意思 comment.senderState.fansGroupIntimacyLevel
                    _eventData.comment_msg.user.fansclub.level = comment.senderState.liveFansGroupState.intimacyLevel;
                }
            }
            eventsData.events_data.push(_eventData);
        })

        // TODO: 热门评论
        // payload.comboCommentFeed.forEach(comboComment => {
        //     _eventData.combo_comment_feed.push({
        //         content: comboComment.content,
        //         combo_count: comboComment.comboCount
        //     })
        // })

        // 点赞消息
        payload.likeFeeds.forEach(likeFeed => {
            // 如: {"user":{"principalId":"3xj6qzv37b7z5gc","userName":"钳🔥钳"},"deviceHash":"3Pp0gA=="}
            let _eventData = this._inline_packCommon(1, 'like_msg');
            _eventData.like_msg = {};
            _eventData.like_msg.user = this._inline_packUser(likeFeed.user);
            _eventData.like_msg.like_count = 1;  // 默认1
            eventsData.events_data.push(_eventData);
        })

        // 送礼
        payload.giftFeeds.forEach(giftFeed => {
            // 没有显示送给指定用户，移动端支持的。
            // {"user":{"principalId":"li-0080528","userName":"ㅤ    小松","headUrl":"https://p5-pro.a.yximgs.com/uhead/AB/2025/06/10/17/BMjAyNTA2MTAxNzQ5MDdfMTk3ODA2NTc2Ml8xX2hkNzU4XzQ1OA==_s.jpg"},"giftId":199,"mergeKey":"1978065762-**********-1","batchSize":1,"comboCount":1,"rank":2,"expireDuration":"300000","slotDisplayDuration":"1000","deviceHash":"RCNM5g=="}
            // {"user":{"principalId":"LigongZi666a","userName":"♀℡李公子♪","headUrl":"https://p2-pro.a.yximgs.com/uhead/AB/2018/08/01/16/BMjAxODA4MDExNjQ0MTZfMzExMDAxNjUxXzJfaGQ2ODhfMTkx_s.jpg"},"giftId":11500,"mergeKey":"*********-753686-11500-80","batchSize":80,"comboCount":1,"rank":1,"expireDuration":"300000","styleType":"BATCH_STAR_0","deviceHash":"MrcLdA=="}
            // console.log(JSON.stringify(giftFeed));
            // if (giftFeed.batchSize !== 1) {// giftFeed.batchSize 一份多少个礼物
            //     console.log(giftFeed.user.userName + ' 送出:' + getGiftName(giftFeed.giftId)
            //         + 'x' + giftFeed.comboCount + '*' + giftFeed.batchSize);//  +',rank:'+giftFeed.rank + ',batch:' + giftFeed.batchSize
            // } else {
            //     console.log(giftFeed.user.userName + ' 送出:' + getGiftName(giftFeed.giftId)
            //         + 'x' + giftFeed.comboCount);
            // }

            var local_gift = this._inline_getGift(giftFeed.mergeKey);
            var cur_count = giftFeed.comboCount*giftFeed.batchSize;
            if (local_gift === null) {
                // 初始化
                this._inline_addOrUpdateGift(giftFeed.mergeKey, cur_count);
            } else {
                cur_count = this._inline_decrementGift(giftFeed.mergeKey, cur_count);
            }
            let _eventData = this._inline_packCommon(3, 'gift_msg');
            _eventData.gift_msg = {};
            _eventData.gift_msg.user = this._inline_packUser(giftFeed.user);
            _eventData.gift_msg.gift_id = giftFeed.giftId;
            _eventData.gift_msg.gift_name = this._inline_getGiftName(giftFeed.giftId);
            _eventData.gift_msg.gift_url = this._inline_getGiftUrl(giftFeed.giftId);
            _eventData.gift_msg.gift_price = 0;
            _eventData.gift_msg.batch_size = giftFeed.batchSize;
            _eventData.gift_msg.total_count = giftFeed.comboCount;
            _eventData.gift_msg.count = cur_count;
            _eventData.gift_msg.gift_msg_key = giftFeed.mergeKey;
            eventsData.events_data.push(_eventData);
            // TODO:
            // giftFeed.liveAssistantType,
        })

        // // TODO: 系统提示
        // payload.systemNoticeFeeds.forEach(systemNoticeFeed => {
        //     console.log(JSON.stringify(systemNoticeFeed));
        // })
        //
        // // TODO: 分享
        // payload.shareFeeds.forEach(shareFeed => {
        //     console.log(JSON.stringify(shareFeed));
        // })
        if (eventCallback) {
            eventCallback(eventsData);
        }
    }

    _inline_getGiftName(giftId) {
        try {
            if (this._inline_allGiftsJson !== null) {
                return this._inline_allGiftsJson["data"][giftId].giftName;
            } else {
                return giftId;
            }
        } catch (e) {
            return giftId;
        }
    }
    _inline_getGiftUrl(giftId) {
        if (this._inline_allGiftsJson !== null) {
            return this._inline_allGiftsJson["data"][giftId].giftUrl;
        } else {
            return giftId;
        }
    }
    // 测试
    testParseRaw(payload, live_url, eventCallback, logCallback) {
        if (this._inline_test_hex && payload !== undefined) {
            this._inline_parseKsPayload(payload, live_url, eventCallback, logCallback);
        }
    }
}

/// @secureEnd

module.exports = LiveKuaishou;
// export default startMonitorRoomMsg;

