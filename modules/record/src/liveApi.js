
// 方式1:

class LiveApi {
    constructor(context='uuid-1234', test=false) {
        this.context = context;
        this.test = test;
    }
    startMonitorRoom(mainWindow, liveUrl, eventCallback, logCallback) {
        if (liveUrl.includes('douyin.com/')) {
            const LiveDouyin = require("./douyin");
            this.liveObject = new LiveDouyin(this.context, this.test);
        } else if (liveUrl.includes('kuaishou.com/')) {
            const LiveKuaishou = require("./kuaishou");
            this.liveObject = new LiveKuaishou(this.context, this.test);
        } else if (liveUrl.includes('tiktok.com/')) {
            const LiveTiktok = require("./tiktok");
            this.liveObject = new LiveTiktok(this.context, this.test);
        } else if (liveUrl === 'weixin') {
            const LiveWeixin = require("./weixin");
            this.liveObject = new LiveWeixin(this.context, this.test);
        } else {
            this.liveObject = null;
            logCallback('unsupported url:'+liveUrl);
            return
        }
        this.liveObject.startMonitorRoom(mainWindow, liveUrl, eventCallback, logCallback);
    }

    release() {
        if (this.liveObject) {
            this.liveObject.release();
            this.liveObject = null;
        }
    }
}

// 方式2
function createLiveObject(live_name, context='uuid-1234', test=false) {
    let liveObject = null;
    if (live_name === 'douyin') {
        const LiveDouyin = require("./douyin");
        liveObject = new LiveDouyin(context, test);
    } else if (live_name === 'kuaishou') {
        const LiveKuaishou = require("./kuaishou");
        liveObject = new LiveKuaishou(context, test);
    } else if (live_name === 'tiktok') {
        const LiveTiktok = require("./tiktok");
        liveObject = new LiveTiktok(context, test);
    } else if (live_name === 'weixin') {
        const LiveWeixin = require("./weixin");
        liveObject = new LiveWeixin(context, test);
    }
    return liveObject;
}

module.exports = {
    createLiveObject:createLiveObject,
    LiveApi: LiveApi,
}