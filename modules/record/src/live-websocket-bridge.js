const { LiveApi } = require('./liveApi');
const WebSocketServer = require('./websocket-server');
const crypto = require('crypto');
const { BrowserWindow } = require('electron');

// 窗口管理器类
class WindowManager {
    constructor() {
        this.windows = new Map(); // connectionId -> BrowserWindow
        this.windowOptions = {
            width: 1200,
            height: 800,
            show: false, // 默认隐藏窗口，只用于数据监听
        };
    }

    // 创建新窗口
    createWindow(connectionId, liveUrl) {
        const window = new BrowserWindow({
            ...this.windowOptions,
            title: `LiveApi Monitor - ${liveUrl}`
        });

        // 存储窗口引用
        this.windows.set(connectionId, window);

        // 监听窗口关闭事件
        window.on('closed', () => {
            this.windows.delete(connectionId);
            console.log(`Window for connection ${connectionId} closed`);
        });

        // 加载空白页面
        window.loadURL('about:blank');

        console.log(`Created window for connection ${connectionId}: ${liveUrl}`);
        return window;
    }

    // 获取窗口
    getWindow(connectionId) {
        return this.windows.get(connectionId);
    }

    // 关闭窗口
    closeWindow(connectionId) {
        const window = this.windows.get(connectionId);
        if (window && !window.isDestroyed()) {
            window.close();
        }
        this.windows.delete(connectionId);
    }

    // 关闭所有窗口
    closeAllWindows() {
        for (const [connectionId, window] of this.windows) {
            if (!window.isDestroyed()) {
                window.close();
            }
        }
        this.windows.clear();
    }

    // 获取窗口数量
    getWindowCount() {
        return this.windows.size;
    }

    // 获取所有窗口信息
    getAllWindowsInfo() {
        const windowsInfo = [];
        for (const [connectionId, window] of this.windows) {
            if (!window.isDestroyed()) {
                windowsInfo.push({
                    connectionId: connectionId,
                    title: window.getTitle(),
                    isVisible: window.isVisible(),
                    isMinimized: window.isMinimized(),
                    bounds: window.getBounds()
                });
            }
        }
        return windowsInfo;
    }

    // 设置窗口可见性
    setWindowVisibility(connectionId, visible) {
        const window = this.windows.get(connectionId);
        if (window && !window.isDestroyed()) {
            if (visible) {
                window.show();
            } else {
                window.hide();
            }
        }
    }
}

class LiveWebSocketBridge {
    constructor(context = 'uuid-1234', wsPort = 8080, test = false) {
        this.context = context;
        this.wsPort = wsPort;
        this.test = test;

        // 创建WebSocket服务器
        this.wsServer = new WebSocketServer(wsPort);

        // 绑定事件处理器
        this.setupEventHandlers();

        // 多个直播间连接管理
        this.connections = new Map(); // connectionId -> connection info
        this.windowManager = new WindowManager(); // 窗口管理器

        // 全局统计信息
        this.globalStats = {
            totalConnections: 0,
            activeConnections: 0,
            totalEvents: 0,
            eventsByType: {},
            startTime: null,
            lastEventTime: null
        };
    }

    // 生成连接ID
    generateConnectionId() {
        return crypto.randomBytes(8).toString('hex');
    }

    setupEventHandlers() {
        // WebSocket服务器事件
        this.wsServer.on('started', (port) => {
            console.log(`LiveWebSocketBridge: WebSocket server started on port ${port}`);
        });

        this.wsServer.on('stopped', () => {
            console.log('LiveWebSocketBridge: WebSocket server stopped');
        });

        // 处理客户端请求连接直播间
        this.wsServer.on('connect_live_request', async (request) => {
            await this.handleConnectLiveRequest(request);
        });

        // 处理客户端请求断开直播间连接
        this.wsServer.on('disconnect_live_request', (request) => {
            this.handleDisconnectLiveRequest(request);
        });

        // 处理客户端请求获取状态
        this.wsServer.on('get_status_request', (request) => {
            this.handleGetStatusRequest(request);
        });

        // 处理客户端请求获取所有连接列表
        this.wsServer.on('list_connections_request', (request) => {
            this.handleListConnectionsRequest(request);
        });

        // 处理客户端请求断开特定连接
        this.wsServer.on('disconnect_connection_request', (request) => {
            this.handleDisconnectConnectionRequest(request);
        });

        // 处理客户端请求显示/隐藏窗口
        this.wsServer.on('toggle_window_request', (request) => {
            this.handleToggleWindowRequest(request);
        });

        // 处理客户端请求获取窗口信息
        this.wsServer.on('get_windows_info_request', (request) => {
            this.handleGetWindowsInfoRequest(request);
        });
    }

    // 启动WebSocket服务器
    startWebSocketServer() {
        this.wsServer.start();
    }

    // 停止WebSocket服务器
    stopWebSocketServer() {
        this.wsServer.stop();
    }

    // 处理客户端连接直播间请求
    async handleConnectLiveRequest(request) {
        const {ws, liveUrl, options} = request;

        try {
            // 验证直播间URL
            if (!liveUrl || typeof liveUrl !== 'string') {
                this.wsServer.sendToClient(ws, {
                    type: 'connect_live_response',
                    success: false,
                    error: 'Invalid live URL',
                    timestamp: new Date().getTime()
                });
                return;
            }

            // 检查是否已经连接了相同的直播间
            for (const [connectionId, connection] of this.connections) {
                if (connection.liveUrl === liveUrl) {
                    this.wsServer.sendToClient(ws, {
                        type: 'connect_live_response',
                        success: false,
                        error: `Live room already connected with ID: ${connectionId}`,
                        connectionId: connectionId,
                        timestamp: new Date().getTime()
                    });
                    return;
                }
            }

            // 为每个直播间创建独立窗口，不需要检查主窗口

            // 创建新连接
            const connectionId = this.generateConnectionId();
            const connection = await this.createLiveConnection(connectionId, liveUrl, options);

            // 发送成功响应
            this.wsServer.sendToClient(ws, {
                type: 'connect_live_response',
                success: true,
                connectionId: connectionId,
                liveUrl: liveUrl,
                timestamp: new Date().getTime()
            });

            // 广播连接状态变化
            this.wsServer.broadcast({
                type: 'live_connection_added',
                connectionId: connectionId,
                liveUrl: liveUrl,
                totalConnections: this.connections.size,
                timestamp: new Date().getTime(),
                source: 'bridge'
            });

        } catch (error) {
            console.error('Error connecting to live room:', error);
            this.wsServer.sendToClient(ws, {
                type: 'connect_live_response',
                success: false,
                error: error.message,
                timestamp: new Date().getTime()
            });
        }
    }

    // 处理客户端断开直播间请求
    handleDisconnectLiveRequest(request) {
        const { ws, connectionId } = request;

        try {
            if (connectionId) {
                // 断开特定连接
                if (!this.connections.has(connectionId)) {
                    this.wsServer.sendToClient(ws, {
                        type: 'disconnect_live_response',
                        success: false,
                        error: `Connection ${connectionId} not found`,
                        timestamp: new Date().getTime()
                    });
                    return;
                }

                this.disconnectConnection(connectionId);

                this.wsServer.sendToClient(ws, {
                    type: 'disconnect_live_response',
                    success: true,
                    connectionId: connectionId,
                    timestamp: new Date().getTime()
                });

            } else {
                // 断开所有连接
                if (this.connections.size === 0) {
                    this.wsServer.sendToClient(ws, {
                        type: 'disconnect_live_response',
                        success: false,
                        error: 'No live rooms are currently connected',
                        timestamp: new Date().getTime()
                    });
                    return;
                }

                const disconnectedCount = this.disconnectAllConnections();

                this.wsServer.sendToClient(ws, {
                    type: 'disconnect_live_response',
                    success: true,
                    disconnectedCount: disconnectedCount,
                    timestamp: new Date().getTime()
                });
            }

        } catch (error) {
            console.error('Error disconnecting from live room:', error);
            this.wsServer.sendToClient(ws, {
                type: 'disconnect_live_response',
                success: false,
                error: error.message,
                timestamp: new Date().getTime()
            });
        }
    }

    // 处理客户端获取状态请求
    handleGetStatusRequest(request) {
        const { ws } = request;

        const connections = Array.from(this.connections.entries()).map(([id, conn]) => ({
            id: id,
            liveUrl: conn.liveUrl,
            isConnected: conn.isConnected,
            startTime: conn.startTime,
            uptime: conn.startTime ? new Date().getTime() - conn.startTime : 0,
            stats: conn.stats
        }));

        const status = {
            bridge: {
                totalConnections: this.globalStats.totalConnections,
                activeConnections: this.globalStats.activeConnections,
                connections: connections
            },
            websocket: this.wsServer.getStatus(),
            globalStats: this.getGlobalStats()
        };

        this.wsServer.sendToClient(ws, {
            type: 'status_response',
            status: status,
            timestamp: new Date().getTime()
        });
    }

    // 处理客户端获取连接列表请求
    handleListConnectionsRequest(request) {
        const { ws } = request;

        const connections = Array.from(this.connections.entries()).map(([id, conn]) => ({
            id: id,
            liveUrl: conn.liveUrl,
            isConnected: conn.isConnected,
            startTime: conn.startTime,
            uptime: conn.startTime ? new Date().getTime() - conn.startTime : 0,
            eventCount: conn.stats.totalEvents,
            lastEventTime: conn.stats.lastEventTime
        }));

        this.wsServer.sendToClient(ws, {
            type: 'connections_list_response',
            connections: connections,
            totalCount: connections.length,
            timestamp: new Date().getTime()
        });
    }

    // 处理客户端断开特定连接请求
    handleDisconnectConnectionRequest(request) {
        const { ws, connectionId } = request;

        try {
            if (!connectionId) {
                this.wsServer.sendToClient(ws, {
                    type: 'disconnect_connection_response',
                    success: false,
                    error: 'Connection ID is required',
                    timestamp: new Date().getTime()
                });
                return;
            }

            if (!this.connections.has(connectionId)) {
                this.wsServer.sendToClient(ws, {
                    type: 'disconnect_connection_response',
                    success: false,
                    error: `Connection ${connectionId} not found`,
                    timestamp: new Date().getTime()
                });
                return;
            }

            const connection = this.connections.get(connectionId);
            this.disconnectConnection(connectionId);

            this.wsServer.sendToClient(ws, {
                type: 'disconnect_connection_response',
                success: true,
                connectionId: connectionId,
                liveUrl: connection.liveUrl,
                timestamp: new Date().getTime()
            });

        } catch (error) {
            console.error('Error disconnecting specific connection:', error);
            this.wsServer.sendToClient(ws, {
                type: 'disconnect_connection_response',
                success: false,
                error: error.message,
                timestamp: new Date().getTime()
            });
        }
    }

    // 处理客户端切换窗口显示请求
    handleToggleWindowRequest(request) {
        const { ws, connectionId, visible } = request;

        try {
            if (!connectionId) {
                this.wsServer.sendToClient(ws, {
                    type: 'toggle_window_response',
                    success: false,
                    error: 'Connection ID is required',
                    timestamp: new Date().getTime()
                });
                return;
            }

            if (!this.connections.has(connectionId)) {
                this.wsServer.sendToClient(ws, {
                    type: 'toggle_window_response',
                    success: false,
                    error: `Connection ${connectionId} not found`,
                    timestamp: new Date().getTime()
                });
                return;
            }

            this.windowManager.setWindowVisibility(connectionId, visible);

            this.wsServer.sendToClient(ws, {
                type: 'toggle_window_response',
                success: true,
                connectionId: connectionId,
                visible: visible,
                timestamp: new Date().getTime()
            });

        } catch (error) {
            console.error('Error toggling window visibility:', error);
            this.wsServer.sendToClient(ws, {
                type: 'toggle_window_response',
                success: false,
                error: error.message,
                timestamp: new Date().getTime()
            });
        }
    }

    // 处理客户端获取窗口信息请求
    handleGetWindowsInfoRequest(request) {
        const { ws } = request;

        try {
            const windowsInfo = this.windowManager.getAllWindowsInfo();

            this.wsServer.sendToClient(ws, {
                type: 'windows_info_response',
                windowsInfo: windowsInfo,
                totalWindows: windowsInfo.length,
                timestamp: new Date().getTime()
            });

        } catch (error) {
            console.error('Error getting windows info:', error);
            this.wsServer.sendToClient(ws, {
                type: 'windows_info_response',
                success: false,
                error: error.message,
                timestamp: new Date().getTime()
            });
        }
    }

    // 创建直播间连接（为每个直播间创建独立窗口）
    async createLiveConnection(connectionId, liveUrl, options = {}) {
        // 为这个连接创建独立的窗口
        const window = this.windowManager.createWindow(connectionId, liveUrl);

        const connection = {
            id: connectionId,
            liveUrl: liveUrl,
            window: window,
            liveApi: new LiveApi(`${this.context}-${connectionId}`, this.test),
            isConnected: false,
            startTime: null,
            stats: {
                totalEvents: 0,
                eventsByType: {},
                lastEventTime: null
            },
            options: options
        };

        // 创建事件回调函数
        const eventCallback = (eventsData) => {
            try {
                // 更新连接统计信息
                this.updateConnectionStats(connectionId, eventsData);

                // 更新全局统计信息
                this.updateGlobalStats(eventsData);

                // 通过WebSocket广播事件数据，包含连接ID
                this.wsServer.broadcast({
                    type: 'live_events',
                    connectionId: connectionId,
                    liveUrl: liveUrl,
                    data: eventsData,
                    timestamp: new Date().getTime(),
                    source: 'liveApi'
                });

            } catch (error) {
                console.error(`Error in eventCallback for connection ${connectionId}:`, error);
                this.broadcastLog(`Error processing events for ${liveUrl}: ${error.message}`);
            }
        };

        // 创建日志回调函数
        const logCallback = (info) => {
            const logMessage = `[${connectionId}] ${liveUrl}: ${info}`;
            console.log(`LiveApi Log: ${logMessage}`);

            // 通过WebSocket发送日志信息
            this.wsServer.broadcast({
                type: 'log',
                connectionId: connectionId,
                liveUrl: liveUrl,
                message: logMessage,
                timestamp: new Date().getTime(),
                source: 'liveApi'
            });
        };

        // 启动LiveApi监控，使用为此连接创建的独立窗口
        connection.liveApi.startMonitorRoom(window, liveUrl, eventCallback, logCallback);
        connection.isConnected = true;
        connection.startTime = new Date().getTime();

        // 存储连接
        this.connections.set(connectionId, connection);
        this.globalStats.totalConnections++;
        this.globalStats.activeConnections = this.connections.size;

        console.log(`LiveWebSocketBridge: Started monitoring ${liveUrl} with connection ID: ${connectionId}`);

        return connection;
    }

    // 断开特定连接
    disconnectConnection(connectionId) {
        const connection = this.connections.get(connectionId);
        if (!connection) {
            return false;
        }

        console.log(`LiveWebSocketBridge: Disconnecting connection ${connectionId} (${connection.liveUrl})`);

        // 释放LiveApi资源
        if (connection.liveApi) {
            connection.liveApi.release();
        }

        // 关闭对应的窗口
        this.windowManager.closeWindow(connectionId);

        // 从连接池中移除
        this.connections.delete(connectionId);
        this.globalStats.activeConnections = this.connections.size;

        // 广播连接移除事件
        this.wsServer.broadcast({
            type: 'live_connection_removed',
            connectionId: connectionId,
            liveUrl: connection.liveUrl,
            totalConnections: this.connections.size,
            timestamp: new Date().getTime(),
            source: 'bridge'
        });

        console.log(`LiveWebSocketBridge: Connection ${connectionId} disconnected`);
        return true;
    }

    // 断开所有连接
    disconnectAllConnections() {
        const connectionIds = Array.from(this.connections.keys());
        let disconnectedCount = 0;

        for (const connectionId of connectionIds) {
            if (this.disconnectConnection(connectionId)) {
                disconnectedCount++;
            }
        }

        // 广播所有连接断开事件
        this.wsServer.broadcast({
            type: 'all_connections_disconnected',
            disconnectedCount: disconnectedCount,
            timestamp: new Date().getTime(),
            source: 'bridge'
        });

        return disconnectedCount;
    }

    // 更新连接统计信息
    updateConnectionStats(connectionId, eventsData) {
        const connection = this.connections.get(connectionId);
        if (!connection) return;

        connection.stats.totalEvents++;
        connection.stats.lastEventTime = new Date().getTime();

        if (eventsData && eventsData.events_data && Array.isArray(eventsData.events_data)) {
            eventsData.events_data.forEach(event => {
                const eventType = event.msg_type_str || 'unknown';
                connection.stats.eventsByType[eventType] = (connection.stats.eventsByType[eventType] || 0) + 1;
            });
        }
    }

    // 更新全局统计信息
    updateGlobalStats(eventsData) {
        this.globalStats.totalEvents++;
        this.globalStats.lastEventTime = new Date().getTime();

        if (eventsData && eventsData.events_data && Array.isArray(eventsData.events_data)) {
            eventsData.events_data.forEach(event => {
                const eventType = event.msg_type_str || 'unknown';
                this.globalStats.eventsByType[eventType] = (this.globalStats.eventsByType[eventType] || 0) + 1;
            });
        }
    }

    // 广播日志消息
    broadcastLog(message) {
        this.wsServer.broadcast({
            type: 'log',
            message: message,
            timestamp: new Date().getTime(),
            source: 'bridge'
        });
    }



    // 开始监控直播间并启动WebSocket服务器（保持向后兼容，但现在会创建独立窗口）
    startMonitorRoom(mainWindow, liveUrl, additionalCallback = null, additionalLogCallback = null) {
        // 启动WebSocket服务器
        this.startWebSocketServer();

        // 初始化全局统计信息
        this.globalStats.startTime = new Date().getTime();

        // 如果提供了liveUrl，直接连接（会创建独立窗口，忽略传入的mainWindow）
        if (liveUrl) {
            const connectionId = this.generateConnectionId();
            this.createLiveConnection(connectionId, liveUrl, {
                additionalCallback: additionalCallback,
                additionalLogCallback: additionalLogCallback
            });
            console.log(`LiveWebSocketBridge: Created connection ${connectionId} for ${liveUrl} with independent window`);
        }

        console.log(`LiveWebSocketBridge: WebSocket server available on ws://localhost:${this.wsPort}`);
        console.log('LiveWebSocketBridge: Ready to accept live room connection requests from clients');
        console.log('LiveWebSocketBridge: Note: Each live room connection will create an independent Electron window');
    }

    // 启动WebSocket服务器（每个直播间将创建独立窗口）
    startServer() {
        // 启动WebSocket服务器
        this.startWebSocketServer();

        // 初始化全局统计信息
        this.globalStats.startTime = new Date().getTime();

        console.log(`LiveWebSocketBridge: WebSocket server started on ws://localhost:${this.wsPort}`);
        console.log('LiveWebSocketBridge: Ready to accept multiple live room connections');
        console.log('LiveWebSocketBridge: Each live room will create an independent Electron window');
        console.log('LiveWebSocketBridge: Supported commands:');
        console.log('  - connect_live: Connect to a live room (creates new window)');
        console.log('  - disconnect_live: Disconnect from live room(s) (closes windows)');
        console.log('  - list_connections: List all active connections');
        console.log('  - get_status: Get detailed status information');
        console.log('  - toggle_window: Show/hide specific window');
        console.log('  - get_windows_info: Get information about all windows');
    }

    // 获取全局统计信息
    getGlobalStats() {
        const now = new Date().getTime();
        const uptime = this.globalStats.startTime ? now - this.globalStats.startTime : 0;

        return {
            ...this.globalStats,
            uptime: uptime,
            wsServerStatus: this.wsServer.getStatus()
        };
    }

    // 获取所有连接的统计信息
    getAllConnectionsStats() {
        const connections = {};
        for (const [connectionId, connection] of this.connections) {
            const uptime = connection.startTime ? new Date().getTime() - connection.startTime : 0;
            connections[connectionId] = {
                ...connection.stats,
                liveUrl: connection.liveUrl,
                uptime: uptime,
                isConnected: connection.isConnected
            };
        }
        return connections;
    }

    // 广播统计信息
    broadcastStats() {
        const globalStats = this.getGlobalStats();
        const connectionsStats = this.getAllConnectionsStats();

        this.wsServer.broadcast({
            type: 'stats',
            data: {
                global: globalStats,
                connections: connectionsStats,
                summary: {
                    totalConnections: this.connections.size,
                    totalEvents: globalStats.totalEvents,
                    uptime: globalStats.uptime
                }
            },
            timestamp: new Date().getTime(),
            source: 'bridge'
        });
    }

    // 释放资源
    release() {
        console.log('LiveWebSocketBridge: Releasing resources...');

        // 发送关闭通知
        this.wsServer.broadcast({
            type: 'shutdown',
            message: 'LiveWebSocketBridge is shutting down',
            totalConnections: this.connections.size,
            timestamp: new Date().getTime(),
            source: 'bridge'
        });

        // 断开所有直播间连接
        this.disconnectAllConnections();

        // 关闭所有窗口
        this.windowManager.closeAllWindows();

        // 停止WebSocket服务器
        this.stopWebSocketServer();

        console.log('LiveWebSocketBridge: Resources released');
    }

    // 发送自定义消息到所有客户端
    broadcast(data) {
        this.wsServer.broadcast({
            type: 'custom',
            data: data,
            timestamp: new Date().getTime(),
            source: 'bridge'
        });
    }

    // 获取WebSocket服务器状态
    getWebSocketStatus() {
        return this.wsServer.getStatus();
    }
}

module.exports = LiveWebSocketBridge;
