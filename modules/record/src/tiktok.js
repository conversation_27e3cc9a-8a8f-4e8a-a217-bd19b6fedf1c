const {tiktok} = require("./release_webcast.min");
const {gunzip} = require("node:zlib");
const {packCtlEvent, initEvents} = require("./common");
const jsdom = require("jsdom");

/// @secureBegin

class LiveTiktok {
    constructor(context, test=false) {
        this._inline_context = context;
        this._inline_gifts = new Map(); // 存储礼物数据的 Map
        this._inline_TIMEOUT_SECONDS = 15 * 1000; // 15秒（以毫秒为单位）
        this._inline_txmll = false;
        this._inline_test_hex = test;
    }

    // 添加或更新礼物数据
    _inline_addOrUpdateGift(combo_id, giftCount, sendTime) {
        const now = Date.now();
        this._inline_gifts.set(combo_id, {
            giftCount,
            lastUpdated: now,
            sendTime: sendTime,
            timeout: setTimeout(() => {
                this._inline_gifts.delete(combo_id); // 30秒后删除未更新的数据
            }, this._inline_TIMEOUT_SECONDS)
        });
        return combo_id;
    }

    // 减去上一次的礼物个数
    _inline_decrementGift(combo_id, giftCount, sendTime) {
        if (!this._inline_gifts.has(combo_id)) {
            return null; // 如果 ID 不存在，返回 null
        }
        const gift = this._inline_gifts.get(combo_id);
        clearTimeout(gift.timeout); // 清除旧的定时器
        var ret_count = giftCount - gift.giftCount;
        gift.giftCount = giftCount;
        gift.lastUpdated = Date.now();
        gift.sendTime = sendTime;
        gift.timeout = setTimeout(() => {
            this._inline_gifts.delete(combo_id); // 30秒后删除未更新的数据
        }, this._inline_TIMEOUT_SECONDS);
        this._inline_gifts.set(combo_id, gift);
        return ret_count;
    }

    // 获取礼物数据
    _inline_getGift(combo_id) {
        return this._inline_gifts.get(combo_id) || null;
    }

    /**
     * 释放内部数据
     * */
    release() {
        this._inline_gifts.forEach(_gift => {
            clearTimeout(_gift.timeout);
            _gift.timeout = null;
        })
        this._inline_gifts.clear();
    }

    /**
     * 一次只能启动一个
     * */
    startMonitorRoom(mainWindow, liveUrl, eventCallback, logCallback) {
        if (liveUrl === '' || liveUrl === undefined || !liveUrl || !/^https?:\/\//.test(liveUrl)) {
            var eventsData = initEvents(this._inline_context, 'tiktok');
            eventsData.events_data.push(packCtlEvent(100, 'liveUrl错误', liveUrl))
            eventCallback(eventsData);
            return;
        }
        try {
            this._inline_txmll = process.env.x234534trsegfsdc || false;
        } catch (err) {
            this._inline_txmll = false;
        }
        const closeWebSocket = false;// 是否禁用 ws
        const defaultSession = mainWindow.webContents.session;
        defaultSession.webRequest.onBeforeRequest((details, callback) => {
            if ((details.url.startsWith(atob('d3NzOi8v')) || details.url.startsWith(atob('d3M6Ly8=')))
                && details.url.includes(atob('L3dlYmNhc3QvaW0vd3NfcHJveHkv'/* '/webcast/im/ws_proxy/' */))) {// cmp ws://,  wss://
                // this._inline_self_debug('拦截 WebSocket 请求:' + details.url);
                // 阻止 WebSocket 请求
                callback({cancel: closeWebSocket});
            } else if (details.url.startsWith(atob('aHR0cHM6Ly8='/* https:// */))) {
                // this._inline_self_debug('拦截HTTPS请求:' + details.url);
                // enter
                callback({});
            } else {
                // 允许其他请求
                callback({});
            }
        });
        try {
            mainWindow.webContents.debugger.attach()
        } catch (err) {
            this._inline_self_debug('Debugger attach failed : ' + err)
        }

        mainWindow.webContents.debugger.on(atob('ZGV0YWNo'/* 'detach' */), (event, reason) => {
            // this._inline_self_debug('Debugger detached due to : ' + reason)
        })
        const gWebcastObj = {};

        var getResponseDecodeBody = async (requestId) => {
            const res = await mainWindow.webContents.debugger.sendCommand(atob('RmV0Y2guZ2V0UmVzcG9uc2VCb2R5'/* 'Fetch.getResponseBody' */), {requestId: requestId})
            // return res.body
            return res.base64Encoded ? Buffer.from(res.body, 'base64') : res.body
        }

        var parseUrl = async (params) => {
            var resBody = await getResponseDecodeBody(params.requestId);
            if (resBody === undefined || resBody.length <= 0) {
                return
            }
            if (params.request.url.includes(atob('L3dlYmNhc3QvaW0vZmV0Y2gvPw=='/* '/webcast/im/fetch/?' */))) {
                // 直播间数据
                // try {
                this._inline_decodePayload(resBody, liveUrl, eventCallback, logCallback);
                // } catch (e) {
                //     // event
                //     console.log('this._inline_decodePayload error:' + e.message);
                // }
            } else if (params.request.url.includes(atob('L3dlYmNhc3QvZ2lmdC9saXN0Lz8='/* '/webcast/gift/list/?' */))) {
                // 礼物id数据
                // var match = params.request.url.match(/room_id=(\d+)/);
                // if (match) {
                //     var roomId = match[1]; // 匹配的数字部分
                //     console.log(roomId);
                //     var idExists = room_gifts.some(function(obj) {
                //         return obj.roomId === roomId;
                //     });
                //     if (!idExists) {
                //         var gifts_data = JSON.parse(response.body);
                //         room_gifts.push({roomId, gifts_data})
                //     }
                // }
                // console.log('room:'+params.request.url);
            } else if (params.request.url.includes(atob('L3dlYmNhc3Qvcm9vbS9lbnRlci8/'/* '/webcast/room/enter/?' */))) {
                // tiktok有时候获取不到数据，提示需要登录 {"data":{"message":"User doesn't login","prompts":"请先登录"},"extra":{"now":1751254479328},"status_code":20003}
                // var enter_room = JSON.parse(resBody);
                // // console.log(JSON.stringify(enter_room));
                // var eventsData = initEvents(this._inline_context, 'tiktok');
                // if (Number(enter_room.status_code) === 0) {
                //     // // 进入的直播间正在直播
                //     // // _newMessage.common.roomId = enter_room.data.id;
                //     // eventsData.events_data.push(packCtlEvent(0, '正在直播', liveUrl))
                //     // eventCallback(eventsData);
                // } else if (Number(enter_room.status_code) === 30003) {
                //     // 进入未开播的直播间
                //     eventsData.events_data.push(packCtlEvent(1, '未开播', liveUrl))
                //     eventCallback(eventsData);
                // }
                // // 进入直播间
                // // console.log('web/enter body' + params.requestId + resBody);
            } else if (params.request.url.includes(atob('L3dlYmNhc3QvcmFua2xpc3QvYXVkaWVuY2UvPw=='/* '/webcast/ranklist/audience/?' */))) {
                // console.log(params.request.url);
                // console.log('webcast/ranklist/audience:'+resBody);
            } else if (params.request.url.includes(atob('L3dlYmNhc3QvcmFua2xpc3QvaG91cl9kZXRhaWwvPw=='/* '/webcast/ranklist/hour_detail/?' */))) {
                // console.log(params.request.url);
                // console.log('webcast/ranklist/hour_detail:'+resBody);
            } else if (params.request.url.includes(atob('LzQwND9mcm9tVXJsPQ=='/* '/404?fromUrl=' */))) {
                var eventsData = initEvents(this._inline_context, 'tiktok');
                eventsData.events_data.push(packCtlEvent(100, '404', liveUrl))
                eventCallback(eventsData);
            } else if (params.request.url.includes(atob('L2FwaS1saXZlL3VzZXIvcm9vbT8='/* '/api-live/user/room?' */))) {
                // 1. 无效的 @id   -> 没有视频直播
                // 2. 无效的 id    -> 404
                // {"data":null,"message":"user_not_found","extra":{"id":"202502120637244696FFBF4DA74406DE50"},"statusCode":19881007}
                let js_body = JSON.parse(resBody);
                if (Number(js_body.statusCode) !== 0) {
                    // 提示没找到
                    var eventsData = initEvents(this._inline_context, 'tiktok');
                    eventsData.events_data.push(packCtlEvent(100, '没找到', liveUrl))
                    eventCallback(eventsData);
                }

                // console.log('live/user/room body' + resBody);
            } else if (params.request.url.includes('https://www.tiktok.com/@')) {
                // 主页，可以获取直播间的一些信息
                // console.log(params.request.url + ' res:' + resBody);
                const jsdom = require("jsdom");

                const dom = new jsdom.JSDOM(resBody);
                // const doc = parser.parseFromString(resBody, 'text/html');
                const head = dom.window.document.head;
                const scripts = head.getElementsByTagName('script');
                for (let i = 0; i < scripts.length; i++) {
                    const script = scripts[i];
                    // console.log(`Script ${i + 1} id: ${script.id} type: ${script.type}`);
                    if (script.id === 'SIGI_STATE' && script.type === 'application/json') {
                        // json:
                        const _text = JSON.parse(script.text)
                        // console.log(script.text);
                        var eventsData = initEvents(this._inline_context, 'tiktok');
                        try {
                            if (_text.LiveRoom.liveRoomUserInfo.liveRoom.status === 2) {// 正在直播
                                // 进入的直播间正在直播
                                var _event = packCtlEvent(0, '正在直播', liveUrl)
                                _event.control_msg.room_id = _text.LiveRoom.liveRoomUserInfo.user.roomId;
                                try {
                                    _event.control_msg.room_name = _text.LiveRoom.liveRoomUserInfo.user.nickname;
                                } catch (e) {
                                }

                                _event.control_msg.enter_live_info = {};
                                try {
                                    _event.control_msg.enter_live_info.room_tital = _text.LiveRoom.liveRoomUserInfo.liveRoom.title;
                                } catch (e) {
                                }

                                try {
                                    _event.control_msg.enter_live_info.own_user_id_str = _text.LiveRoom.liveRoomUserInfo.user.id;
                                    _event.control_msg.enter_live_info.own_user_uniqueId = _text.LiveRoom.liveRoomUserInfo.user.uniqueId;
                                    _event.control_msg.enter_live_info.own_user_sec_uid = _text.LiveRoom.liveRoomUserInfo.user.secUid;
                                    _event.control_msg.enter_live_info.own_user_nickname = _text.LiveRoom.liveRoomUserInfo.user.nickname;
                                    _event.control_msg.enter_live_info.own_user_head_url = _text.LiveRoom.liveRoomUserInfo.user.avatarThumb;
                                } catch (e) {
                                }
                                // console.log(JSON.stringify(_event));
                                eventsData.events_data.push(_event);
                                eventCallback(eventsData);
                            } else {
                                eventsData.events_data.push(packCtlEvent(1, '未开播', liveUrl))
                                eventCallback(eventsData);
                            }
                        } catch (e) {}


                        // 直播间状态： _text.LiveRoom.liveRoomStatus
                        // 直播间标题： _text.LiveRoom.liveRoomUserInfo.liveRoom.title
                        // 直播间作者信息：
                        // _text.LiveRoom.liveRoomUserInfo.user.avatarThumb
                        // _text.LiveRoom.liveRoomUserInfo.user.nickname
                        // _text.LiveRoom.liveRoomUserInfo.user.secUid
                        // _text.LiveRoom.liveRoomUserInfo.user.uniqueId -> liveId
                        // _text.LiveRoom.liveRoomUserInfo.user.roomId
                        // _text.LiveRoom.liveRoomUserInfo.user.status  -> 2
                        // 作者关注和被关注信息： { followingCount: 2192, followerCount: 324750 }
                        // _text.LiveRoom.liveRoomUserInfo.stats.followingCount
                        // _text.LiveRoom.liveRoomUserInfo.stats.followerCount
                        // 直播开始时间
                        // _text.LiveRoom.liveRoomUserInfo.liveRoom.startTime
                        // _text.LiveRoom.liveRoomUserInfo.liveRoom.status -> 2
                        // 观众数：
                        // _text.LiveRoom.liveRoomUserInfo.liveRoom.liveRoomStats.userCount
                        // console.log(_text.LiveRoom.liveRoomUserInfo.stats);
                        // console.log(_text.LiveRoom.liveRoomUserInfo.liveRoom);

                        break
                    }
                }
            }
            // todo: 返回直播间信息
            // https://www.tiktok.com/api-live/user/room?aid=1

            // Start ==================== 校验逻辑
            if (Array.isArray(params.responseHeaders)) {
                for (let i = 0; i < params.responseHeaders.length; i++) {
                    if (params.responseHeaders[i].name.toLowerCase() === atob('ZGF0ZQ=='/* 'date' */)) {
                        const date = params.responseHeaders[i].value;
                        if (date !== undefined) {
                            const serverTimestamp = Date.parse(date);
                            // console.log('time:' + serverTimestamp);
                            // 时间如果超过 2026-01-20 15:09:23 则提示过期
                            if (1768892963000 < serverTimestamp) {
                                // console.log('过期');
                                mainWindow.webContents.debugger.detach();
                                // mainWindow.close();  // 关闭窗口
                            } else {
                                // console.log('ok');
                            }
                        } else {
                            // todo: 没有返回 im-now 的情况先忽略
                        }
                        break
                    }
                }
            } else {
                // 突然断开代理可能会没有返回数据
            }
            // End ==================== 校验逻辑
        }

        mainWindow.webContents.debugger.on(atob('bWVzc2FnZQ=='/* 'message' */), (event, method, params) => {
            // this._inline_self_debug('info:' + params.requestId+method)
            // this._inline_self_debug('info:'+method + ':' + JSON.stringify(params))
            if (method === atob('RmV0Y2gucmVxdWVzdFBhdXNlZA=='/* 'Fetch.requestPaused' */)) {
                try {
                    parseUrl(params);
                    // 不能阻塞，否则会死循环
                    mainWindow.webContents.debugger.sendCommand(atob('RmV0Y2guY29udGludWVSZXF1ZXN0'/* 'Fetch.continueRequest' */), {requestId: params.requestId})
                } catch (e) {
                    this._inline_self_debug(method + 'Err[' + e + ']' + params.requestId + ':' + params.request.url);
                }
            } else if (method === atob('TmV0d29yay53ZWJTb2NrZXRGcmFtZVJlY2VpdmVk'/* 'Network.webSocketFrameReceived' */)) {
                // console.log('webSocketFrameReceived:'+params.requestId);
                if (gWebcastObj.hasOwnProperty(params.requestId)) {
                    // 存在则解包

                    const PushFrameMsg = tiktok.PushFrame.decode(Buffer.from(params.response.payloadData, 'base64'));
                    // console.log(PushFrameMsg)
                    if (this._inline_needUnzip(PushFrameMsg)) {
                        // 是否需要解包:
                        // console.log('needUnzip');
                        gunzip(PushFrameMsg.payload, (err, uncompressedData) => {
                            if (err) {
                                logCallback('解压缩失败:' + err);
                                return;
                            }
                            if (PushFrameMsg.payloadType === 'msg') {
                                try {
                                    this._inline_decodePayload(uncompressedData, liveUrl, eventCallback, logCallback);
                                } catch (e) {
                                    logCallback('this._inline_decodePayload ws error:' + e.message);
                                }
                            } else if (PushFrameMsg.payloadType === 'hb') {
                                // ignore
                            } else {
                                // console.log('unknown type:'+PushFrameMsg.payloadType);
                            }
                        });
                    } else {
                        // 可能是其他 ws 协议数据,可以忽略
                        // logCallback('recive warn not found ' + params.requestId);
                    }
                } else {
                    // 可能是其他 ws 协议数据,可以忽略
                    logCallback('recive warn not found ' + params.requestId);
                }

                // params.response.payload;
                // CAIYuEUgCCocCgtzZXJ2ZXJfdGltZRINMTczNjMyMTU5NTg3MioVCg1jb21wcmVzc190eXBlEgRnemlwOgJoYkIcH4sIAAAAAAAA/wAAAP//AQAA//8AAAAAAAAAAA==
            } else if (method === atob('TmV0d29yay53ZWJTb2NrZXRDcmVhdGVk'/* 'Network.webSocketCreated' */)) {
                if (params.url.startsWith(atob('d3NzOi8v'/* 'wss://' */))
                    // && params.url.includes(atob('L3dlYmNhc3QvaW0vcHVzaC8='/* '/webcast/im/push/' */) // douyin
                    && params.url.includes(atob('L3dlYmNhc3QvaW0vd3NfcHJveHkv'/* '/webcast/im/ws_proxy/' */)) // tiktok
                ) {
                    gWebcastObj[params.requestId] = params.url; // 提取 roomId
                }
                // console.log('webSocketCreated'+params.requestId+','+params.url);
            } else if (method === atob('TmV0d29yay53ZWJTb2NrZXRDbG9zZWQ='/* 'Network.webSocketClosed' */)) {
                // console.log('webSocketClosed:'+params.requestId);
                delete gWebcastObj[params.requestId];
            }
            // else if (method.includes('webSocket')) {
            //   console.log(method);
            // }
        });

        var _str_response = atob('UmVzcG9uc2U='/* 'Response' */)
        mainWindow.webContents.debugger.sendCommand(atob('RmV0Y2guZW5hYmxl'/* 'Fetch.enable' */), {
            patterns: [
                {
                    urlPattern: '*' + atob('L3dlYmNhc3QvaW0vZmV0Y2gvPw=='/* '/webcast/im/fetch/?' */) + "*",
                    requestStage: _str_response
                },
                {
                    urlPattern: '*' + atob('L3dlYmNhc3QvZ2lmdC9saXN0Lz8='/* '/webcast/gift/list/?' */) + "*",
                    requestStage: _str_response
                },
                {
                    urlPattern: '*' + atob('L3dlYmNhc3Qvcm9vbS9lbnRlci8/'/* '/webcast/room/enter/?' */) + "*",
                    requestStage: _str_response
                },
                {
                    urlPattern: '*' + atob('L3dlYmNhc3QvcmFua2xpc3QvYXVkaWVuY2UvPw=='/* '/webcast/ranklist/audience/?' */) + "*",
                    requestStage: _str_response
                },
                {
                    urlPattern: '*' + atob('L3dlYmNhc3QvcmFua2xpc3QvaG91cl9kZXRhaWwvPw=='/* '/webcast/ranklist/hour_detail/?' */) + "*",
                    requestStage: _str_response
                },
                {
                    urlPattern: '*' + atob('LzQwND9mcm9tVXJsPQ=='/* '/404?fromUrl=' */) + '*',
                    requestStage: _str_response
                },
                {
                    urlPattern: '*' + atob('L2FwaS1saXZlL3VzZXIvcm9vbT8='/* '/api-live/user/room?' */) + "*",
                    requestStage: _str_response
                },
                {
                    urlPattern: liveUrl,
                    requestStage: _str_response
                }
            ]
        })

        mainWindow.loadURL('about:blank').then(r => {
        })
        // 必须要有页面才会 enable 成功
        mainWindow.webContents.debugger.sendCommand(atob('TmV0d29yay5lbmFibGU='/* 'Network.enable' */)).then(()=>{
            this._inline_self_debug('enable ok')
            // this._inline_self_debug('to loadURL')
            // 使用代理，而且需要先访问主页，再去访问直播间，这样才不会弹出登录窗口
            mainWindow.loadURL(liveUrl).catch((err) => {
                logCallback('Failed to load URL:', err);
            }).finally(()=>{
                // this._inline_self_debug('loadURL finally')
            })
        });
    }

    // tools
    _inline_needUnzip(t) {
        for (const e of Object.values(t.headers)) if ("compress_type" === e.key && "gzip" === e.value) return !0;
        return !1
    }

    _inline_debug_log(_common, msg) {
    }

    _inline_self_debug(msg) {
        if (this._inline_txmll) {
            console.log(msg)
        }
    }

    _inline_decodePayload(payload, live_url, eventCallback, logCallback) {
        if (typeof payload === 'string') {
            payload = Buffer.from(payload, 'base64');
        }

        var eventsData = initEvents(this._inline_context, 'tiktok');
        if (this._inline_txmll) {
            eventsData.origin = payload.toString('base64');
        }
        const response = tiktok.priv.Response.decode(payload)
        response.messages.forEach(msg => {
            var _decodeMessage;
            try {
                // this._inline_self_debug(msg.method + ', ' + JSON.stringify(msg))
                if (msg.method === "WebcastLikeMessage") {
                    // 点赞
                    _decodeMessage = tiktok.webcast.LikeMessage.decode(msg.payload);
                    // let formatMsg = parseFormatText(_decodeMessage.common.displayText);
                    // 记录点赞个数
                    // this._inline_debug_log(_decodeMessage.common,  formatMsg + "x" + _decodeMessage.count);
                    eventsData.like_total = _decodeMessage.total;  // 外部
                    let _eventData = LiveTiktok._inline_packCommon(1, 'like_msg');
                    _eventData.like_msg = {};
                    _eventData.like_msg.user = LiveTiktok._inline_packUser(_decodeMessage.user);
                    _eventData.like_msg.like_count = _decodeMessage.count;
                    eventsData.events_data.push(_eventData);
                    // this._inline_debug_log(_decodeMessage.common, JSON.stringify(_decodeMessage));
                }
                // 评论数据
                else if (msg.method === "WebcastChatMessage") {
                    /**
                     * 有 _decodeMessage.userIdentity.isGiftGiverOfAnchor true
                     * 有 _decodeMessage.userIdentity.isFollowerOfAnchor true
                     * contentLanguage en 语言类型
                     * */
                    _decodeMessage = tiktok.webcast.ChatMessage.decode(msg.payload);
                    let _eventData = LiveTiktok._inline_packCommon(2, 'comment_msg');
                    _eventData.comment_msg = {};
                    _eventData.comment_msg.user = LiveTiktok._inline_packUser(_decodeMessage.user);
                    _eventData.comment_msg.content = _decodeMessage.content;
                    _eventData.comment_msg.language = _decodeMessage.contentLanguage;
                    eventsData.events_data.push(_eventData);

                    // log(_decodeMessage.common, _decodeMessage.user.nickname + ":" + _decodeMessage.content);
                    // var _newMessage = pb_webcast.tkMsg.ChatMessage.create();
                    // _newMessage.common = pb_webcast.tkMsg.InteractiveCommon.create();
                    // _newMessage.common.roomId = _decodeMessage.common.roomId;
                    // _newMessage.common.msgId = _decodeMessage.common.msgId;
                    // _newMessage.common.msgType = 2;
                    // _newMessage.common.msgTypeStr = 'live_comment';
                    // _newMessage.content = _decodeMessage.content;
                    // _newMessage.contentLanguage = _decodeMessage.contentLanguage;
                    // _newMessage.clientSendTime = _decodeMessage.common.clientSendTime;
                    // if (_decodeMessage.user !== undefined && _decodeMessage.user !== null) {
                    //     processUserInfo(_decodeMessage, _newMessage);
                    // }
                    // // {"common":{"method":"WebcastChatMessage","msgId":"7473040849883171601","roomId":"7472988463770487559","createTime":"1739952916443","isShowMsg":true,"foldType":"2","anchorFoldType":"2","fromIdc":"my2","roomMessageHeatLevel":"4","foldTypeForWeb":"2","anchorFoldTypeForWeb":"2","clientSendTime":"1739952916047"},"user":{"id":"6718893884770124801","nickname":"🐝はち🦕","avatarThumb":{"urlList":["https://p77-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&nonce=26325&refresh_token=ed8d6987e765a4397a528d55dca22fa5&x-expires=1740124800&x-signature=5jftB3m0FUPliKHCJFy5kQQE8gM%3D&idc=my2&ps=13740610&shcp=fdd36af4&shp=a5d48078&t=4d5b0474","https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-cropcenter:100:100.webp?dr=14579&nonce=41643&refresh_token=0b886cb65b468710bf5d5273c50566db&x-expires=1740124800&x-signature=c56A1QUBLfx%2FJtJl639x%2BzX8jhA%3D&idc=my2&ps=13740610&shcp=fdd36af4&shp=a5d48078&t=4d5b0474","https://p77-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-cropcenter:100:100.jpeg?dr=14579&nonce=6151&refresh_token=555608c36e69d35604eb0f1e0b590ed0&x-expires=1740124800&x-signature=U%2FOh5zcBCkJEX4Knu9Fm%2FWyO%2BOQ%3D&idc=my2&ps=13740610&shcp=fdd36af4&shp=a5d48078&t=4d5b0474"],"uri":"100x100/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e"},"avatarMedium":{"urlList":["https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-compress_quality_30:64:64.webp?lk3s=1e19605b&nonce=38347&refresh_token=0be2d4802ff0258f9e8d7570df69d6ab&x-expires=1739973600&x-signature=VpmjFYJ0jgP2r0Es6x91LfE9FSU%3D&shp=1e19605b&shcp=-","https://p16-sign-sg.tiktokcdn.com/tos-alisg-avt-0068/a4564b1a0254e4b6de44219a7e71670e~tplv-tiktokx-compress_quality_30:64:64.jpeg?lk3s=1e19605b&nonce=53291&refresh_token=1b89e42b52ad53b0e2906fc863ce2ad6&x-expires=1739973600&x-signature=Q0VRk4CoOGKQW5pYVvxLD4Wi0c8%3D&shp=1e19605b&shcp=-"]},"badgeImageList":[{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image"],"uri":"webcast-va/subs_badge_icon_101.png","height":"16","width":"16","imageType":30}],"followInfo":{"followingCount":"325","followerCount":"83","followStatus":"1"},"payGrade":{},"userAttr":{},"displayId":"clarks8808","secUid":"MS4wLjABAAAARIInXh0BFoiBDNTs5l3gLJudJLFMfqdRJIQK4J8a8Lv0C7m56_B-hy0huOGAgOZK","badgeList":[{"displayType":"BADGE_DISPLAY_TYPE_IMAGE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Subscriber","position":"PositionLeft","privilegeLogExtra":{"dataVersion":"2","privilegeId":"7459985881393761032","privilegeVersion":"0","privilegeOrderId":"mock_sub_7459985881393761032","level":"1"},"image":{"displayType":"BADGE_DISPLAY_TYPE_IMAGE","image":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image"],"uri":"webcast-va/subs_badge_icon_101.png","height":"16","width":"16","imageType":30}}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Platform","sceneType":"BadgeSceneType_UserGrade","position":"PositionLeft","OpenWebURL":"sslocal://webcast_lynxview_popup?use_spark=1&url=https%3A%2F%2Flf16-gecko-source.tiktokcdn.com%2Fobj%2Fbyte-gurd-source-sg%2Ftiktok%2Ffe%2Flive%2Ftiktok_live_revenue_user_level_main%2Fsrc%2Fpages%2Fprivilege%2Fpanel%2Ftemplate.js&hide_status_bar=0&hide_nav_bar=1&container_bg_color=00000000&height=90%25&bdhm_bid=tiktok_live_revenue_user_level_main&use_forest=1","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7138381747292542756","privilegeVersion":"0","privilegeOrderId":"mock_fix_width_transparent_7138381747292542756","level":"15"},"combine":{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","icon":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/grade_badge_icon_lite_lv15_v2.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/grade_badge_icon_lite_lv15_v2.png~tplv-obj.image"],"uri":"webcast-va/grade_badge_icon_lite_lv15_v2.png","openWebUrl":"sslocal://webcast_lynxview_popup?use_spark=1&url=https%3A%2F%2Flf16-gecko-source.tiktokcdn.com%2Fobj%2Fbyte-gurd-source-sg%2Ftiktok%2Ffe%2Flive%2Ftiktok_live_revenue_user_level_main%2Fsrc%2Fpages%2Fprivilege%2Fpanel%2Ftemplate.js&hide_status_bar=0&hide_nav_bar=1&container_bg_color=00000000&height=90%25&bdhm_bid=tiktok_live_revenue_user_level_main&use_forest=1"},"str":"15","padding":{"useSpecific":true,"middlePadding":1,"badgeWidth":32},"fontStyle":{},"profileCardPanel":{"projectionConfig":{"icon":{}},"profileContent":{}},"background":{"image":{},"backgroundColorCode":"_inline_B3477EFF"},"backgroundDarkMode":{"image":{},"backgroundColorCode":"_inline_B3477EFF"},"publicScreenShowStyle":14,"personalCardShowStyle":15,"paddingNewFont":{"useSpecific":true,"middlePadding":1,"badgeWidth":32}}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Fans","position":"PositionLeft","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7196929090442545925","privilegeVersion":"0","privilegeOrderId":"mock_fix_width_transparent_7196929090442545925","level":"20"},"combine":{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","icon":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/fans_badge_icon_lv20_v0.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/fans_badge_icon_lv20_v0.png~tplv-obj.image"],"uri":"webcast-va/fans_badge_icon_lv20_v0.png"},"str":"Ⅲ","padding":{"useSpecific":true,"middlePadding":3,"badgeWidth":32},"fontStyle":{},"profileCardPanel":{"projectionConfig":{"icon":{}},"profileContent":{}},"background":{"image":{},"backgroundColorCode":"_inline_B3D63D35"},"backgroundDarkMode":{"image":{},"backgroundColorCode":"_inline_B3D63D35"},"publicScreenShowStyle":14,"personalCardShowStyle":15,"paddingNewFont":{"useSpecific":true,"middlePadding":2,"badgeWidth":32}}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Subscriber","position":"PositionLeft","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7459985881393761032","privilegeVersion":"0","privilegeOrderId":"mock_sub_7459985881393761032","level":"1"},"combine":{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","icon":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/subs_badge_icon_101.png~tplv-obj.image"],"uri":"webcast-va/subs_badge_icon_101.png"},"str":"せいら沼","padding":{"useSpecific":true,"middlePadding":1,"leftPadding":2,"rightPadding":4,"horizontalPaddingRule":"HorizontalPaddingRuleUseLeftAndMiddleAndRight","verticalPaddingRule":"VerticalPaddingRuleUseTopAndBottom"},"background":{"image":{},"backgroundColorCode":"_inline_99EF7300"},"publicScreenShowStyle":14,"personalCardShowStyle":14,"paddingNewFont":{"useSpecific":true,"middlePadding":1,"leftPadding":2,"rightPadding":4,"horizontalPaddingRule":"HorizontalPaddingRuleUseLeftAndMiddleAndRight","verticalPaddingRule":"VerticalPaddingRuleUseTopAndBottom"}}}]},"content":"素晴らしい体験させてもらいましたよ","emotes":[{"index":"17","emote":{"emoteId":"7324257075008146184","image":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp","https://p19-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp"],"uri":"webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20","avgColor":"_inline_7C7CA3"},"packageId":"7238813739339090690"}},{"index":"18","emote":{"emoteId":"7324257075008146184","image":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp","https://p19-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp"],"uri":"webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20","avgColor":"_inline_7A6D53"},"packageId":"7238813739339090690"}},{"index":"19","emote":{"emoteId":"7324257075008146184","image":{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp","https://p19-webcast.tiktokcdn.com/webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20~tplv-fyjuxuxag8-webp.webp"],"uri":"webcast-sg/sub_e8c7b8159825bab172604c09165b1ec3ae65bbeeccd97734b30683ba2c3baa20","avgColor":"_inline_666666"},"packageId":"7238813739339090690"}}],"contentLanguage":"ja","userIdentity":{"isSubscriberOfAnchor":true,"isFollowerOfAnchor":true},"commentQualityScores":[{"version":"user_type_rule","score":"300000"},{"version":"community-flagged","score":"9572829"},{"version":"commentator_id","score":"6718893884770124801"},{"version":"default","score":"1739952917129000"},{"version":"default_app","score":"10000"},{"version":"rankV3","score":"1739952920000099"},{"version":"ttp_rule_rerank","score":"1739952926363000"},{"version":"timestamp_desc","score":"98260047083637000"},{"version":"tikcast_community_comment_18866_v7_r655069","score":"1739952920158849"},{"version":"tikcast_community_comment_18866_v7_r655069_desc","score":"1739952929841150"},{"version":"idc_rule_rerank","score":"1739952916364000"},{"version":"v13_r712088","score":"1739952916364000"},{"version":"batch_rank_v1","score":"1739952940897409"},{"version":"batch_rank_v1_understanding","score":"1739952910897409"},{"version":"v20_r1002545","score":"1739952910602652"},{"version":"v20_r1002546","score":"1739952913156820"},{"version":"v20_r1203882","score":"1739952915616958"},{"version":"v20_r1203882_desc","score":"1739952914383041"},{"version":"v20_r1203882_understanding","score":"1739952914383041"}],"commentTag":["COMMENT_TAG_NORMAL"],"publicAreaMessageCommon":{"creatorSuccessInfo":{"tags":[{"tagType":19,"tagText":{"key":"ttlive_companionTool_tag_sentGifts30D","defaultPattern":"30日間にギフトを853個贈りました","defaultFormat":{},"pieces":[{"type":1,"format":{},"stringValue":"853"}]}}],"topic":{"topicActionType":"TOPIC_ACTION_TYPE_FOLLOW","topicText":{"key":"ttlive_negScreenCompanion_actionTag_memberLevelTag","defaultPattern":"ハイレベルなメンバー"},"topicTips":{"key":"ttlive_negScreenCompanion_actionTag_memberLevelTip","defaultPattern":"フォローバックして交流しましょう！"}}},"portraitInfo":{},"userInteractionInfo":{"likeCnt":"2439","commentCnt":"6"}},"screenTime":"1739952914008","signature":"65e235bfaef2edff05b93c7efff971ad","signatureVersion":"v1"}
                    // // 聊天消息里还有一个 emotes, 里面有 index
                    // if (Array.isArray(_decodeMessage.emotes) && _decodeMessage.emotes.length !== 0) {
                    //     // 存在
                    //     for (var i = 0; i < _decodeMessage.emotes.length; i++) {
                    //         // _decodeMessage.emotes[i].index; // 表示在 content 里的位置
                    //         _newMessage.emotes[i] = pb_webcast.tkMsg.ChatMessage.EmoteWithIndex.create();
                    //         _newMessage.emotes[i].index = _decodeMessage.emotes[i].index;
                    //         _newMessage.emotes[i].emote = pb_webcast.tkMsg.ChatMessage.Emote.create();
                    //         _newMessage.emotes[i].emote.emoteId = _decodeMessage.emotes[i].emote.emoteId;
                    //         _newMessage.emotes[i].emote.imageUrl = _decodeMessage.emotes[i].emote.image.urlList[0];
                    //         _newMessage.emotes[i].emote.imageUri = _decodeMessage.emotes[i].emote.image.uri;
                    //     }
                    // }
                    // if (_decodeMessage.userIdentity !== null && _decodeMessage.userIdentity !== undefined) {
                    //     _newMessage.userIdentity = pb_webcast.tkMsg.ChatMessage.UserIdentity.create();
                    //     _newMessage.userIdentity = _decodeMessage.userIdentity;
                    // }
                    // // this._inline_self_debug(JSON.stringify(_decodeMessage));
                    // outPayload.payload.push(_newMessage);
                    // this._inline_debug_log(_decodeMessage.common, JSON.stringify(_decodeMessage));
                } else if (msg.method === "WebcastGiftMessage") {
                    _decodeMessage = tiktok.webcast.GiftMessage.decode(msg.payload);
                    if (_decodeMessage.gift.combo === true && _decodeMessage.repeatEnd === 1) {
                        // 忽略最后一个汇总
                        return;
                    }
                    // 处理 combo 类型
                    var cur_count = _decodeMessage.comboCount*_decodeMessage.groupCount;
                    var str_groupId = '';
                    if (_decodeMessage.gift.combo === true) {
                        str_groupId = String(_decodeMessage.groupId) + '_' + _decodeMessage.user.displayId;
                        // todo: logId, tiktok有些groupId是0 (combo === false)
                        var local_gift = this._inline_getGift(str_groupId);
                        if (local_gift === null) {
                            // 初始化
                            this._inline_addOrUpdateGift(str_groupId, cur_count, _decodeMessage.sendTime);
                        } else {
                            if (_decodeMessage.sendTime < local_gift.sendTime) {
                                // 有可能时间顺序不一样，忽略旧的
                                return;
                            }
                            cur_count = this._inline_decrementGift(str_groupId, cur_count, _decodeMessage.sendTime);
                        }
                    } else {
                        str_groupId = 'combo_false';
                    }
                    let _eventData = LiveTiktok._inline_packCommon(3, 'gift_msg');
                    _eventData.gift_msg = {};
                    _eventData.gift_msg.user = LiveTiktok._inline_packUser(_decodeMessage.user);
                    _eventData.gift_msg.gift_id = _decodeMessage.giftId;
                    _eventData.gift_msg.gift_name = _decodeMessage.gift.name;
                    if (_decodeMessage.gift.image !== undefined && _decodeMessage.gift.image !== null) {
                        _eventData.gift_msg.gift_url = _decodeMessage.gift.image.urlList[0];
                    }
                    _eventData.gift_msg.gift_price = _decodeMessage.gift.diamondCount;
                    _eventData.gift_msg.gift_msg_key = str_groupId;
                    _eventData.gift_msg.batch_size = _decodeMessage.groupCount;
                    _eventData.gift_msg.count = cur_count;
                    _eventData.gift_msg.total_count = _decodeMessage.comboCount;
                    eventsData.events_data.push(_eventData);
                    if (_decodeMessage.gift.name === 'GG') {
                        // console.log(JSON.stringify(_decodeMessage));
                    }
                    // console.log(JSON.stringify(_decodeMessage));
                } else if (msg.method === 'WebcastFansclubMessage') {
                    // // 没有displayText
                    // // [2025-1-17 15:0:22:32][7460736793282562857][WebcastFansclubMessage]{"common":{"method":"WebcastFansclubMessage","msgId":"7460775750880096041","roomId":"7460736793282562857","isShowMsg":true},"action":7,"user":{"id":"94528882049","shortId":"796497705","nickname":"月baby🌙","gender":2,"signature":"被爱好似有靠山❤️","avatarThumb":{"urlList":["https://p3.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=306,"https://p11.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p26.douyinpic.com/aweme/100x100/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********"],"uri":"100x100/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100"},"avatarMedium":{"urlList":["https://p11.douyinpic.com/aweme/720x720/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p3.douyinpic.com/aweme/720x720/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p26.douyinpic.com/aweme/720x720/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********"],"uri":"720x720/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100"},"avatarLarge":{"urlList":["https://p11.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p3.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********","https://p26.douyinpic.com/aweme/1080x1080/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100.jpeg?from=**********"],"uri":"1080x1080/aweme-avatar/tos-cn-avt-0015_25cce4db6fd8373a5c1f0b7b5e3cd100"},"verified":true,"city":"衡水","status":1,"modifyTime":"**********","areQrcodeUri":"7b7d001f0c5cc7d20e8a","badgeImageList":[{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_35.png","height":"16","width":"32","imageType":1,"content":{"level":"35","alternativeText":"荣誉等级35级勋章"}},{"urlList":["https://p3-webcast.douyinpic.com/img//fansclub_level_v6_15.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15.png~tplv-obj.image"],"uri":"webcast/fansclub_level_v6_15.png","imageType":7,"content":{"name":"迪士尼","fontColor":"_inline_FFF","level":"15","alternativeText":"迪士尼粉丝团勋章"}}],"followInfo":{"followingCount":"113","followerCount":"19639","followerCountStr":"2万","followingCountStr":"113"},"payGrade":{"level":"35","thisGradeMinDiamond":"230000","taxDiamond":"300000","newImIconWithLevel":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_35.png","height":"16","width":"32","imageType":1},"newLiveIcon":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/aweme_pay_grade_2x_35_39.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/aweme_pay_grade_2x_35_39.png~tplv-obj.image"],"uri":"webcast/aweme_pay_grade_2x_35_39.png","height":"12","width":"12","imageType":1}},"fansClub":{"data":{"clubName":"迪士尼","level":15,"usersClubStatus":1,"badge":{"icons":{"2":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15.png~tplv-obj.image"],"uri":"webcast/fansclub_level_v6_15.png"},"4":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image"],"uri":"webcast/star_guard_advanced_badge_15_xmp.png","flexSettingList":{"settingList":["0","0","62","18"]},"textSettingList":{"settingList":["0","0","62","18"]}}},"title":"迪士尼"},"anchorId":"65305021"}},"userAttr":{},"linkMicStats":1,"displayId":"748983y.","withCommercePermission":true,"withFusionShopEntry":true,"secUid":"MS4wLjABAAAA_Rd-EmKuOLmAZ6bOU0AXgAEAJLGW6fgDNqSiMHYrRyU","authorizationInfo":3,"adversaryAuthorizationInfo":1,"badgeImageListV2":[{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_35.png","height":"16","width":"32","imageType":1,"content":{"level":"35","alternativeText":"荣誉等级35级勋章"}},{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15lv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/fansclub_level_v6_15.png~tplv-obj.image"],"uri":"webcast/fansclub_level_v6_15.png","imageType":7,"content":{"name":"迪士尼","fontColor":"_inline_FFFFFF","level":"15","alternaeText":"迪士尼粉丝团勋章"}},{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-e"],"uri":"webcast/star_guard_advanced_badge_15_xmp.png","imageType":51,"content":{"name":"迪士尼","fontColor":"_inline_FFFFFF","level":"15","alternativeText":"迪士尼粉丝团勋章"},"flexSettingList":{"settingList":["0","0","62","18"]},"tist":{"settingList":["0","0","62","18"]}}],"locationCity":"衡水","fansGroupInfo":{"listFansGroupUrl":"sslocal://webcast_lynxview?height=754&radius=8&gravity=bottom&type=popup&animation_type=present&url=https%3A%2F%2Flf-webcast-srcecdn-tos.bytegecko.com%2Fobj%2Fbyte-gurd-source%2Fwebcast%2Fmono%2Flynx%2Fdouyin_lynx_fansclub%2Ftemplate%2Fpages%2Ffansclub%2Ffans_group%2Fuser%2Ftemplate.js&load_taro=0&fallback_url=sslocal%3A%2F%2Fwebcast_webview%3Furl%3Dhttps%253A%252F%252Flf-webcast-sourcecdn-tos.bytegecko.com%252Fobj%252Fbyte-gurd-source%252Fwebcast%252Fmono%252Flynx%252Fdouyin_lynx_fansclub%252Ftemplate%252Fpages%252Ffansclub%252Ffans_group%252Fuser%252Findex.html%26type%3Dpopup%26gravity%3Dbottom%26height%3D754%26radius%3D8%26load_taro%3D0"},"mysteryMan":1,"jAccreditInfo":{},"subscribe":{},"webcastUid":"MS4wLjPZnV_HMPcONy7lczqH14YEx-eKLO6wCXX_JC1OEYTAoHu74hvwgavYKpEJU-naW78","publicAreaBadgeInfo":{"badgeInfoMap":{"\u0006\u0000\u0000\u0000\u0000\u0000\u0000\u0000":{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/new_user_grade_level_v1_35.png~tplv-obj.image"],"uri":"webcast/new_user_grade_level_v1_35.png","height":"16","width":"32","imageType":1,"content":{"level":"35","alternativeText":"荣誉等级35级勋章"}},"\f\u0000\u0000\u0000\u0000\u0000\u0000:{"urlList":["https://p3-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image","https://p11-webcast.douyinpic.com/img/webcast/star_guard_advanced_badge_15_xmp.png~tplv-obj.image"],"uri":"webcast/star_guard_advanced_badge_15_xmp.png","imageType":51,"content":{"name":"迪士尼","fontColor":"_inline_FFFFFF","level":"15","alternativeText":"迪士尼粉丝团勋章"},"flexSettingList":{"settingList":["0","0","62","18"]},"textSettingList":{"s:["0","0","62","18"]}}},"badgeList":["6","12"]},"idStr":"94528882049"},"publicAreaCommon":{"individualPriority":"30","imAction":3}}
                    // // "恭喜 {0:user} 加入粉丝团成为第{1:string}名"{2:string}"
                    // _decodeMessage = tiktok.webcast.FansclubMessage.decode(msg.payload);
                    // if (_decodeMessage.action === 2 || _decodeMessage.action === 1) {
                    //     // 新加入, 升级
                    // } else {
                    //     // TODO: test
                    //     // logCallback(_decodeMessage.content + ',' + _decodeMessage.action);
                    // }
                    // var _newMessage = pb_webcast.tkMsg.FansclubMessage.create();
                    // _newMessage.common = pb_webcast.tkMsg.InteractiveCommon.create();
                    // _newMessage.common.roomId = _decodeMessage.common.roomId;
                    // _newMessage.common.msgId = _decodeMessage.common.msgId;
                    // _newMessage.common.msgType = 4;
                    // _newMessage.common.msgTypeStr = 'live_fansclub';
                    // _newMessage.action = _decodeMessage.action;
                    // if (_decodeMessage.user !== undefined && _decodeMessage.user !== null) {
                    //     processUserInfo(_decodeMessage, _newMessage);
                    // }
                    // outPayload.payload.push(_newMessage);
                } else if (msg.method === "WebcastSocialMessage") {
                    // _decodeMessage = tiktok.webcast.SocialMessage.decode(msg.payload);
                    // // 关注主播 type == 1
                    // // 分享直播 type == 3
                    // if (Number(_decodeMessage.action) === 1) {
                    //     var _newMessage = pb_webcast.tkMsg.SocialMessage.create();
                    //     _newMessage.common = pb_webcast.tkMsg.InteractiveCommon.create();
                    //     _newMessage.common.roomId = _decodeMessage.common.roomId;
                    //     _newMessage.common.msgId = _decodeMessage.common.msgId;
                    //     _newMessage.common.msgType = 5;
                    //     _newMessage.common.msgTypeStr = 'live_follow';
                    //     _newMessage.action = _decodeMessage.action;
                    //     _newMessage.followCount = _decodeMessage.followCount;
                    //     if (_decodeMessage.user !== undefined && _decodeMessage.user !== null) {
                    //         processUserInfo(_decodeMessage, _newMessage);
                    //     }
                    //     outPayload.payload.push(_newMessage);
                    // }
                } else if (msg.method === "WebcastMemberMessage") {
                    _decodeMessage = tiktok.webcast.MemberMessage.decode(msg.payload);
                    if (Number(_decodeMessage.action) === 1) {
                        eventsData.watching_total = _decodeMessage.memberCount;
                        let _eventData = LiveTiktok._inline_packCommon(4, 'member_msg');
                        _eventData.member_msg = {};
                        _eventData.member_msg.user = LiveTiktok._inline_packUser(_decodeMessage.user);
                        eventsData.events_data.push(_eventData);
                    } else {
                        // {"common":{"method":"WebcastMemberMessage","msgId":"7469733849273387794","roomId":"7469711765472103176","createTime":"1739182940791","isShowMsg":true},"operator":{"id":"7021861793069515777","nickname":"ปัด🅰️Ⓜ️คุณซิน มาเชียร์","displayId":"pud9942","secUid":"MS4wLjABAAAANvzDNxqVWngL9WgAoW80PTGZbBULTdBu45qUAQ-GQGDCODP5y6TDG4BN4fOaPRBf"},"action":"26"}
                        // action: 26, 27 之后会有 WebcastRoomPinMessage 消息
                        // 26 设置 pin 消息
                        // 27 应该是取消 pin 消息
                        // todo: debug
                        this._inline_self_debug(JSON.stringify(_decodeMessage));
                    }
                } else if (msg.method === 'WebcastControlMessage') {
                    _decodeMessage = tiktok.webcast.ControlMessage.decode(msg.payload);
                    // this._inline_self_debug(JSON.stringify(_decodeMessage));
                    if (Number(_decodeMessage.action) === 3) {  // 直播结束
                        let _eventData = LiveTiktok._inline_packCommon(5, 'control_msg');
                        _eventData.control_msg = {};
                        _eventData.control_msg.action = 1;
                        _eventData.control_msg.action_msg = _decodeMessage.tips;
                        _eventData.control_msg.live_url = live_url;
                        eventsData.events_data.push(_eventData);
                    }
                } else if (msg.method === 'WebcastRoomUserSeqMessage') {
                    _decodeMessage = tiktok.webcast.RoomUserSeqMessage.decode(msg.payload);
                    var showMsg = 'ranks:';
                    _decodeMessage.ranks.forEach((_rank) => {
                        showMsg += '[' + _rank.rank + ']' + _rank.user.nickname + ','
                    });
                    showMsg += '在线观众:' + _decodeMessage.total + ',TotalUser:' + _decodeMessage.totalUser;
                    // this._inline_self_debug(showMsg);
                    // this._inline_self_debug(JSON.stringify(_decodeMessage));
                }
                    // else if (msg.method === 'WebcastUnauthorizedMemberMessage') {
                    //     // {"common":{"method":"WebcastUnauthorizedMemberMessage","msgId":"7469979692280728362","roomId":"7469929484154604293","createTime":"1739240183688","isShowMsg":true,"foldType":"1","anchorFoldType":"1"},"action":1,"nickNamePrefix":{"key":"web_nonlogin_im_1","defaultPattern":"Viewer%s","defaultFormat":{"color":"_inline_ffffffff","weight":400}},"nickName":"256052","enterText":{"key":"live_room_enter_toast","defaultPattern":"{0:user} joined","defaultFormat":{"color":"_inline_ffffffff","weight":400}}}
                    //     // 未登录的用户进入直播间
                    //     _decodeMessage = tiktok.webcast.UnauthorizedMemberMessage.decode(msg.payload);
                    //     this._inline_self_debug(JSON.stringify(_decodeMessage));
                // }
                else if (msg.method === 'WebcastRoomPinMessage') {
                    // {"common":{"method":"WebcastRoomPinMessage","msgId":"7471131345818438443","roomId":"7471092186025970474","createTime":"1739508338046","isShowMsg":true},"chatMessage":{"common":{"method":"WebcastChatMessage","msgId":"7471131367271648046","roomId":"7471092186025970474","createTime":"1739508329749","isShowMsg":true,"foldType":"2","anchorFoldType":"2","fromIdc":"useast5","anchorPriorityScore":"1000","roomMessageHeatLevel":"4","foldTypeForWeb":"2","anchorFoldTypeForWeb":"2","clientSendTime":"1739508329263"},"user":{"id":"7111486241325499434","nickname":"Blessing's","avatarThumb":{"urlList":["https://p16-sign.tiktokcdn-us.com/tos-useast5-avt-0068-tx/40a7532208eb9c388b544e5f98f93512~c5_100x100.webp?lk3s=a5d48078&nonce=44791&refresh_token=67e34261476f9153948c38335a84a872&x-expires=1739678400&x-signature=EfrT0RoPnJgBTNu0ZtuJfsfZrpE%3D&shp=a5d48078&shcp=fdd36af4","https://p19-sign.tiktokcdn-us.com/tos-useast5-avt-0068-tx/40a7532208eb9c388b544e5f98f93512~c5_100x100.webp?lk3s=a5d48078&nonce=25340&refresh_token=a6459a8a29f33f899f576524ce49745a&x-expires=1739678400&x-signature=Piy1FOLLkagBjFfXUwbgm1hvavE%3D&shp=a5d48078&shcp=fdd36af4","https://p16-sign.tiktokcdn-us.com/tos-useast5-avt-0068-tx/40a7532208eb9c388b544e5f98f93512~c5_100x100.jpeg?lk3s=a5d48078&nonce=95897&refresh_token=f8323187053034160ca6eaaccaea01c3&x-expires=1739678400&x-signature=kiINPpJKIjoOva6KzWF3W86XyxQ%3D&shp=a5d48078&shcp=fdd36af4"],"uri":"100x100/tos-useast5-avt-0068-tx/40a7532208eb9c388b544e5f98f93512"},"badgeImageList":[{"urlList":["https://p16-webcast.tiktokcdn.com/webcast-va/subs_badge_Lv1.png~tplv-obj.image","https://p19-webcast.tiktokcdn.com/webcast-va/subs_badge_Lv1.png~tplv-obj.image"],"uri":"webcast-va/subs_badge_Lv1.png","height":"16","width":"16","imageType":30}],"followInfo":{"followingCount":"10000","followerCount":"7691","followStatus":"2"},"payGrade":{},"userAttr":{},"displayId":"2merri1111","secUid":"MS4wLjABAAAA_bmOfMAZmiR3CvYTPMRMe8L37xMKY9_dlg3gwo8ronLc8ogqqOV1NVG8EFJRIKWZ","badgeList":[{"displayType":"BADGE_DISPLAY_TYPE_IMAGE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Subscriber","position":"PositionLeft","privilegeLogExtra":{"dataVersion":"2","privilegeId":"7449250932443581226","privilegeVersion":"0","privilegeOrderId":"mock_sub_7449250932443581226","level":"0"}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Platform","sceneType":"BadgeSceneType_UserGrade","position":"PositionLeft","OpenWebURL":"sslocal://webcast_lynxview_popup?use_spark=1&url=https%3A%2F%2Flf16-gecko-source.tiktokcdn.com%2Fobj%2Fbyte-gurd-source-sg%2Ftiktok%2Ffe%2Flive%2Ftiktok_live_revenue_user_level_main%2Fsrc%2Fpages%2Fprivilege%2Fpanel%2Ftemplate.js&hide_status_bar=0&hide_nav_bar=1&container_bg_color=00000000&height=90%25&bdhm_bid=tiktok_live_revenue_user_level_main&use_forest=1","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7138381747292526372","privilegeVersion":"0","privilegeOrderId":"mock_fix_width_transparent_7138381747292526372","level":"14"}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Fans","position":"PositionLeft","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7196929090442513157","privilegeVersion":"0","privilegeOrderId":"mock_fix_width_transparent_7196929090442513157","level":"5"}},{"displayType":"BADGE_DISPLAY_TYPE_COMBINE","priorityType":"BadgePriorityType_Relation","sceneType":"BadgeSceneType_Subscriber","position":"PositionLeft","display":true,"privilegeLogExtra":{"dataVersion":"2","privilegeId":"7449250932443581226","privilegeVersion":"0","privilegeOrderId":"mock_sub_7449250932443581226","level":"0"}}]},"content":"Rest.   Up.   Peeps 🐥","contentLanguage":"en","userIdentity":{"isGiftGiverOfAnchor":true,"isSubscriberOfAnchor":true,"isMutualFollowingWithAnchor":true,"isFollowerOfAnchor":true},"commentQualityScores":[{"version":"user_type_rule","score":"300000"},{"version":"community-flagged","score":"5537109"},{"version":"commentator_id","score":"7111486241325499434"},{"version":"default","score":"1739508329702000"},{"version":"default_app","score":"10000"},{"version":"rankV3","score":"1739508329705000"},{"version":"ttp_rule_rerank","score":"1739508329705000"},{"version":"timestamp_desc","score":"98260491670295000"},{"version":"tikcast_community_comment_18866_v7_r655069","score":"1739508329705000"},{"version":"tikcast_community_comment_18866_v7_r655069_desc","score":"1739508329705000"},{"version":"idc_rule_rerank","score":"1739508329705000"},{"version":"v13_r712088","score":"1739508329705000"},{"version":"v12_r702075","score":"1739508329705000"}],"commentTag":["COMMENT_TAG_NORMAL"],"publicAreaMessageCommon":{"portraitInfo":{},"userInteractionInfo":{"likeCnt":"7731","commentCnt":"119","shareCnt":"8"}},"screenTime":"1739508323312","signature":"693780bbf3b4ae97f3a1255a05da53cc","signatureVersion":"v1"},"method":"WebcastChatMessage","pinTime":"1739508338047","operator":{"id":"6725502374993773573","userAttr":{}},"action":"Pin","displayDuration":"60","pinMsgId":"7471131345818438443"}
                    _decodeMessage = tiktok.webcast.RoomPinMessage.decode(msg.payload);
                    //this._inline_self_debug(JSON.stringify(_decodeMessage));
                }
                    // else if (msg.method === 'RoomMessage') {
                    //     _decodeMessage = tiktok.webcast.RoomMessage.decode(msg.payload);
                    //     if (_decodeMessage.isWelcome) {
                    //         // this._inline_self_debug(JSON.stringify(_decodeMessage));
                    //     }
                // }


                else {
                    this._inline_self_debug(msg.method);
                }
            } catch (e) {
                logCallback(_decodeMessage.common.method + ' message error:' + e.message + '\n' + JSON.stringify(_decodeMessage));
            }
        })
        if (eventsData.events_data.length > 0) {
            eventCallback(eventsData);
        }
    }

    /**
     * 处理用户徽章列表
     * @param {Array} badgeList - 用户徽章列表
     * @param {Object} _newMessage - 消息对象
     */
    _inline_processDebugUserBadges(badgeList, _newMessage) {
        badgeList.forEach(function (badge) {// repeated CombineBadge
            if (badge.displayType === tiktok.webcast.BadgeDisplayType.BADGE_DISPLAY_TYPE_COMBINE) {
                if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_UserGrade) {
                    // this._inline_self_debug(badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
                } else if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_Fans) {
                    // this._inline_self_debug(badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
                }

                if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_UserGrade) {
                    // this._inline_self_debug(badge.combine.displayType+","+badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
                } else if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_Fans) {
                    // this._inline_self_debug(badge.combine.displayType+","+badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
                } else if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_FirstRecharge) {
                    // New gifter
                    this._inline_self_debug(badge.combine.str + "," + (badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : '') + ', color:' + (badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : '') + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
                } else if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_Ranklist) {
                    // ranklist
                    this._inline_self_debug(badge.combine.str + "," + (badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : '') + ', color:' + (badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : '') + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
                } else if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_Subscriber) {
                    // Subscriber
                    this._inline_self_debug(badge.combine.str + "," + (badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : '') + ', color:' + (badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : '') + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
                } else if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_Admin) {
                    // Moderator
                    this._inline_self_debug(badge.combine.str + "," + (badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : '') + ', color:' + (badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : '') + ', icon:' + (badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : ''));
                } else {
                    this._inline_self_debug('else sceneType:' + JSON.stringify(badge));
                }
            }
        });
    }
    
    static _inline_packUser(ori_user) {
        var _user = {
            user_name: ori_user.displayId,
            nick_name: ori_user.nickname
        };

        if (ori_user.avatarThumb && ori_user.avatarThumb.urlList && ori_user.avatarThumb.urlList.length > 0) {
            _user.head_url = ori_user.avatarThumb.urlList[0];
        }
        // // TODO: 粉丝团信息:
        ori_user.badgeList.forEach(function (badge) {
            if (badge.displayType === tiktok.webcast.BadgeDisplayType.BADGE_DISPLAY_TYPE_COMBINE) {
                if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_UserGrade) {
                    _user.level = Number(badge.privilegeLogExtra.level);
                    // this._inline_self_debug(badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + badge.combine?.icon?.urlList[0]);
                } else if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_Fans) {
                    _user.fansclub = {};// debug_tkEvents-296088
                    _user.fansclub.level = badge.privilegeLogExtra.level;
                    if (Number(badge.greyedByClient) === 8) {// 过期
                        // 正常是没有的，有的话看到是 8
                        _user.fansclub.status = false;
                    } else {
                        _user.fansclub.status = true;
                    }
                }
            }
        });
        return _user;
    }

    static _inline_packCommon(msg_type, msg_type_str) {
        var _eventData = {};
        _eventData.msg_type = msg_type;
        _eventData.msg_type_str = msg_type_str;
        return _eventData;
    }

    // /**
    //  * 处理用户信息
    //  * @param {Object} _decodeMessage - 解码后的消息对象
    //  * @param {Object} _newMessage - 新消息对象
    //  */
    // processUserInfo(_decodeMessage, _newMessage) {
    //     _newMessage.user = pb_webcast.tkMsg.InteractiveUser.create();
    //     _newMessage.user.nickname = _decodeMessage.user.nickname;
    //     _newMessage.user.secUid = _decodeMessage.user.secUid;
    //     _newMessage.user.displayId = _decodeMessage.user.displayId;
    //     if (_decodeMessage.user.avatarThumb !== null && _decodeMessage.user.avatarThumb.urlList !== null && _decodeMessage.user.avatarThumb.urlList !== undefined) {
    //         _newMessage.user.avatarUrl = _decodeMessage.user.avatarThumb.urlList[0];
    //     }
    //     // if (_decodeMessage.user.fansClub !== null && _decodeMessage.user.fansClub.data !== null && _decodeMessage.user.fansClub.data !== undefined) {
    //     //     _newMessage.user.fansclubLevel = _decodeMessage.user.fansClub.data.level;
    //     // }
    //     if (_decodeMessage.user.payGrade !== null && _decodeMessage.user.payGrade !== undefined) {
    //         _newMessage.user.userPrivilegeLevel = _decodeMessage.user.payGrade.level;
    //     }
    //     if (_decodeMessage.user.badgeList !== undefined && _decodeMessage.user.badgeList !== null) {
    //         // processDebugUserBadges(_decodeMessage.user.badgeList, _newMessage);
    //         // 添加徽章数组
    //         _newMessage.user.combineBadges = [];
    //         _decodeMessage.user.badgeList.forEach(function (badge) {
    //             if (badge.displayType === tiktok.webcast.BadgeDisplayType.BADGE_DISPLAY_TYPE_COMBINE) {
    //                 if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_UserGrade) {
    //                     _newMessage.user.userPrivilegeLevel = Number(badge.privilegeLogExtra.level);
    //                     // this._inline_self_debug(badge.combine.str + ', color:' + badge.combine.background.backgroundColorCode + ', icon:' + badge.combine?.icon?.urlList[0]);
    //                 } else if (badge.sceneType === tiktok.webcast.BadgeSceneType.BadgeSceneType_Fans) {
    //                     _newMessage.user.fansclubLevel = Number(badge.privilegeLogExtra.level);
    //                 }
    //                 var combineBadge = {
    //                     sceneType: badge.sceneType,
    //                     str: badge.combine.str,
    //                     backgroundColorCode: badge.combine && badge.combine.background ? badge.combine.background.backgroundColorCode : null,
    //                     iconUrl: badge.combine && badge.combine.icon && badge.combine.icon.urlList && badge.combine.icon.urlList[0] ? badge.combine.icon.urlList[0] : null,
    //                     text: badge.combine && badge.combine.text ? badge.combine.text.defaultPattern : null,
    //                     level: badge.privilegeLogExtra ? badge.privilegeLogExtra.level : null
    //                 };
    //                 _newMessage.user.combineBadges.push(combineBadge);
    //             }
    //         });
    //     }
    // }

    // 测试
    testParseRaw(payload, live_url, eventCallback, logCallback) {
        if (this._inline_test_hex && payload !== undefined) {
            this._inline_decodePayload(payload, live_url, eventCallback, logCallback);
        }
    }
}
/// @secureEnd

module.exports = LiveTiktok;