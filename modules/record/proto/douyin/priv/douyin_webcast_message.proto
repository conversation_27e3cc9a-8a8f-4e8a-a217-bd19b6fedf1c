syntax = "proto3";
package douyin.priv;
// 8.9.1 -> 843.e9780b81.js

// msg 数据包二层解密
message Response {
    repeated Message messages = 1;
    string cursor = 2;
    int64 fetchInterval = 3;
    int64 now = 4;
    string internalExt = 5;
    int32 fetchType = 6;
    map<string, string> routeParams = 7;
    int64 heartbeatDuration = 8;
    bool needAck = 9;
    string pushServer = 10;
    string liveCursor = 11;
    bool historyNoMore = 12;
    string proxyServer = 13;
    string pushServer2 = 14;
}
message Message{
    string method = 1;
    bytes payload = 2;
    int64 msgId = 3;
    int32 msgType = 4;
    int64 offset = 5;
    bool needWrdsStore = 6;
    int64 wrdsVersion = 7;
    string wrdsSubKey = 8;
    map<string, string> messageExtra = 9;
}

// hb 数据包二层解密, 未测试
message HeartbeatPacket {
    repeated WrdsKeyVersion wrdsKeyVersions = 1;
    int64 roomID = 2;
}
message WrdsKeyVersion {
    string syncKey = 1;
    int64 version = 2;
}
