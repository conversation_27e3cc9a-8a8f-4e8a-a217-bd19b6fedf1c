syntax = "proto3";
package douyin.webcast;

// data
message MultiLiveCoreInfo {
    Scene scene = 1;
    VideoEqualRoomLiveCoreInfo videoEqualRoomLiveCoreInfo = 2;
    KTVLiveCoreInfo ktvLiveCoreInfo = 3;
    MultiAnchorLinkmicLiveCoreInfo multiAnchorLinkmicLiveCoreInfo = 4;
    SingingChallengeLiveCoreInfo singingChallengeLiveCoreInfo = 5;
    VideoDUOBattleLiveCoreInfo videoDuoBattleLiveCoreInfo = 6;
    VideoPositionLiveCoreInfo videoPositionLiveCoreInfo = 7;
    enum Scene {
        Scene_Unknown = 0;
        Scene_PK = 1;
        Scene_AnchorLinkmic = 2;
        Scene_VirutalPK = 3;
        Scene_AudienceLinkmic = 4;
        Scene_AudioChatRoom = 5;
        Scene_CloudGame = 6;
        Scene_AnchorMultiLinkmic = 7;
        Scene_VideoChatRoom = 8;
        Scene_KTV = 9;
        Scene_ShareVideo = 10;
        Scene_ShareKTV = 11;
        Scene_VideoEqualRoom = 12;
        Scene_VideoKTV = 13;
        Scene_GameAnchorLinkmic = 14;
        Scene_GameAnchorLinkmicWithLayout = 15;
        Scene_CrossRoomLinkmic = 16;
        Scene_MediaLinkmic = 17;
        Scene_AudioChannelRoom = 18;
        Scene_CommentaryLinkmic = 19;
        Scene_Default = 100;
    }
    message VideoEqualRoomLiveCoreInfo{
        map<int64,string> liveCoreInfoMap = 1;
    }
    message MultiAnchorLinkmicLiveCoreInfo{
        map<int64,string> liveCoreInfoMap = 1;
        map<int64,string> pushStreamModeLiveCoreMap = 2;
    }
    message KTVLiveCoreInfo{
        map<int64,string> liveCoreInfoMap = 1;
    }
    message SingingChallengeLiveCoreInfo{
        map<int64,string> liveCoreInfoMap = 1;
    }
    message VideoDUOBattleLiveCoreInfo{
        map<int64,string> liveCoreInfoMap = 1;
    }
    message VideoPositionLiveCoreInfo{
        map<int64,string> liveCoreInfoMap = 1;
    }
}
message BanUser {
    int64 userId = 1;
    string reason = 2;
    string url = 3;
    string anchorExplain = 4;
    string audienceExplain = 5;
    string schemaUrl = 6;
    string openId = 5000;
}
message JoinTeamfightInfo {
    int64 teamId = 1;
    string teamIdStr = 2;
}
message GameInviteInfo {
    int32 inviteSource = 1;
}
message MultiChannelInfo {
    bool useMultiChannel = 1;
    map<int64,string> linkmicInfos = 2;
}
message RoomLinkerContent {
    repeated ListUser linkedUsers = 1;
    int32 roomLinkSilenceStatus = 2;
    int64 anchorId = 3;
    int64 isAnchorBackground = 4;
    int64 version = 5;
    string anchorOpenId = 5000;
}
message MsgBoardItemInfo {
    int64 id = 1;
    string idStr = 2;
    User sender = 3;
    string content = 4;
    int32 processStatus = 5;
    int64 giftId = 6;
    int64 msgDisappearTime = 7;
}

message LinkmicMediaInfo {
    string rtcExtInfo = 1;
    string linkmicIdStr = 2;
    string liveCoreExtInfo = 3;
    MultiRtcInfo multiRtcInfo = 4;
    MultiLiveCoreInfo multiLiveCoreInfo = 5;
}

message MCUContent {
    string currentStreamId = 1;
    string rivalsStreamId = 2;
}

message BreakthroughInfo {
    int64 stage = 1;
    int64 totalScore = 2;
    int64 mvpAnchorId = 3;
    State state = 4;
    map<int64, StageInfo> stageInfoMap = 5;
    string mvpAnchorOpenId = 5000;
    enum State{
        Running = 0;
        Success = 1;
        Failure = 2;
        Unsettled = 3;
    }
    message StageInfo{
        int64 finishTimeDelta = 1;
    }
}
message LinkerBaseInfo {
    Scene scene = 1;
    repeated int64 playModes = 2;
    UILayout uiLayout = 3;
    int64 MaxMemberCount = 4;
    int64 anchorManualOpenUiLayout = 5;
    int64 crossLinkTimerStart = 6;
    string functionType = 7;
    int64 beforeSceneStart = 8;
    enum Scene {
        Scene_Unknown = 0;
        Scene_PK = 1;
        Scene_AnchorLinkmic = 2;
        Scene_VirutalPK = 3;
        Scene_AudienceLinkmic = 4;
        Scene_AudioChatRoom = 5;
        Scene_CloudGame = 6;
        Scene_AnchorMultiLinkmic = 7;
        Scene_VideoChatRoom = 8;
        Scene_KTV = 9;
        Scene_ShareVideo = 10;
        Scene_ShareKTV = 11;
        Scene_VideoEqualRoom = 12;
        Scene_VideoKTV = 13;
        Scene_GameAnchorLinkmic = 14;
        Scene_GameAnchorLinkmicWithLayout = 15;
        Scene_CrossRoomLinkmic = 16;
        Scene_MediaLinkmic = 17;
        Scene_AudioChannelRoom = 18;
        Scene_CommentaryLinkmic = 19;
        Scene_Default = 100;
    }
    enum UILayout {
        LinkmicUILayout_Default = 0;
        LinkmicUILayout_VideoChatFloatWindowLayout = 1;
        LinkmicUILayout_VideoEqualDynamicLayout = 2;
        LinkmicUILayout_VideoEqual_Left1V8 = 3;
        LinkmicUILayout_VideoEqual_Top1V8 = 4;
        LinkmicUILayout_VideoEqual_3X3 = 5;
        LinkmicUILayout_VideoChat_EnlargeGuest1V6 = 6;
        LinkmicUILayout_VideoChat_DynamicLayout1V6 = 7;
        LinkmicUILayout_VideoChat_Old1V6 = 8;
        LinkmicUILayout_VideoChat_KTVSingingChallenge = 9;
        LinkmicUILayout_VideoChat_2v4 = 10;
        LinkmicUILayout_VideoEqual_Left1V3 = 11;
        LinkmicUILayout_VideoEqual_Left1V6 = 12;
        LinkmicUILayout_VideoChat_DynamicLayout1V6_Horizontal = 13;
        LinkmicUILayout_VideoChat_DynamicLayout1V6_Horizontal_4_3 = 14;
        LinkmicUILayout_VideoChat_Dynamic1V7 = 15;
        LinkmicUILayout_AudioChat_1V8 = 21;
        LinkmicUILayout_AudioChat_3X3 = 22;
        LinkmicUILayout_AudioChat_Dynamic = 23;
        LinkmicUILayout_AudioChat_Cycle = 24;
        LinkmicUILayout_AudioChat_PC_Customize_1V6 = 25;
        LinkmicUILayout_AudioChat_Top2v4v4 = 26;
        LinkmicUILayout_AudioChat_1V8_Stage = 27;
        LinkmicUILayout_AudioChat_Left1v8 = 28;
        LinkmicUILayout_AudioChat_1v6 = 29;
        LinkmicUILayout_KTV_MainStage = 31;
        LinkmicUILayout_Video_1V1 = 41;
        LinkmicUILayout_Video_2V2 = 42;
        LinkmicUILayout_Video_Top2v4v4 = 43;
        LinkmicUILayout_CrossRoomLinkmic_6V6 = 51;
        LinkmicUILayout_RealTimeChorus = 61;
        LinkmicUILayout_VideoEqual_1V1 = 102;
        LinkmicUILayout_VideoEqual_1V2 = 103;
        LinkmicUILayout_VideoEqual_2V2 = 104;
        LinkmicUILayout_VideoEqual_Top3V2 = 105;
        LinkmicUILayout_VideoEqual_Top3V3 = 106;
        LinkmicUILayout_VideoEqual_Top3V3V1 = 107;
        LinkmicUILayout_VideoEqual_Top3V3V2 = 108;
        LinkmicUILayout_VideoEqual_DynamicLayout1V6Plus = 109;
        LinkmicUILayout_VideoEqual_DynamicLayout1V6_Compact = 110;
        LinkmicUILayout_VideoEqual_DynamicLayout1V6Plus_Compact = 111;
        LinkmicUILayout_VideoEqual_DynamicLayout1V8Fix = 112;
        LinkmicUILayout_VideoChat_Dynamic1V7_Stash_Hold = 113;
        LinkmicUILayout_VideoChat_1V6_PICO = 200;
        LinkmicUILayout_VideoChat_DynamicLayout1V6_InPK = 52;
    }
}
message AudienceActionSource {
    AudienceInviteSource inviteSource = 1;
    AudiencePermitSource permitSource = 2;
    enum AudienceInviteSource {
        AudienceInviteSourceNone = 0;
        AudienceInviteSourceOrigin = 1;
        AudienceInviteSourceGamePannel = 2;
        AudienceInviteSourcePublicAreaComment = 3;
        AudienceInviteSourceUserSearch = 4;
        AudienceInviteSourceRealTimeChorus = 5;
    }
    enum AudiencePermitSource {
        AudiencePermitSourceNone = 0;
        AudiencePermitSourcePriorsong = 1;
        AudiencePermitSourceOrigin = 2;
        AudiencePermitSourcePublicAreaComment = 3;
        AudiencePermitSourceAutoRotate = 11;
        AudiencePermitSourceAutoRotateChatMatch = 12;
        AudiencePermitSourceAutoRotateKTVSong = 13;
        AudiencePermitSourceAutoRotateMultiChorus = 14;
        AudiencePermitSourceAutoRotateStrongReach = 15;
        AudiencePermitSourceAutoRotatePrepareApply = 16;
        AudiencePermitSourceAutoRotateOChannelHost = 17;
    }
}
message BattleBarConfig {
    string leftColor = 1;
    string rightColor = 2;
    string leftScoreViewColor = 3;
    string rightScoreViewColor = 4;
    AnimeInfo animeInfo = 5;
}
message AnimeInfo{
    int32 barType = 1;
    int32 auraType = 2;
    string normalBarUrl = 3;
    string buffBarUrl = 4;
    string stealTowerBarUrl = 5;
    string curBarUrl = 6;
    string curAuraUrl = 7;
}
message AuxiliaryScoreInfo {
    string anchorScore = 1;
    Image auxiliaryImg = 2;
    int64 index = 3;
    int64 giftId = 4;
    int64 auxiliaryType = 5;
    int64 anchorScoreInt = 6;
    bool noNeedUpdateScore = 7;
}
message LinkGameInfo {
    string gameAppId = 1;
    string gameExtra = 2;
    string openId = 3;
    string initParam = 4;
    string linkExtra = 5;
    LinkMicBizExtra linkBizExtra = 6;
}
message LinkMicBizExtra{
    OpenGameConfig openGameConfig = 1;
}
message OpenGameConfig{
    string appId = 1;
    string appExtra = 2;
    Image iconImage = 3;
    string gameTitle = 4;
}
message LinkmicJoinChannelData {
    string prompt = 1;
    DelegateSetting delegateSetting = 2;
    string token = 3;
    string liveCoreSettings = 4;
    int64 payCount = 5;
    repeated ListUser linkedUsers = 6;
    uint64 version = 7;
    int32 applyType = 8;
    MatchEffect matchEffect = 9;
    CityEffect cityEffect = 10;
    MsgBoardItemInfo msgBoardItem = 11;
    bool isPaidLinkmic = 12;
    string promptWithEarphone = 13;
    LinkGameInfo linkGameInfo = 15;
}
message DelegateSetting{
    bool showPanel = 1;
    bool enableDelegate = 2;
    Image tagImageUrl = 3;
    string prompt = 4;
    Status status = 5;
    enum Status{
        SETTING_UNSET = 0;
        SETTING_OPEN = 1;
        SETTING_OFF = 2;
        SETTING_DELEGATED_TO_EVERYONE = 50;
    }
}
message MatchEffect{
    bool showEffect = 1;
    Image effectResource = 2;
}
message CityEffect{
    Image effectResource = 1;
    string city = 2;
    repeated int64 showEffectUserIdList = 3;
    repeated string showEffectOpenIdList = 5000;
}
message ChorusCDNInfo {
    string stream = 1;
    PlayInfo playInfo = 2;
    PushInfo pushInfo = 3;
    message PlayInfo{
        string streamData = 1;
    }
    message PushInfo{
        repeated string mainUrls = 1;
        string pushUrl = 2;
    }
}

message ListUser {
    User user = 1;
    int64 linkmicId = 2;
    string linkmicIdStr = 3;
    LinkStatus linkStatus = 4;
    LinkType linkType = 5;
    int32 userPosition = 6;
    SilenceStatus silenceStatus = 7;
    int64 modifyTime = 8;
    int64 linkerId = 9;
    int32 roleType = 10;
    Content content = 11;
    int64 mcStatus = 12;
    int64 isBackground = 13;
    LinkHostInfo host = 14;
    MicPosTagInfo micPosTagInfo = 15;
    bool pkAvailable = 16;
    string linkmicUniqueId = 17;
    int64 kSongUid = 18;
    string extra = 200;
    message Content{
        ListUserPKContent pkContent = 1;
        ListUserLinkmicContent linkmicContent = 2;
        ListUserLinkmicAudienceContent linkmicAudienceContent = 3;
    }
    enum LinkStatus {
        STATUS_UNKNOWN = 0;
        STATUS_WAITING = 1;
        STATUS_LINKED = 2;
        STATUS_FINISHED = 3;
        STATUS_WAITING_OR_LINKED = 4;
    }
    enum LinkType {
        TYPE_UNKNOWN = 0;
        TYPE_VIDEO = 1;
        TYPE_AUDIO = 2;
        TYPE_VIRTUAL = 3;
        TYPE_CHORUS = 4;
    }
    enum SilenceStatus {
        STATUS_UNSILENCE = 0;
        STATUS_SILENCE_BY_SELF = 1;
        STATUS_SILENCE_BY_OWNER = 2;
        STATUS_SILENCE_PASSIVE = 3;
        STATUS_SILENCE_BY_GAME = 4;
    }
}

message ListUserPKContent {
    // empty
}

message ListUserLinkmicContent {
    int64 resourceId = 1;
    bool showIdentity = 2;
    int32 pkUserRole = 3;
    string gameName = 4;
    string fanTicket = 5;
    int64 joinChannelTime = 6;
    AnchorLinkmicIDInfo anchorLinkmicIdInfo = 7;
    int32 anchorAuthRole = 8;
}
message AnchorLinkmicIDInfo{
    string mainLinkmicIdStr = 1;
    string backupLinkmicIdStr = 2;
    bool useBackupStream = 3;
}

message ListUserLinkmicAudienceContent {
    int64 fanTicket = 1;
    bool hostPermission = 2;
    int64 appId = 3;
    int64 clientVersion = 4;
    string devicePlatform = 5;
    int64 joinChannelTime = 6;
    int64 expectedLeaveTime = 7;
    bool inWaitingList = 8;
    string extra = 9;
    int64 currentTime = 10;
    string fanTicketFuzzyStr = 11;
    int32 listUserRole = 12;
    string fanTicketRealStr = 13;
    repeated int64 rankContributorIds = 14;
    MicDress micDress = 15;
    int64 paidCount = 16;
    bool isEnlarged = 17;
    int64 selfDisciplineDuration = 18;
    bool isAnonymous = 19;
    int32 applyType = 20;
    int64 selfDisciplineStartTimeMs = 21;
    int64 selfDisciplineStartTime = 22;
    int64 selfDiscriplineTargetDuration = 23;
    string deviceIdStr = 24;
    int32 themedCompetitionRole = 25;
    Linkmic2DAvatar linkmic_2dAvatar = 26;
    int64 chorusGuestStatus = 27;
    repeated string rankContributorOpenIds = 5000;
}
message MicDress{
    VoiceWave voiceDress = 1;
    VoiceWave voiceWave = 2;
    LinkmicBadge badge = 3;
    LinkmicCustomizedRandomEmoji customizedRandomEmoji = 4;
}
message VoiceWave {
    string dressId = 1;
    Image lowImg = 2;
    Image mediumImg = 3;
    Image highImg = 4;
}

message LinkmicBadge {
    string dressId = 1;
    int32 showType = 2;
}

message LinkmicCustomizedRandomEmoji {
    Status status = 1;
    int64 refreshTimestamp = 2;
    Image emojiStatusImage = 3;
    Image emojiDisplaydDynamicImage = 4;
    int64 randomNum = 5;
    int64 type = 6;
    string triggerReason = 7;
    enum Status {
        CustomizedRandomEmojiStatus_Unknown = 0;
        CustomizedRandomEmojiStatus_Hidden = 1;
        CustomizedRandomEmojiStatus_Display = 2;
    }
}
message Linkmic2DAvatar{
    Image image = 1;
    bool enableAvatar = 2;
}
message LinkmicPositionItem {
    int64 position = 1;
    int32 status = 2;
    string activeName = 3;
    int32 verifyStatus = 4;
    int32 positionType = 5;
    int64 clientUiPosition = 6;
}

message MultiRtcInfo {
    int32 scene = 1;
    VideoEqualRoomRtcInfo videoEqualRoomRtcInfo = 2;
    MultiAnchorLinkmicRtcInfo multiAnchorLinkmicRtcInfo = 3;
    SingingChallengeRtcInfo singingChallengeRtcInfo = 4;
    CrossRoomLinkmicRtcInfo crossRoomLinkmicRtcInfo = 5;
    VideoDUOBattleRtcInfo videoDuoBattleRtcInfo = 6;
    VideoPositionRtcInfo videoPositionRtcInfo = 7;
    GameBarrageRtcInfo gameBarrageRtcInfo = 8;
}

message LinkHostInfo{
    int64 isHost = 1;
    string hostPositionName = 2;
}
message MicPosTagInfo{
    int32 tagType = 1;
    string tagName = 2;
    string textColorValue = 3;
    string bgColorValue = 4;
    int64 tagTypeId = 5;
    Image bgColorImg = 6;
    bool openTagDescPage = 7;
    string schema = 8;
}
message RanklistHourEntrance{
    repeated Info globalInfos = 1;
    repeated Info defaultGlobalInfos = 2;
    repeated Info verticalInfos = 3;
    repeated Info defaultVerticalInfos = 4;
    message Info{
        repeated Detail details = 1;
    }
    message Detail{
        repeated Page pages = 1;
        RanklistType ranklistType = 2;
        string title = 3;
        string ranklistExtra = 4;
        string entranceExtra = 5;
        string schema = 6;
        string iconUrl = 7;
    }
    message Page{
        string content = 1;
        string backgroundColor = 2;
        int64 showTimes = 3;
        int32 contentType = 4;
    }
    enum RanklistType {
        Hour = 0;
        RegionHour = 1;
        Shop = 2;
        Popularity = 3;
        Ktv = 4;
        GameCpCarry = 5;
        NewAnchor = 6;
        TeamFight = 7;
        SellCourse = 8;
        Goods = 9;
        Game = 10;
        ChatRoom = 11;
        VideoChatRoom = 12;
        AudioChatRoom = 13;
        GameHot = 14;
        GameHotPartition = 15;
        GameCpCarryPartition = 16;
        LifeLive = 17;
        ElectronicBusinessPromotion20220921 = 18;
        Commerce = 19;
        Talent = 20;
        EcomShop = 21;
        LifeLiveBusiness = 22;
        Barrage = 23;
        VideoGuest = 24;
        AudioGuest = 25;
        BarrageMulA = 26;
        BarrageMulB = 27;
        BarrageMulC = 28;
        BarrageMulD = 29;
        BarrageMulE = 30;
        BarrageMulF = 31;
        AutomobilePopularity = 32;
        DcdKolIncome = 33;
        BestPartner = 34;
        BestPartnerLast = 35;
        PassengerMVP = 36;
        Passenger = 37;
        LifeLiveBusinessV2 = 40;
        LifeLiveBusinessV3 = 41;
        LifeLiveBusinessV4 = 42;
        Chorus = 43;
        LivePlayApp = 44;
    }
}

message RivalExtraInfo {
    string text = 1;
    TextType textType = 2;
    string label = 3;
    AnchorLayer anchorLayer = 4;
    LinkerInfo linkerInfo = 5;
    AnchorLinkmicUserSettings linkmicUserSettings = 6;
    BattleUserSettings battleUserSettings = 7;
    int64 videoShowScore = 8;
    GameInfo gameInfo = 9;
    string signExtra = 10;
    string recommendInfo = 11;
    int32 fromServerInviteType = 12;

    enum TextType {
        TextTypeUnknown = 0;
        CurRoomFanTicket = 1;
        TotalDiamondCount = 2;
        Distance = 3;
        DistanceCity = 4;
    }
    enum AnchorLayer {
        AnchorLayerUnknown = 0;
        AnchorLayerTop = 1;
        AnchorLayerSMALL = 2;
    }
    enum SignExtra {
        TypeNoUse = 0;
        TypeMatchFollow = 101;
        TypeMatchRecommend = 102;
        TypeFollow = 103;
        TypeRecommend = 104;
        TypeSingleFollow = 105;
    }
    message LinkerInfo {
        repeated UserInfo linkedUsers = 1;
        message UserInfo{
            int64 userId = 1;
            string nickName = 2;
            Image avatarThumb = 3;
            string openId = 5000;
        }
    }
    message GameInfo {
        string gameText = 1;
        string gameName = 2;
    }
}

message AnchorLinkmicUserSettings {
    bool isTurnOn = 1;
    bool acceptMultiLinkmic = 2;
    bool acceptNotFollowerInvite = 3;
    bool allowGiftToOtherAnchors = 4;
    bool acceptFriendInvite = 5;
    bool acceptHourlyRankInvite = 6;
    bool acceptPopularityRankInvite = 7;
    bool acceptRecommendSearchInvite = 8;
    bool acceptBigSmallLayout = 9;
    bool linkmicAllowGiftToOtherAnchors = 10;
    bool pkAllowGiftToOtherAnchors = 11;
}

message BattleUserSettings {
    bool isTurnOn = 1;
    bool acceptNotFollowerInvite = 2;
    bool allowGiftToOtherAnchors = 3;
    bool acceptFriendInvite = 4;
    bool acceptHourlyRankInvite = 5;
    bool acceptPopularityRankInvite = 6;
    int64 inviteRejectPermanentTotal = 7;
    int64 inviteRejectTemporaryTotal = 8;
    string inviteRejectTemporaryText = 9;
    bool pkAcceptVolumeUp = 10;
    bool pkAutoConfirm = 11;
    bool acceptUnionRecommendInvite = 12;
    bool linkmicAllowGiftToOtherAnchors = 13;
    bool pkAllowGiftToOtherAnchors = 14;
}

message AnchorLinkmicInfo {
    string rtcExtInfo = 1;
    string linkmicIdStr = 2;
    bool useBackupStream = 3;
}
message ChatLikeCount {
    int64 count = 1;
    int64 version = 2;
}
message FreeGift {
    int64 id = 1;
    int64 count = 2;
    string content = 3;
    int64 group_id = 4;
    int64 repeat_count = 5;
    int64 fan_tickets = 6;
    int64 waterwmlon = 7;
}
message ChatReplyRespInfo{
    int64 replyMsgId = 1;
    int64 replyId = 2;
    Text replyText = 3;
    int64 replyUid = 4;
    string replyWebcastUid = 5;
    string replyOpenId = 5000;
}
message ChatMentionInfo{
    repeated string mentionUserIds = 1;
    repeated string mentionWebcastUserIds = 2;
    int64 showTime = 3;
}
message Text{
    string key = 1;
    string defaultPattern = 2;
    TextFormat defaultFormat = 3;
    repeated TextPiece pieces = 4;
    map<string, SchemaInfo> schemaInfos = 20;
}
message SchemaInfo {
    string schemaUrl = 1;
}
message TextFormat{
    string color = 1;
    bool bold = 2;
    bool italic = 3;
    int32 weight = 4;
    int32 italicAngle = 5;
    int32 fontSize = 6;
    bool userHeightLightColor = 7;
    bool useRemoteClor = 8;
    bool ignoreColorMapping = 9;
}
message TextPiece{
    int32 type = 1;
    TextFormat format = 2;
    string valueRef = 3;
    string stringValue = 11;// type == 1 string
    TextPieceUser userValue = 21;// type == 11 user
    TextPieceGift giftValue = 22;
    TextPieceHeart heartValue = 23;
    TextPiecePatternRef patternRefValue = 24;
    TextPieceImage imageValue = 25;// type == 15 image
    string schemaKey = 100;
}
message TextPieceGift{
    int64 giftId = 1;
    PatternRef nameRef = 2;
}
message PatternRef{
    string key = 1;
    string defaultPattern = 2;
}
message TextPieceHeart{
    string color = 1;
}
message TextPiecePatternRef{
    string key = 1;
    string defaultPattern = 2;
}
message TextPieceImage{
    Image image = 1;
    float scalingRate = 2;
}
message TextPieceUser{
    User user = 1;
    bool withColon = 2;
}
message AnchorGiftData {
    Image anchorDiyOriginImg = 1;
}
message SendTogether{
    string id = 1;
    int64 startTime = 2;
    int64 endTime = 3;
}
message ExtraEffect{
    int64 assetId = 1;
    int32 displayForm =2;
}
message RoomHotInfo {
    int32 localHotStrategy = 1;
    int32 publicAreaLevel = 2;
    int32 giftLevel = 3;
}
// 直播伴侣的字段和web的字段不一样。
//message GiftInfo {
//    int64 giftId = 1;
//    string giftName = 2;
//    string giftImgUrl = 3;
//    int64 diamondCount = 4;
//}
// web
message GiftInfo {
    int64 gift_id = 1;
    Image gift_icon = 2;
    int64 diamond_count = 4;
}
message SeriesTrayInfo {
    int64 duration = 1;
    Image staticImg = 2;
    Image dynamicImg = 3;
}
message SeriesPlayGift{
    GiftStruct giftStruct = 1;
    SeriesTrayInfo seriesTrayInfo = 2;
    SendTogether sendTogether = 3;
    string diyItemInfo = 4;
    AnchorGiftData anchorGift = 5;
    AssetEffectMixInfo assetEffectMixInfo = 6;
}
message User{
    int64 id = 1;
    int64 shortId = 2;
    string nickname = 3;
    int32 gender = 4;
    string signature = 5;
    int32 level = 6;
    int64 birthday = 7;
    string telephone = 8;
    Image avatarThumb = 9;
    Image avatarMedium = 10;
    Image avatarLarge = 11;
    bool verified = 12;
    int32 experience = 13;
    string city = 14;
    int32 status = 15;
    int64 createTime = 16;
    int64 modifyTime = 17;
    int32 secret = 18;
    string shareQrcodeUri = 19;
    int32 incomeSharePercent = 20;
    repeated Image badgeImageList = 21;
    FollowInfo followInfo = 22;
    PayGrade payGrade = 23;
    FansClub fansClub = 24;
    Border border = 25;
    string specialId = 26;
    Image avatarBorder = 27;
    Image medal = 28;
    repeated Image realTimeIcons = 29;
    repeated Image newRealTimeIcons = 30;
    int64 topVipNo = 31;
    UserAttr userAttr = 32;
    OwnRoom ownRoom = 33;
    int64 payScore = 34;
    int64 ticketCount = 35;
    AnchorInfo anchorInfo = 36;
    int32 linkMicStats = 37;
    string displayId = 38;
    bool withCommercePermission = 39;
    bool withFusionShopEntry = 40;
    int64 totalRechargeDiamondCount = 41;
    AnchorLevel webcastAnchorLevel = 42;
    string verifiedContent = 43;
    AuthorStats authorStats = 44;
    repeated User topFans = 45;
    string secUid = 46;
    int32 userRole = 47;
    XiguaParams xiguaInfo = 48;
    ActivityInfo activityReward = 49;
    NobleLevelInfo nobleInfo = 50;
    BrotherhoodInfo brotherhoodInfo = 51;
    Image personalCard = 52;
    AuthenticationInfo authenticationInfo = 53;
    int32 authorizationInfo = 54;
    int32 adversaryAuthorizationInfo = 55;
    PoiInfo poiInfo = 56;
    repeated Image mediaBadgeImageList = 57;
    int32 adversaryUserStatus = 58;
    UserVIPInfo userVipInfo = 59;
    repeated int64 commerceWebcastConfigIds = 60;
    repeated Image badgeImageListV2 = 61;
    IndustryCertification industryCertification = 62;
    string locationCity = 63;
    FansGroupInfo fansGroupInfo = 64;
    string remarkName = 65;
    int32 mysteryMan = 66;
    string webRid = 67;
    string desensitizedNickname = 68;
    JAccreditInfo jAccreditInfo = 69;
    Subscribe subscribe = 70;
    bool isAnonymous = 71;
    int32 consumeDiamondLevel = 72;
    string webcastUid = 73;
    ProfileStyleParams profileStyleParams = 74;
    UserDressInfo userDressInfo = 75;
    BizRelation bizRelation = 76;
    MemberEntranceInfo memberEntranceInfo = 77;
    PublicAreaBadgeInfo publicAreaBadgeInfo = 78;
    ExtraInfo extraInfo = 79;
    UserSettingInfo userSettingInfo = 80;
    int64 publicAreaOperFreq = 81;
    UserPermissionGrant userPermissionGrantInfo = 82;
    bool userCanceled = 83;
    bool allowBeLocated = 1001;
    bool allowFindByContacts = 1002;
    bool allowOthersDownloadVideo = 1003;
    bool allowOthersDownloadWhenSharingVideo = 1004;
    bool allowShareShowProfile = 1005;
    bool allowShowInGossip = 1006;
    bool allowShowMyAction = 1007;
    bool allowStrangeComment = 1008;
    bool allowUnfollowerComment = 1009;
    bool allowUseLinkmic = 1010;
    AnchorLevel anchorLevel = 1011;
    Image avatarJpg = 1012;
    string bgImgUrl = 1013;
    string birthdayDescription = 1014;
    bool birthdayValid = 1015;
    int32 blockStatus = 1016;
    int32 commentRestrict = 1017;
    string constellation = 1018;
    int32 disableIchat = 1019;
    int64 enableIchatImg = 1020;
    int32 exp = 1021;
    int64 fanTicketCount = 1022;
    bool foldStrangerChat = 1023;
    int64 followStatus = 1024;
    bool hotsoonVerified = 1025;
    string hotsoonVerifiedReason = 1026;
    int32 ichatRestrictType = 1027;
    string idStr = 1028;
    bool isFollower = 1029;
    bool isFollowing = 1030;
    bool needProfileGuide = 1031;
    int64 payScores = 1032;
    bool pushCommentStatus = 1033;
    bool pushDigg = 1034;
    bool pushFollow = 1035;
    bool pushFriendAction = 1036;
    bool pushIchat = 1037;
    bool pushStatus = 1038;
    bool pushVideoPost = 1039;
    bool pushVideoRecommend = 1040;
    UserStats stats = 1041;
    bool verifiedMobile = 1042;
    string verifiedReason = 1043;
    bool withCarManagementPermission = 1044;
    int32 ageRange = 1045;
    int64 watchDurationMonth = 1046;
    string userOpenId = 5000;
    OpenHostInfo hostInfo = 5001;

    message Border {
        Image icon = 1;
        int64 level = 2;
        Image thumbIcon = 3;
        string dressId = 4;
    }
    message UserAttr {
        bool isMuted = 1;
        bool isAdmin = 2;
        bool isSuperAdmin = 3;
        repeated int32 adminPrivileges = 4;
    }

    message OwnRoom {
        repeated int64 roomIds = 1;
        repeated string roomIdsStr = 2;
        repeated int32 roomIdsDisplay = 3;
    }

    message AnchorInfo {
        int64 level = 1;
    }
    message AnchorLevel {
        int64 level = 1;
        int64 experience = 2;
        int64 lowestExperienceThisLevel = 3;
        int64 highestExperienceThisLevel = 4;
        int64 taskStartExperience = 5;
        int64 taskStartTime = 6;
        int64 taskDecreaseExperience = 7;
        int64 taskTargetExperience = 8;
        int64 taskEndTime = 9;
        Image profileDialogBg = 10;
        Image profileDialogBgBack = 11;
        Image stageLevel = 12;
        Image smallIcon = 13;
    }
    message AuthorStats{
        int64 videoTotalCount = 1;
        int64 videoTotalPlayCount = 2;
        int64 videoTotalShareCount = 3;
        int64 videoTotalSeriesCount = 4;
        int64 varietyShowPlayCount = 5;
        int64 videoTotalFavoriteCount = 6;
    }
    message XiguaParams{
        string userAuthInfo = 1;
        int64 ugcPublishMediaId = 2;
        int64 mediaId = 3;
        string authorDesc = 4;
        string description = 5;
        bool userVerified = 6;
        UserExtendInfo userExtendInfo = 7;
        int64 xiguaUid = 8;
        string nickName = 9;
        Image avatarThumb = 10;
        Image avatarMedium = 11;
        Image avatarLarge = 12;
        message UserExtendInfo {
            string shareUrl = 1;
            string rSchemaUrl = 2;
            RocketSchema rocketSchemaInfo = 3;
            message RocketSchema{
                string rSchema = 1;
                string rToken = 2;
                string downloadUrl = 3;
            }
        }
    }
    message ActivityInfo {
        Image Badge = 1;
        Image StoryTag = 2;
    }
    message AuthenticationInfo {
        string customVerify = 1;
        string enterpriseVerifyReason = 2;
        Image authenticationBadge = 3;
        repeated int32 levelList = 4;
        AccountTypeInfo accountTypeInfo = 10;
        Image authenticationBadgeV2 = 11;
        string accountCertInfo = 12;
        message AccountTypeInfo{
            map<int64, bool> accountTypeMap = 1;
        }
    }
    message PoiInfo {
        bool isPoiEnabled = 1;
        int64 poiId = 2;
        string poiName = 3;
        string poiIdStr = 4;
        int64 followerCountPermission = 5;
        int64 whiteUserPermission = 6;
    }
    message UserVIPInfo {
        int64 vipLevel = 1;
        string vipLevelName = 2;
        int32 status = 3;
        int64 startTime = 4;
        int64 endTime = 5;
        int64 remainingDays = 6;
        int64 totalConsume = 7;
        int64 targetConsume = 8;
        VIPBadge badge = 9;
        map<string, bool> privileges = 10;
    }
    message FansGroupInfo {
        string listFansGroupUrl = 1;
    }
    message JAccreditInfo {
        int32 JAccreditBasic = 1;
        int32 JAccreditAdvance = 2;
        int32 JAccreditContent = 3;
        int32 JAccreditLive = 4;
    }
    message ProfileStyleParams {
        int64 profileStyle = 1;
        int64 smartSwitch = 2;
    }
    message UserDressInfo {
        repeated string dressWearIds = 1;
        repeated string dressOwnIds = 2;
    }
    message BizRelation {
        bool shopFansClubReverse = 1;
    }
    message MemberEntranceInfo {
        int64 type = 1;
        bool showEntrance = 2;
        string schema = 3;
        Image singleEntranceIcon = 4;
        Image doubleEntranceIcon = 5;
        bool isMember = 6;
        map<string, string> trackingParams = 50;
    }
    message PublicAreaBadgeInfo {
        map<int32, Image> badgeInfoMap = 1;
        repeated int64 badgeList = 2;
        repeated WebcastDslBadgeList webcastDslBadgeList = 3;
        message WebcastDslBadgeList{
            repeated int64 badgeList = 1;
            DslRule targetDslRule = 2;
            DslRule filterDslRule = 3;
            message DslRule{
                repeated int64 appId = 1;
                repeated int64 versionCode = 2;
                map<string, string> abParams = 3;
            }
        }
    }
    message ExtraInfo {
        bool isContract = 1;
    }
    message UserSettingInfo {
        int64 liveVisitorSwitch = 1;
    }
    message UserPermissionGrant {
        bool hideOnPrivateRoom = 1;
    }
    message UserStats {
        int64 id = 1;
        string idStr = 2;
        int64 followingCount = 3;
        int64 followerCount = 4;
        int64 recordCount = 5;
        int64 totalDuration = 6;
        int64 dailyFanTicketCount = 7;
        int64 dailyIncome = 8;
        int64 itemCount = 9;
        int64 favoriteItemCount = 10;
        int64 diamondCount = 11;
        int64 diamondConsumedCount = 12;
        int64 tuwenItemCount = 13;
        string openId = 5000;
        string openIdStr = 5001;
    }
    message NobleLevelInfo{
        Image nobleBackground = 1;
        int64 nobleLevel = 2;
        Image nobleIcon = 3;
        string nobleName = 4;
        int64 expireTime = 5;
        Image nobleBigIcon = 6;
        Image nobleIconWithBack = 7;
        Image nobleBoarder = 8;
        repeated string nobleBackgroundColor = 9;
    }
    message BrotherhoodInfo {
        string name = 1;
        int64 level = 2;
        Image background = 3;
        string fontColor = 4;
    }
    message Subscribe {
        bool isMember = 1;
        int64 level = 2;
        int64 identityType = 3;
        int64 buyType = 4;
        int64 open = 5;
    }
    message FollowInfo {
        int64 followingCount = 1;
        int64 followerCount = 2;
        int64 followStatus = 3;
        int64 pushStatus = 4;
        string remarkName = 5;
        string followerCountStr = 6;
        string followingCountStr = 7;
        bool invalidFollowStatus = 8;
    }
    message FansClub{
        FansClubData data = 1;
        map<int32, FansClubData> preferData = 2;

        message FansClubData {
            string clubName = 1;
            int32 level = 2;
            int32 userFansClubStatus = 3;
            UserBadge badge = 4;
            repeated int64 availableGiftIds = 5;
            int64 anchorId = 6;
            int32 badgeType = 7;
            string anchorOpenId = 5000;

            message UserBadge {
                map<int32, Image> icons = 1;
                string title = 2;
            }
        }
    }
    message GradeBuffInfo {
        int64 buffLevel = 1;
        int32 status = 2;
        int64 endTime = 3;
        map<int64, int64> statsInfo = 4;
        Image buffBadge = 5;
    }
    message PayGrade {
        int64 totalDiamondCount = 1;
        Image diamondIcon = 2;
        string name = 3;
        Image icon = 4;
        string nextName = 5;
        int64 level = 6;
        Image nextIcon = 7;
        int64 nextDiamond = 8;
        int64 nowDiamond = 9;
        int64 thisGradeMinDiamond = 10;
        int64 thisGradeMaxDiamond = 11;
        int64 payDiamondBak = 12;
        string gradeDescribe = 13;
        repeated GradeIcon gradeIconList = 14;
        int64 screenChatType = 15;
        Image imIcon = 16;
        Image imIconWithLevel = 17;
        Image liveIcon = 18;
        Image newImIconWithLevel = 19;
        Image newLiveIcon = 20;
        int64 upgradeNeedConsume = 21;
        string nextPrivileges = 22;
        Image background = 23;
        Image backgroundBack = 24;
        int64 score = 25;
        GradeBuffInfo buffInfo = 26;
        string gradeBanner = 1001;
        Image profileDialogBg = 1002;
        Image profileDialogBgBack = 1003;

        message GradeIcon{
            Image icon = 1;
            int64 iconDiamond = 2;
            int64 level = 3;
            string levelStr = 4;
        }
    }
}
message UserVIPInfo{
    int64 vipLevel = 1;
    string vipLevelName = 2;
    int32 status = 3;
    int64 startTime = 4;
    int64 endTime = 5;
    int64 remainingDays = 6;
    int64 totalConsume = 7;
    int64 targetConsume = 8;
    VIPBadge badge = 9;
    map<int64, bool> privileges = 10;
}
message VIPBadge{
    map<int64, Image> icons = 1;
}
message Image{
    repeated string urlList = 1;
    string uri = 2;
    int64 height = 3;
    int64 width = 4;
    string avgColor = 5;
    int32 imageType = 6;
    string openWebUrl = 7;
    Content content = 8;
    bool isAnimated = 9;
    NinePatchSetting flexSettingList = 10;
    NinePatchSetting textSettingList = 11;

    message Content {
        string name = 1;
        string fontColor = 2;
        int64 level = 3;
        string alternativeText = 4;
    }
    message NinePatchSetting{
        repeated int64 settingList = 1;
    }
}
message GiftIMPriority{
    repeated int64 queueSizes = 1;
    int64 selfQueuePriority = 2;
    int64 priority = 3;
}
message GiftTrayInfo{
    Text trayDisplayText = 1;
    Image trayBaseImg = 2;
    Image trayHeadImg = 3;
    Image trayRightImg = 4;
    int64 trayLevel = 5;
    Image trayDynamicImg = 6;
    string traySchemaUrl = 7;
    int32 trayType = 8;
    Image trayBaseImgV2 = 9;
    Image trayRightImgV2 = 10;
    bool useHighLayer = 11;
    int64 duration = 12;
    string toast = 13;
    int32 traySlideRate = 14;
    TrayPreEffect trayPreEffect = 15;
    TrayPostEffect trayPostEffect = 16;
    int64 originGiftId = 17;
    int64 buffLevel = 18;
    string toolbarCardName = 19;
    Image trayBaseWebpImg = 20;
    int32 trayGroupMode = 21;
}
message TrayPreEffect {
    Image preEffectImg = 1;
    int64 trayStartTime = 2;
    Image trayRipple = 3;
    string preSchema = 4;
    int64 preDuration = 5;
    int32 preEffectSource = 6;
    string extra = 7;
}
message TrayPostEffect {
    Image postEffectImg = 1;
    string postSchema = 2;
    int64 postDuration = 3;
    int32 postEffectSource = 4;
    Text subTitle = 5;
    int32 showType = 6;
    map<string, string> eventTracking = 10;
}
message GiftStruct {
    Image image = 1;
    string describe = 2;
    bool notify = 3;
    int64 duration = 4;
    int64 id = 5;
    GiftStructFansClubInfo fansclubInfo = 6;
    bool forLinkmic = 7;
    bool doodle = 8;
    bool forFansclub = 9;
    bool combo = 10;
    int32 type = 11;
    int32 diamondCount = 12;
    int32 isDisplayedOnPanel = 13;
    int64 primaryEffectId = 14;
    Image giftLabelIcon = 15;
    string name = 16;
    string region = 17;
    string manual = 18;
    bool forCustom = 19;
    map<string, int64> specialEffects = 20;
    Image icon = 21;
    int32 actionType = 22;
    int32 watermelonSeeds = 23;
    string goldEffect = 24;
    repeated LuckyMoneyGiftMeta subs = 25;
    int64 goldenBeans = 26;
    int64 honorLevel = 27;
    int32 itemType = 28;
    string schemeUrl = 29;
    GiftPanelOperation giftOperation = 30;
    string eventName = 31;
    int64 nobleLevel = 32;
    string guideUrl = 33;
    bool punishMedicine = 34;
    bool forPortal = 35;
    string businessText = 36;
    bool cnyGift = 37;
    int64 appId = 38;
    int64 vipLevel = 39;
    bool isGray = 40;
    string graySchemeUrl = 41;
    int64 giftScene = 42;
    GiftBanner giftBanner = 43;
    repeated string triggerWords = 44;
    repeated GiftBuffInfo giftBuffInfos = 45;
    bool forFirstRecharge = 46;
    Image dynamicImgForSelected = 47;
    int32 afterSendAction = 48;
    int64 giftOfflineTime = 49;
    string topBarText = 50;
    Image topRightAvatar = 51;
    string bannerSchemeUrl = 52;
    bool isLocked = 53;
    int64 reqExtraType = 54;
    repeated int64 assetIds = 55;
    GiftPreviewInfo giftPreviewInfo = 56;
    GiftTip giftTip = 57;
    int32 needSweepLightCount = 58;
    repeated GiftGroupInfo groupInfo = 59;
    Text bottomText = 60;
    int32 mysteryShopStatus = 61;
    repeated int64 optionalAssetIds = 62;
    bool disableWishList = 63;
    GiftMsgBoard giftMsgBoard = 64;
    EmojiInteractResource emojiInteractResource = 65;
    bool trayDynamicImgFlippable = 66;
    int64 picoShowAction = 67;
    int64 selectedDynamicEffect = 68;
    GiftTouchLabel giftTouchLabel = 69;
    GiftUnselectedBottomInfo unselectedBottomInfo = 70;
    GiftConfirmInfo giftConfirmInfo = 71;
    int32 bizType = 72;
    GoodsBizItem bizItem = 73;
    Image webpImage = 74;
    int32 giftSource = 75;
    repeated int64 requiredAssets = 76;
    Image selectedLabel = 77;
    int64 sortScore = 78;
    int64 topicId = 79;
    string sortExtra = 80;

    message GiftStructFansClubInfo {
        int32 minLevel = 1;
        int32 insertPos = 2;
    }
}
message AssetEffectMixInfo{
    repeated EffectMixImageInfo effectMixImageInfos = 1;
    repeated EffectMixTextInfo effectMixTextInfos = 2;
}
message LuckyMoneyGiftMeta {
    Image image = 1;
    string describe = 2;
    int64 id = 3;
    int32 diamondCount = 4;
    Image icon = 5;
}
message GiftPanelOperation {
    Image leftImage = 1;
    Image rightImage = 2;
    string title = 3;
    string titleColor = 4;
    int64 titleSize = 5;
    string schemeUrl = 6;
    string eventName = 7;
}
message GiftBanner {
    Text displayText = 1;
    string displayTextBgColor = 2;
    Image boxImg = 3;
    Image bgImg = 4;
    string schemeUrl = 5;
    bool animate = 6;
    int64 boxId = 7;
    int64 availableBoxCount = 8;
}
message GiftBuffInfo{
    string text = 1;
    string textColor = 2;
    Image bgImg = 3;
    Image sweepLightImg = 4;
    Image buffGiftDescribeImg = 5;
    int64 buffGiftId = 6;
    int32 buffLevel = 7;
    bool buffCanSend = 8;
    int64 buffDiamondCount = 9;
    string lockToast = 10;
    int64 defaultChoseAction = 11;
    int64 startTime = 12;
    BuffLockInfo buffLockInfo = 13;
    Image bgImgV2 = 14;
    map<int32, int64> tabChoseAction = 15;
}
message GiftPreviewInfo{
    int64 lockStatus = 1;
    bool clientBlockUseSchemeUrl = 2;
    string blockSchemeUrl = 3;
    bool clientCheckLeftDiamond = 4;
    string blockToast = 5;
}
message GiftTip {
    Text displayText = 1;
    string backgroundColor = 2;
    Image prefixImage = 3;
    int64 remainingDuration = 4;
    Text remainingDurationSuffixText = 5;
    int64 countdownDeadlineTime = 6;
}
message GiftGroupInfo {
    int32 groupCount = 1;
    string groupText = 2;
}

message EffectMixImageInfo {
    string imageKey = 1;
    Image mixImage = 2;
}
message EffectMixTextInfo{
    string textKey = 1;
    string content = 2;
    int64 fontSize = 3;
    string fontColor = 4;
}
message BuffLockInfo{
    bool locked = 1;
    string toast = 2;
    string schema = 3;
    string cellText = 4;
}
message GiftMsgBoard {
    bool forMsgBoard = 1;
    string promptText = 2;
}
message EmojiInteractResource {
    SendInteractEmojiConfig fromImage = 1;
    SendInteractEmojiConfig passImage = 2;
    SendInteractEmojiConfig toImage = 3;
}
message SendInteractEmojiConfig{
    Image interactEmoji = 1;
    int64 durationMs = 2;
    int64 start = 3;
    Image ownEmoji = 4;
    int64 ownEmojiDurationMs = 5;
    int64 offset = 6;
    int64 scaleUp = 7;
    bool reshape = 8;
    string soundUrl = 9;
    int64 reshapeStart = 10;
}
message GiftTouchLabel {
    Image icon = 1;
    string uniqueKey = 2;
}
message GiftUnselectedBottomInfo {
    string text = 1;
}
message GiftConfirmInfo {
    string title = 1;
    string text = 2;
    string cancelButtonText = 3;
    string confirmButtonText = 4;
    int32 confirmType = 5;
}
message GoodsBizItem {
    BizGoods goods = 1;
    BizEntrance entrance = 2;
}
message BizGoods{
    GoodsStruct goods = 1;
    string extra = 2;
    int64 consumeScene =3;
}
message BizEntrance{
    string schemeUrl = 1;
}
message GoodsStruct {
    int64 goodsId = 1;
    int64 goodsType = 2;
    string name = 3;
    int64 diamondCount = 4;
    Image icon = 5;
    string description = 6;
    int64 offlineTime = 7;
    string extra = 8;
    repeated int64 options = 50;
}
message Room {
    int64 id = 1;
    string idStr = 2;
    int64 status = 3;
    int64 ownerUserId = 4;
    string title = 5;
    int64 userCount = 6;
    int64 createTime = 7;
    int64 linkmicLayout = 8;
    int64 finishTime = 9;
    RoomExtra extra = 10;
    string dynamicCoverUri = 11;
    map<int64, string> dynamicCoverDict = 12;
    int64 lastPingTime = 13;
    int64 liveId = 14;
    int64 streamProvider = 15;
    int64 osType = 16;
    int64 clientVersion = 17;
    bool withLinkmic = 18;
    bool enableRoomPerspective = 19;
    Image cover = 20;
    Image dynamicCover = 21;
    Image dynamicCoverLow = 22;
    string shareUrl = 23;
    string anchorShareText = 24;
    string userShareText = 25;
    int64 streamId = 26;
    string streamIdStr = 27;
    StreamUrl streamUrl = 28;
    int64 mosaicStatus = 29;
    string mosaicTip = 30;
    int64 cellStyle = 31;
    LinkMic linkMic = 32;
    int64 luckymoneyNum = 33;
    repeated Decoration decoList = 34;
    repeated TopFan topFans = 35;
    RoomStats stats = 36;
    string sunDailyIconContent = 37;
    string distance = 38;
    string distanceCity = 39;
    string location = 40;
    string realDistance = 41;
    Image feedRoomLabel = 42;
    string commonLabelList = 43;
    RoomUserAttr livingRoomAttrs = 44;
    repeated int64 adminUserIds = 45;
    User owner = 46;
    string privateInfo = 47;
    bool hasCommerceGoods = 48;
    bool liveTypeNormal = 49;
    bool liveTypeLinkmic = 50;
    bool liveTypeAudio = 51;
    bool liveTypeThirdParty = 52;
    bool liveTypeScreenshot = 53;
    bool liveTypeSandbox = 54;
    bool liveTypeOfficial = 55;
    int64 groupId = 59;
    int64 orientation = 60;
    int64 category = 61;
    repeated int64 tags = 62;
    int64 startTime = 63;
    int64 popularity = 64;
    string popularityStr = 65;
    int64 fcdnAppid = 66;
    bool sellGoods = 68;
    int64 webCount = 69;
    string verticalCoverUri = 70;
    int64 baseCategory = 71;
    RoomAuthStatus roomAuth = 72;
    repeated RoomTab roomTabs = 73;
    string introduction = 74;
    BurstInfo burst = 75;
    RoomHealthScoreInfo healthScore = 76;
    bool isReplay = 77;
    string vid = 78;
    int64 groupSource = 79;
    int64 lotteryFinishTime = 80;
    RoomActivityTag activityTag = 81;
    Image portraitCover = 82;
    Image background = 83;
    int64 layout = 84;
    string waitCopy = 85;
    Image guideButton = 86;
    string previewCopy = 87;
    bool isShowInquiryBall = 88;
    MatchInfo matchInfo = 89;
    bool useFilter = 90;
    int64 giftMsgStyle = 91;
    string distanceKm = 92;
    string finishUrl = 93;
    OfficialRoomInfo officialRoomInfo = 94;
    bool isShowUserCardSwitch = 95;
    string videoFeedTag = 96;
    string forumExtraData = 97;
    int64 fansclubMsgStyle = 98;
    int64 followMsgStyle = 99;
    int64 shareMsgStyle = 100;
    int64 roomLayout = 101;
    string shortTitle = 102;
    RoomShortTouchAreaConfig shortTouchAreaConfig = 103;
    int64 bookTime = 104;
    int64 bookEndTime = 105;
    int64 roomAuditStatus = 106;
    repeated int64 liveDistribution = 107;
    TVStation tv = 108;
    bool replay = 109;
    string challengeInfo = 110;
    int64 likeCount = 111;
    int64 searchId = 112;
    string anchorScheduledTimeText = 113;
    string hotSentenceInfo = 114;
    int64 replayLocation = 115;
    int64 streamCloseTime = 116;
    string contentTag = 117;
    Image contentLabel = 118;
    Image operationLabel = 119;
    int32 anchorTabType = 120;
    GameExtra gameExtra = 121;
    OfficialChannelInfo officialChannel = 122;
    string stamps = 123;
    CommentBox commentBox = 124;
    int32 businessLive = 125;
    bool withKtv = 126;
    bool withDrawSomething = 127;
    int64 webcastCommentTcs = 128;
    int64 roomTag = 129;
    map<string, int64> linkerMap = 130;
    int32 finishReason = 131;
    RoomCart roomCart = 132;
    string scrollConfig = 133;
    string relationTag = 134;
    int64 ownerDeviceId = 135;
    int64 autoCover = 136;
    int64 appId = 137;
    int64 webcastSdkVersion = 138;
    int64 commentNameMode = 139;
    string roomCreateAbParam = 140;
    int64 preEnterTime = 141;
    int64 ranklistAudienceType = 142;
    int64 previewFlowTag = 143;
    Image previewTagUrl = 144;
    QuizExtra quizExtra = 145;
    map<string, string> AnchorABMap = 146;
    LinkerUsers linkerUsers = 147;
    int64 linkmicDisplayType = 148;
    AudioBGData AudioRoomBGImage = 149;
    LinkerUsers cityTopLinkerUsers = 150;
    BorderInfo borderInfo = 151;
    Image cityTopBackground = 152;
    string cityTopDistance = 153;
    int64 liveRoomMode = 154;
    Bonus bonus = 155;
    bool highlight = 156;
    bool isOfficialChannelRoom = 157;
    ActivityRoomSkinInfo activityRoomSkinInfo = 158;
    repeated int64 fansGroupAdminUserIds = 159;
    RoomReplayInfo replayInfo = 160;
    int64 officialChannelUid = 161;
    string livePlatformSource = 162;
    int64 acquaintanceStatus = 163;
    CommentWallInfo commentWallInfo = 164;
    CommentWallPosition commentWallPosition = 165;
    bool liveTypeVsLive = 166;
    bool liveTypeVsPremiere = 167;
    EpisodeExtraInfo episodeExtra = 168;
    repeated int32 vsRoles = 169;
    string itemExplicitInfo = 170;
    ShortTouchAuth shortTouchAuth = 171;
    int64 sofaLayout = 172;
    AnnouncementInfo announcementInfo = 173;
    bool isNeedCheckList = 174;
    LiveStatusInfo liveStatusInfo = 175;
    RoomIMInfo imInfo = 176;
    LabelInfo assistLabel = 177;
    InteractOpenExtra interactOpenExtra = 178;
    VerticalTypeInfo verticalTypeInfo = 179;
    repeated FilterWord filterWords = 180;
    LabelInfo dynamicLabel = 181;
    LinkerDetail linkerDetail = 182;
    int32 visibilityRange = 183;
    CornerMarkReach cornerMarkReach = 184;
    PreviewExposeData previewExpose = 185;
    WelfareProjectInfo welfareProjectInfo = 186;
    int32 gameRoomType = 187;
    PaidLiveData paidLiveData = 188;
    EasterEggData easterEggData = 189;
    bool titleRecommend = 190;
    int64 danmakuDetail = 191;
    AvatarLiveInfo avatarLiveInfo = 192;
    CircleInfo circleInfo = 193;
    int64 hasPromotionGames = 194;
    Image screenshotSover = 195;
    Appearance appearance = 196;
    EcomData ecomData = 197;
    IndustryServiceInfo industryServiceInfo = 198;
    RelevantRecommendation relevantRecommendation = 199;
    RoomSpecificSceneTypeInfo sceneTypeInfo = 200;
    GameCPData gameCp = 201;
    GamePlayData gamePlay = 202;
    UnionLiveInfo unionLiveInfo = 203;
    BeautifyInfo beautifyInfo = 204;
    ToolBarData toolbarData = 205;
    AnchorTabLabel anchorTabLabel = 206;
    LifeGrouponInfo lifeGrouponInfo = 207;
    VipData vipData = 208;
    int64 toutiaoCoverRecommendLevel = 209;
    int64 toutiaoTitleRecommendLevel = 210;
    repeated UpperRightWidgetData upperRightWidgetDataList = 211;
    FeedbackCard liveFeedbackCard = 212;
    repeated LabelInfo assistLabelList = 213;
    DesireInfo desireInfo = 214;
    HotRoomInfo hotRoomInfo = 215;
    bool withAggregateColumn = 216;
    CastScreenData castScreenData = 217;
    OfficialChannelExtraInfo officialChannelExtra = 218;
    string authCity = 219;
    ActivityLiveRecommendConfig activityLiveRecommendConfig = 220;
    RoomChannelData roomChannel = 221;
    PackMetaInfo packMeta = 222;
    ActivityData activityData = 223;
    LikeDisplayConfig likeDisplayConfig = 224;
    RoomViewStats roomViewStats = 225;
    MatchRoomData match = 226;
    CommentaryRoomInfo commentaryRoomInfo = 227;
    int64 redpacketAudienceAuth = 228;
    MatchChatConfig matchChatConfig = 229;
    int64 vsMainReplayId = 230;
    string screenCaptureSharingTitle = 231;
    ShareResource shareResource = 232;
    repeated string sharingMusicIdList = 233;
    PublicScreenBottomInfo publicScreenBottomInfo = 234;
    StreamUrl pushStreamHkt = 235;
    RoomBasisData basis = 300;
    RoomInteractData interact = 301;
    RoomRevenueData revenue = 302;
    RoomReqUserData reqUser = 303;
    RoomAnchorData anchorData = 304;
    RoomOthersData others = 305;
    PicoInfo picoInfo = 306;
    RoomGameData gameData = 307;
    RoomFeedData feedData = 308;
    OpenContentData openContentData = 309;
    ClientComponent clientComponentData = 310;
    RoomPlatformComponentsData platformComponentsData = 311;
    IOSClientComponent iosClientComponentData = 312;
    string ownerOpenId = 5000;
    repeated string adminUserOpenIds = 5001;
    repeated string fansGroupAdminUserOpenIds = 5002;
    string officialChannelOpenId = 5003;
    Image coverGauss = 5004;
}
message RoomAuthStatus{
    bool Chat = 1;
    bool Danmaku = 2;
    bool Gift = 3;
    bool LuckMoney = 4;
    bool Digg = 5;
    bool RoomContributor = 7;
    bool Props = 8;
    bool UserCard = 9;
    bool POI = 10;
    int64 MoreAnchor = 11;
    int64 Banner = 12;
    int64 Share = 13;
    int64 UserCorner = 14;
    int64 Landscape = 15;
    int64 LandscapeChat = 16;
    int64 PublicScreen = 17;
    int64 GiftAnchorMt = 18;
    int64 RecordScreen = 19;
    int64 DonationSticker = 20;
    int64 HourRank = 21;
    int64 CommerceCard = 22;
    int64 AudioChat = 23;
    int64 DanmakuDefault = 24;
    int64 KtvOrderSong = 25;
    int64 SelectionAlbum = 26;
    int64 Like = 27;
    int64 MultiplierPlayback = 28;
    int64 DownloadVideo = 29;
    int64 Collect = 30;
    int64 TimedShutdown = 31;
    int64 Seek = 32;
    int64 Denounce = 33;
    int64 Dislike = 34;
    int64 OnlyTa = 35;
    int64 CastScreen = 36;
    int64 CommentWall = 37;
    int64 BulletStyle = 38;
    int64 ShowGamePlugin = 39;
    int64 VSGift = 40;
    int64 VSTopic = 41;
    int64 VSRank = 42;
    int64 AdminCommentWall = 43;
    int64 CommerceComponent = 44;
    int64 DouPlus = 45;
    int64 GamePointsPlaying = 46;
    int64 Poster = 47;
    int64 Highlights = 48;
    int64 TypingCommentState = 49;
    int64 StrokeUpDownGuide = 50;
    int64 UpRightStatsFloatingLayer = 51;
    int64 CastScreenExplicit = 52;
    int64 Selection = 53;
    int64 IndustryService = 54;
    int64 VerticalRank = 55;
    int64 EnterEffects = 56;
    int64 FansClub = 57;
    int64 EmojiOutside = 58;
    int64 CanSellTicket = 59;
    int64 DouPlusPopularityGem = 60;
    int64 MissionCenter = 61;
    int64 ExpandScreen = 62;
    int64 FansGroup = 63;
    int64 Topic = 64;
    int64 AnchorMission = 65;
    int64 Teleprompter = 66;
    int64 ChatDynamicSlideSpeed = 67;
    int64 SmallWindowDisplay = 68;
    int64 MessageDispatch = 69;
    int64 RoomChannel = 70;
    int64 ChatDispatch = 71;
    int64 LinkmicGuestLike = 72;
    int64 MediaLinkmic = 73;
    int64 VideoShare = 74;
    int64 ChatGuideEmoji = 75;
    int64 ChatGuideImage = 76;
    int64 PCPlay = 77;
    int64 PadPlay = 78;
    int64 LongTouch = 79;
    int64 FirstFeedHistChat = 80;
    int64 MoreHistChat = 81;
    int64 WordAssociation = 82;
    int64 LandscapeScreenCapture = 83;
    int64 LandscapeScreenRecording = 84;
    int64 ScreenProjectionBarrage = 85;
    int64 SmallWindowPlayer = 86;
    int64 ChatOperate = 87;
    int64 EcomFansClub = 88;
    int64 AudioChatTotext = 89;
    int64 CommonCard = 90;
    int64 ShortTouch = 91;
    int64 HostTeamChannel = 92;
    int64 LandscapeChatDynamicSlideSpeed = 93;
    int64 HostTeam = 94;
    int64 AnchorHotMessageAggregated = 95;
    int64 AnchorColdMessageTiled = 96;
    int64 ScreenBottomInfo = 97;
    int64 PreviewHotCommentSwitch = 98;
    int64 RoomWidget = 99;
    RoomAuthOffReasons OffReason = 100;
    RoomAuthSpecialStyle SpecialStyle = 101;
    int64 PanelECService = 102;
    int64 FixedChat = 103;
    int64 LandscapeGift = 104;
    int64 HotChatTray = 105;
    int64 ItemShare = 106;
    int64 ShortTouchTempState = 107;
    int64 StickyMessage = 108;
    int64 ProjectionBtn = 109;
    int64 ChatDynamicSlideSpeedAnchor = 110;
    int64 PosterCache = 111;
    int64 MediaHistoryMessage = 112;
    int64 ToolbarBubble = 113;
    int64 ImHeatValue = 114;
    int64 InteractiveComponent = 115;
    int64 ChatReply = 116;
    int64 ChatMention = 117;
    int64 FrequentlyChat = 118;
    int64 StreamAdaptation = 119;
    int64 VideoAmplificationType = 120;
    int64 FeaturedPublicScreen = 121;
    int64 LandscapeScreenShare = 122;
    int64 VerticalScreenShare = 123;
    int64 AnchorAudioChat = 124;
    int64 PreviewChatExpose = 125;
    int64 FusionEmoji = 126;
    int64 MyLiveEntrance = 127;
    int64 ChatIdentity = 128;
    int64 MarkUser = 129;
    int64 LongPressOption = 130;
    int64 ClearEntranceOption = 131;
    int64 PlayerRankList = 132;
    int64 AIClone = 133;
    int64 GiftVote = 134;
    int64 TextGift = 135;
    int64 FansClubLetter = 136;
    int64 FansClubBlessing = 137;
    int64 FansClubNotice = 138;
    int64 FansClubDeclaration = 139;
    int64 MessageGift = 140;
    int64 EnhancedTouch = 141;
    int64 ChatMentionV2 = 142;
    int64 RoomChatOperatePanel = 143;
    int64 RoomChatLikeDisplay = 144;
    int64 VsCommentBar = 200;
    int64 VsWelcomeDanmaku = 201;
    int64 VsFansClub = 202;
    int64 VsExtensionEnableFollow = 203;
    int64 VsDouPlus = 204;
    int64 QuizGamePointsPlaying = 205;
    int64 UgcVSReplayDelete = 206;
    int64 UgcVsReplayVisibility = 207;
    int64 InteractionGift = 208;
    int64 SubscribeCardPackage = 209;
    int64 ShowQualification = 210;
    int64 UseHostInfo = 301;
    int64 CountType = 500;
    message RoomAuthOffReasons{
        string gift = 1;
    }
    message RoomAuthSpecialStyle{
        Style Chat = 1;
        Style Gift = 2;
        Style RoomContributor = 3;
        Style Like = 4;
        Style RoomChannel = 5;
        Style Share = 6;
        Style CastScreenAuth = 7;
        Style Landscape = 8;
        message Style {
            int32 UnableStyle = 1;
            string Content = 2;
            int32 OffType = 3;
            int32 AnchorSwitch = 4;
            string SwitchStatusTipMsg = 5;
            string SwitchStatusAnchorTipMsg = 6;
            int32 AnchorSwitchForPaidLive = 7;
            string ContentForPaidLive = 8;
        }
    }
}
message RoomTab{
    int32 tabType = 1;
    string tabName = 2;
    string tabUrl = 3;
}
message BurstInfo{
    int64 burstTimeRemainSeconds = 1;
    int64 multiple = 2;
    int64 propertyDefinitionId = 3;
    Image propertyIcon = 4;
}
message RoomHealthScoreInfo{
    double score = 1;
    string bubbleMessage = 2;
    string jumpUrl = 3;
}
message RoomActivityTag{
    int32 activityType = 1;
    string name = 2;
    string url = 3;
    string extra = 4;
    Image icon = 5;
}
message MatchInfo{
    MatchSkinInfo skin =1;
}
message MatchSkinInfo{
    FollowBtnSkin unfollowSkin = 1;
    FollowBtnSkin followedSkin = 2;
    string tabSelectedColor = 3;
    string tabUnselectedColor = 4;
    string commentPlaceHolderColor = 5;
    string anchorNameColor = 6;
    string anchorInfoColor = 7;
    Image userBannerImage = 8;
    Image toolBarImage = 9;
    string commentPlaceHolderBgColor = 10;
    Image shareIcon = 11;
    message FollowBtnSkin{
        string leftColor = 1;
        string rightColor = 2;
        string fontColor = 3;
    }
}
message OfficialRoomInfo{
    bool isShowMoreAnchor = 1;
    bool isUseServerSubtitle = 2;
    string serverSubtitle = 3;
}
message RoomShortTouchAreaConfig{
    map<int32, Element> elements = 1;
    map<int32, ForbiddenType> forbiddenTypesMap = 3;
    map<int32, TempStateCondition> tempStateConditionMap = 4;
    map<int32, TempStateStrategy> tempStateStrategy = 5;
    repeated string strategyFeatWhitelist = 6;
    TempStateGlobalCondition tempStateGlobalCondition = 7;
    message Element {
        int32 type = 1;
        int32 priority = 2;
    }
    message ForbiddenType {
        int32 type = 1;
        string reason =2;
    }
    message TempStateCondition {
        TempStateType type = 1;
        int32 minimumGap = 2;
    }
    message TempStateType{
        int32 strategyType = 1;
        string priority =2;
    }
    message TempStateStrategy {
        int32 shortTouchType = 1;
        map<int32, TempStateStrategyInfo> strategyMap = 2;
        message TempStateStrategyInfo{
            TempStateType type = 1;
            int32 duration = 2;
            string strategyMethod = 3;
        }
    }
}
message TempStateGlobalCondition {
    int32 durationGap = 1;
    int32 allowCount = 2;
    repeated int32 ignoreStrategyTypes = 3;
}
message TVStation{
    bool isIdle = 1;
    string stateDesc = 2;
}
message Bonus{
    int64 bonusType = 1;
    BonusAuthor author = 2;
    string title = 3;
    string subTitle = 4;
    string text = 5;
    string style = 6;
    BonusCommerce commerceInfo = 7;
    string openUrl = 8;
    string token = 9;
    int64 source = 10;
    Image label = 11;
    int64 sceneId = 12;
    bool maybeCarp = 13;
    int64 countdownSecond = 14;
}
message BonusAuthor {
    int64 id = 1;
    string name = 2;
    Image avatarLarger = 3;
    Image avatarThumb = 4;
    Image avatarMedium = 5;
}
message BonusCommerce {
    int64 id = 1;
}
message GameExtra {
    int32 kind = 1;
    int32 status = 2;
    int64 gameId = 3;
    int32 giftLimit = 4;
    int64 roundId = 5;
    int32 gameKind = 6;
    int64 chat = 7;
    int64 loader = 8;
    int64 reload = 9;
    map<string, string> data = 10;
}
message OfficialChannelInfo {
    User channelUser = 1;
    string channelName = 2;
    string channelIntro = 3;
    int64 endTimestamp = 4;
    int64 forbiddenBeforeEnd = 5;
    int32 currentShowId = 6;
    int64 maxEnterTime = 7;
    int64 maxNextTime = 8;
    map<int64, int64> delayEnterTime = 9;
    bool hostPermission = 10;
    int64 backupRoomId = 11;
    User livingUser = 12;
    bool hostCanAcceptGift = 13;
    repeated int64 hostUids = 14;
    string backupRoomIdStr = 15;
    bool enableHost = 16;
    repeated string hostOpenIds = 5000;
}
message CommentBox {
    Image icon = 1;
    string placeholder = 2;
}
message RoomCart {
    bool containCart = 1;
    int64 total = 2;
    int64 flashTotal = 3;
    string cartIcon = 4;
    int32 showCart = 5;
    CartVertical vertical = 6;
    CartHorizontal horizontal = 7;
    GlobalCustomIcons globalCustomIcons = 8;
}
message CartVertical {
    bool allowShowCart = 1;
}
message CartHorizontal {
    bool allowShowCart = 1;
}
message GlobalCustomIcons {
    string staticIconUrl = 1;
    string animatedIconUrl = 2;
    string animatedCommonIconUrl = 3;
}
message QuizExtra {
    string quizInfos =1;
}
message LinkerUsers {
    int64 count = 1;
    repeated User usersInfo = 2;
}
message AudioBGData {int64 id = 1;
    string imageUri = 2;
    int32 imgType = 3;
    Image image = 4;
    Image imageThumbnail = 5;
    Image imageAnimatedBG = 6;
    Image imageNormalAvatar = 7;
    Image imageStartupAvatar = 8;
    Image imageChatJoinIcon = 9;
    Image imageStaticBG = 10;
    AudioSpeakingImageList speakingImageList = 11;
    string statusColorValue = 12;
    string publicScreenColorValue = 13;
    int32 timeLimit = 14;
    int32 bgType = 15;
    string emptyStartColor = 16;
    string emptyEndColor = 17;
    Image imageColdBG = 18;
    string micBGColorValue = 19;
    repeated Image iconMicNormals = 20;
    Image iconMicLock = 21;
    string colorMicText = 22;
    string colorMicLine = 23;
    string colorMicBG = 24;
    string themeLabel = 25;
    int32 bgStatus = 26;
    AnimatedBgInfo animateInfo = 27;
    string idStr = 28;
    int32 playMode = 29;
    string themeTag = 30;
    int64 startTime = 31;
    int64 endTime = 32;
    int32 formatType = 33;
    AudioSpeakingImageList speakingImageV2 = 34;
}
message AudioSpeakingImageList {
    message SpeakingImage {
        Image speakingLowImage = 1;
        Image speakingDefaultImage = 2;
        Image speakingHighImage = 3;
    }
    SpeakingImage maleSpeakingImage = 1;
    SpeakingImage femaleSpeakingImage = 2;
    SpeakingImage unknownSpeakingImage = 3;
    SpeakingImage audioStageSpeakingImage = 4;
}

message AnimatedBgInfo {
    ShowArea showArea = 1;
    string fileFormat = 2;
    Image animatedBg = 3;
}
message ShowArea{
    int64 x = 1;
    int64 y = 2;
    int64 h = 3;
    int64 w = 4;
}
message BorderInfo {
    int64 borderType = 1;
    StaticBorderInfo staticBorder = 2;
    DynamicBorderInfo dynamicBorder = 3;
    int64 duration = 4;
}
message StaticBorderInfo{
    Image top = 1;
    Image bottom = 2;
    Image left = 3;
    Image right = 4;
}
message DynamicBorderInfo{
    int64 id = 1;
}
message ActivityRoomSkinInfo {
    map<int32, Image> verticalScreen = 1;
    map<int32, Image> horizontalScreen = 2;
}
message RoomReplayInfo {
    int64 replayDuration = 1;
    string replayUrl = 2;
    string contentUniqId = 3;
}
message CommentWallInfo {
    string content = 1;
    int64 id = 2;
    int64 commentMsgId = 3;
    int64 commenterId = 4;
    string commenterNickname = 5;
    int64 eventTime = 6;
    int64 msgTime = 7;
    int64 endTime = 8;
    int32 countdownStyle = 9;
    int64 operatorId = 10;
    string operatorNickname = 11;
    string operatorOpenid = 5000;
    string commenterOpenId = 5001;
}
message CommentWallPosition {
    double x = 1;
    double y = 2;
    int64 eventTime = 3;
}
message EpisodeExtraInfo {
    EpisodeMod episodeMod = 1;
    string currentPeriod = 2;
    string title = 3;
    string episodeListH5 = 4;
    WatchInfo watchInfo = 5;
    int64 episodeId = 6;
    string episodeIdStr = 7;
    string itemId = 8;
    string selectionUrl = 9;
    string relationPlaceText = 10;
    repeated string operationPlaceTextList = 11;
    repeated ToolbarItemConfig toolbarList = 12;
    bool collected = 13;
    int64 seasonId = 14;
    string finishUrl = 15;
    string releaseTime = 16;
    int64 watchPeriod = 17;
    int64 latestPeriod = 18;
    string showName = 19;
    int32 currentPeriodRaw = 20;
    int32 videoCode = 21;
    string seasonIdStr = 22;
    string watchPvRaw = 23;
    int64 nextSpecialEpisodeId = 24;
    Image cover = 25;
    Image coverVertical = 26;
    int32 style = 27;
    EpisodePremierePlay playControl = 28;
    EpisodePreviewImage previewImage = 29;
    VSGiftPannel giftPannel = 30;
    VSPannelIcon pannelIcon = 31;
    repeated ToolbarItemConfig pannelToolbar = 32;
    EpisodePreviewBottom previewBottom = 33;
    string drawSubTitle = 34;
    Image seasonCoverVertical = 35;
    repeated VSCameraInfo cameraInfos = 36;
    int64 defaultCameraId = 37;
    int64 defaultCameraIdStr = 38;
    string defaultCameraIdStrV2 = 39;
    VSCameraInfo priorityCamera = 40;
    string multiSeasonTag = 41;
    Image previewBackground = 42;
    Image background = 43;
    EpisodeMod mod = 44;
    Image itemCommentIcon = 45;
    VSWatermark vsWatermark = 46;
    VSItemComment itemComment = 47;
    string seasonTypeName = 48;
    VSBar vsBar = 49;
    WatchInfo seasonWatchInfo = 50;
    VSLinkInfo linkRoomInfo = 51;
    CommentConfig commentConfig = 52;
    string cameraInfosTableTitle = 53;
    int32 episodeStatus = 54;
    MatchRoomInfo matchRoomInfo = 55;
    MultiCameraBasicInfo multiCameraBasicInfo = 56;
    SharePosterInfo sharePosterInfo = 57;
    VSPremiereToast premiereToast = 58;
    string previewBottomText = 59;
    repeated BusinessConfigure businessConfigure = 60;
    string publicScreenColor = 61;
    string chatTrayColor = 62;
    MultiTab multiTab = 63;
    Image mainCameraCover = 64;
    string mainCameraTitle = 65;
    map<int32, PrivilegeInfo> privilegeInfo = 66;
    bool isInteractConfigExist = 67;
    MainCameraMatchInfo mainCameraMatchInfo = 68;
    int64 groupId = 69;
    VSConfigDrawer vsConfigDrawer = 70;
    TitleIcon titleIcon = 71;
    string groupIdStr = 72;
    map<string, PrivilegeInfo> privilegeInfoV2 = 73;
}
message EpisodeMod {
    int32 episodeStage = 1;
    int32 episodeType = 2;
    int32 episodeSubType = 3;
    int32 episodeRecordType = 4;
}
message WatchInfo {
    string desc = 1;
    string count = 2;
    string countWithBackup = 3;
    int32 realCount = 4;
    int64 realCountInt64 = 5;
    int32 liveCountDisplayType = 6;
    int64 displayTypeStartTime = 7;
    string formatCount = 8;
}

message ToolbarItemConfig {
    int32 toolbarType = 1;
    Image icon = 2;
    string jumpSchema = 3;
    int32 displayType = 4;
    Image dynamicIcon = 5;
    Image iconVertical = 6;
    Image dynamicBottomIcon = 7;
    Image bottomIcon = 8;
    repeated Toast toastList = 9;
    string extra = 10;
}
message Toast{
    int64 startTime = 1;
    int64 endTime = 2;
    string content = 3;
    string schema = 4;
    string toastId = 5;
}
message EpisodePremierePlay {
    repeated VsEpisodeHighLight highlights = 1;
    int32 playType = 2;
    int64 startTime = 3;
    string playText = 4;
    int64 textDuration = 5;
}
message VsEpisodeHighLight {
    int64 location = 1;
    string description = 2;
    Image image = 3;
    string pid = 4;
}
message EpisodePreviewImage {
    Image previewUriUp = 1;
    Image previewUriDown = 2;
    int32 previewType = 3;
    string previewWordUp = 4;
    string previewWordDown = 5;
    Image previewBackgroundUri = 6;
}
message VSGiftPannel {
    repeated string backgroundColors = 1;
    Image background = 2;
    Image selected = 3;
    Image topTitle = 4;
    Image backgroundBottom = 5;
    Image backgroundTop = 6;
}
message VSPannelIcon {
    Image spLandscapeIcon = 1;
    Image spVerticalIcon = 2;
    Image landscapeVerticalSwitchIcon = 3;
    Image lockScreenIcon = 4;
    Image landscapeMoreIcon = 9;
    Image verticalMoreIcon = 10;
    Image landscapeCommentIcon = 11;
    Image verticalCommentIcon = 12;
    Image verticalLandscapeSwitchIcon = 13;
    Image verticalLockScreenIcon = 14;
}

message EpisodePreviewBottom {
    string watchPeriodStr = 1;
    string latestPeriodStr =2;
}

message VSCameraInfo {
    int64 cameraId = 1;
    string cameraIdStr = 2;
    string title = 3;
    Image cover = 4;
    Image coverVertical = 5;
    StreamUrl streamInfo = 6;
    int64 startTime = 7;
    int64 endTime = 8;
    int32 style = 9;
    RoomAuthStatus cameraRoomAuth = 10;
    int32 vrType = 11;
    CameraPaidInfo cameraPaidInfo = 12;
    CameraMatchInfo cameraMatchInfo = 13;
    int64 groupId = 14;
    int32 cameraType = 15;
    Image label = 16;
    int64 pcu = 17;
    bool isSubscribed = 18;
    string groupIdStr = 19;
    EpisodePreviewImage previewImage = 20;
    bool banSwitch = 21;
}
message CameraPaidInfo{
    int32 maxDuration = 1;
    int64 duration = 2;
    PriceInfo priceInfo = 3;
    PaidLiveBaseInfo paidLiveBaseInfo = 4;
    PaidLiveBizExtra paidLiveBizExtra = 5;
    AsyncAuthData asyncAuthData = 6;
    message PriceInfo {
        string amount = 1;
        string currency = 2;
    }
    message PaidLiveBizExtra {
        string ticketPanelSixJumpUrl = 1;
        bool isReplay = 2;
        string paidConversionJumpUrl = 3;
    }
}
message PaidLiveBaseInfo {
    int32 paidLiveType = 1;
    int64 ticketSessionId = 2;
    int64 ticketPrice = 3;
    int32 viewRight = 4;
    int32 delivery = 5;
}
message AsyncAuthData {
    int64 keepAliveTime = 1;
    int64 asyncAuthenticationOperate = 2;
    int64 asyncAuthenticationAbType = 3;
    int64 keepAliveTimeStamp = 4;
    int64 buffer = 5;
}
message CameraMatchInfo{
    int64 matchId = 1;
    int64 contentId = 2;
    repeated int64 eventIds = 3;
    repeated int64 hideTabIds = 4;
    string matchIdStr = 5;
}
message VSWatermark {
    int64 position = 1;
    int64 landscapeDis = 2;
    int64 verticalDis = 3;
    repeated VSWatermarkIcon watermarkIcon = 4;
}
message VSWatermarkIcon {
    Image icon = 1;
    int64 iconWidth = 2;
    int64 iconType = 3;
    int64 landscapeDis = 4;
    int64 verticalDis = 5;
}
message VSItemComment {
    Image itemCommentIconDark = 1;
    Image itemCommentIconLight = 2;
}
message VSBar {
    VSItemBar itemComment =1;
    VSItemBar itemBar = 2;
}
message VSItemBar{
    Image iconDark = 1;
    Image iconLight = 2;
    string title = 3;
    string seperator = 4;
    string detail = 5;
    int64 watchInfoPv = 6;
    string watchInfoHeat = 7;
    int64 collectionId = 8;
    int32 tabId = 9;
    string contentType = 10;
}
message VSLinkInfo {
    string title = 1;
    string tabTitle = 2;
    repeated VSLinkNode rooms = 3;
    int32 pattern = 4;
}
message VSLinkNode{
    int64 roomId = 1;
    string roomIdStr = 2;
    string title = 3;
    int64 liveRoomMode = 4;
    User owner = 5;
}
message CommentConfig {
    repeated CommentColor commentColors = 1;
    repeated CommentRole commentRoles = 2;
    string roleSuffix = 3;
    string roleTitle = 4;
    string unlockRoleTip = 5;
    repeated CommentMedal commentMedals = 6;
    string medalTitle = 7;
}
message CommentColor {
    string name = 1;
    string color = 2;
}
message CommentRole {
    int64 roleId = 1;
    string name = 2;
    Image avatar = 3;
}
message CommentMedal {
    string name = 1;
    Image image = 2;
    map<string, string> jumpSchema = 3;
    Image keyboardImage = 4;
}
message MatchRoomInfo {
    MatchTabFrame matchTabFrame = 1;
    MatchTitle matchTitle = 2;
    ToutiaoMatchData matchData = 3;
    map<int64, int64> matchHostChannel = 4;
    string matchHashTag = 5;
    repeated Image backgroundImageUrlList = 6;
    Image shareIcon = 7;
    MatchRoomImInfo imInfo = 8;
    MatchShareBackground matchShareBackground = 9;
    string themeId = 10;
    MatchShareBackground matchLiveShareBackground = 11;
    int64 matchId = 12;
}
message MatchTabFrame {
    repeated MatchTab tabs = 1;
    repeated MatchTab replayTabs = 2;
    string color = 3;
    string backgroundColor = 4;
}
message MatchTab{
    string title = 1;
    string schema = 2;
    int64 id = 3;
}
message MatchTitle {
    string roomTitle = 1;
    TitleIcon titleIcon = 2;
    message TitleIcon{
        string url = 1;
        string sizeType = 2;
    }
}
message ToutiaoMatchData {
    int64 matchId = 1;
    string matchTitle = 2;
    Against against = 3;
    string startedTime = 4;
    string city = 5;
    string venueName = 6;
    string referee = 7;
    TeamStats teamStats = 8;
    repeated PlayerStats playerStats = 9;
    string matchGroup = 10;
    string matchRound = 11;
    string matchPhaseName = 12;
    bool canSubscribe = 13;
    int32 displayStatus = 14;
    string bjtFormat = 15;
    string localTimeFormat = 16;
    int32 liveStatus = 17;
    int32 matchStatus = 18;
    string matchIdStr = 19;
    int64 startedTimeUnix = 20;
    int32 leftScoreAddition = 21;
    int32 rightScoreAddition = 22;
    string duration = 23;
    repeated int64 eventIds = 24;
    int64 winnerId = 25;
    string winnerIdStr = 26;
    Image winnerIcon = 27;
    string eventName = 28;
    string boNum = 29;
}
// 对手信息
message Against {
    string leftName = 1;
    Image leftLogo = 2;
    string leftGoal = 3;
    repeated PlayerInfo leftPlayers = 4;
    GoalStageDetail leftGoalStageDetail = 5;

    string rightName = 6;
    Image rightLogo = 7;
    string rightGoal = 8;
    repeated PlayerInfo rightPlayers = 9;
    GoalStageDetail rightGoalStageDetail = 10;

    int64 timestamp = 11;
    int64 version = 12;
    int64 leftTeamId = 13;
    int64 rightTeamId = 14;
    int64 diffSei2absSecond = 15;
    int32 finalGoalStage = 16;
    int32 currentGoalStage = 17;
    int32 leftScoreAddition = 18;
    int32 rightScoreAddition = 19;
    int64 leftGoalInt = 20;
    int64 rightGoalInt = 21;
    BasketBallGoalStageDetail leftScoreDetail = 22;
    BasketBallGoalStageDetail rightScoreDetail = 23;
    BasketballStage basketballStage = 24;
    ESportsStage esportStage = 25;
}
message PlayerInfo{
    int64 id = 1;
    string name = 2;
    int32 shirt = 3;
    string position = 4;
    int32 lineupType = 5;
    Image portrait = 6;
    PlayByPlayItemInfo lastEvent = 7;
    double xAxis = 8;
    double yAxis = 9;
    int32 positionNum = 10;
    double playingTime = 11;
    PlayerStats playerStats = 12;
    string playerIdStr = 13;
    PlayByPlayItemInfo lastUpEvent = 14;
    repeated PlayByPlayItemInfo eventList = 15;
    int32 age = 16;
    double worth = 17;
    string clubName = 18;
    bool superstarFlag = 19;
    Image superstarImg = 20;
    string superstarName = 21;
    string superstarDesc = 22;
}
message PlayByPlayItemInfo{
    int64 timestamp = 1;
    int64 playerId = 2;
    int64 refPlayerId = 3;
    int32 incidentType = 4;
    double elapsed = 5;
    double elapsedPlus = 6;
    string playerName = 7;
    string refPlayerName = 8;
    string playerIdStr = 9;
    string refPlayerIdStr = 10;
    int64 startTimePts = 11;
    int64 endTimePts = 12;
    int64 startTimeSei = 13;
    int64 endTimeSei = 14;
}
message GoalStageDetail{
    string firstHalfGoal = 1;
    string secondHalfGoal = 2;
    string overtime = 3;
    string shots = 4;
    string total = 5;
}
message BasketBallGoalStageDetail{
    int32 firstPeriod = 1;
    int32 secondPeriod = 2;
    int32 thirdPeriod = 3;
    int32 forthPeriod = 4;
    int32 overtime = 5;
    int32 total = 6;
}
message BasketballStage{
    int32 currentStage = 1;
    int32 finalStage = 2;
}
message ESportsStage{
    int32 currentStage = 1;
    int32 finalStage = 2;
}
message TeamStats {
    int64 teamId = 1;
    double possession = 2;
    double shotOn = 3;
    double shotOff = 4;
    double freeKick = 5;
    double corner = 6;
    double counter = 7;
    double saves = 8;
    double offside = 9;
    double foulCommit = 10;
    double yellowCards = 11;
    double redCards = 12;
    double pass = 13;
    double keyPass = 14;
    double passesCompleted = 15;
    double steal = 16;
    double intercept = 17;
    double shots = 18;
    double possessionFiveMin = 19;
}
message PlayerStats {
    int64 playerId = 1;
    int64 teamId = 2;
    double minutesPlayed = 3;
    double goals = 4;
    double assists = 5;
    double shots = 6;
    double shotsOn = 7;
    double yCards = 8;
    double rCards = 9;
    double offsides = 10;
    double foulsCommitted = 11;
    double ownGoals = 12;
    double saves = 13;
    double caughtBall = 14;
    double cleanSheets = 15;
    double pass = 16;
    double keyPass = 17;
    double passesCompleted = 18;
    double steal = 19;
    double intercept = 20;
    double clearances = 21;
}
message MatchRoomImInfo{
    Image icon = 1;
    string title =2;
}
message MatchShareBackground{
    Image verticalImg =1;
    Image horizontalImg = 2;
}
message MultiCameraBasicInfo {
    Image icon = 1;
    string iconBackgroundColor = 2;
    string panelBackgroundColor = 3;
    Image iconPad = 4;
    bool banXgs = 5;
    bool supportVsCore = 6;
    string name = 7;
    int32 defaultDisplayDuration = 8;
}

message SharePosterInfo {
    PosterData data = 1;
    message PosterData{
        repeated Image poster = 1;
        repeated Image decorator = 2;
    }
}

message VSPremiereToast {
    string leftTopToast = 1;
    string playerToast = 2;
    string playerTopToast = 3;
    Image icon = 4;
}

message BusinessConfigure {
    int64 BusinessID = 1;
    int64 BusType = 2;
    int32 DelaySecond = 3;
    int64 PreciseTime = 4;
    int32 DisplaySecond = 5;
    ResourceConfigure ResConfig = 6;
    int64 DelType = 7;
}
message ResourceConfigure{
    int64 ResourceID = 1;
    BarrageConfigure BarrageConf = 2;
    ScreenBarConfigure ScreenBarConf = 3;
}

message BarrageConfigure {
    Image Logo = 1;
    string Text = 2;
    string AtomsBGColor = 3;
    string AtomsFrame = 4;
    string AtomsText = 5;
    Image Image = 6;
    string schema = 7;
    Image jumpIcon = 8;
}

message ScreenBarConfigure {
    Image Image = 1;
    int32 IsDynamicImg = 2;
    string schema = 3;
    string jumpPrompt = 4;
}
message MultiTab {
    string tabTitleColor = 1;
    repeated TabItem tabs = 2;
    int32 switchOn = 3;
    int32 disableBackground = 4;
    int32 disableProject = 5;
    message TabItem{
        int64 tabType = 1;
        string tabName = 2;
        string jumpLink = 3;
        int64 tabId = 4;
        int32 tabShowType = 5;
        repeated int64 hideCameraIds = 6;
        Bubble bubble = 7;
        int32 disablePreload = 8;
        string color = 9;
        int64 expireTime = 10;
        repeated int32 scene = 11;
    }
    message Bubble{
        int32 type = 1;
        repeated string rollTips = 2;
        Image icon = 3;
        int32 rollAfterMs = 4;
        int64 couponMateId = 5;
        string etType = 6;
        string extra = 7;
    }
}
message PrivilegeInfo {
    int64 entityId = 1;
    repeated PaidLiveItemInfo itemList = 2;
    int32 paidLiveType = 3;
    PrivilegeBaseInfo paidLiveInfo = 4;
    string extra = 5;
    PrivilegeUrl privilegeUrl = 6;
    PaidLiveUIBaseConfig uiConfig = 7;
    PaidLiveConfig entityConfig = 8;
}
message PaidLiveItemInfo {
    int64 itemId = 1;
    int32  itemType = 2;
}
message PrivilegeBaseInfo {
    int32 paidLiveType = 1;
    int32 viewRight = 2;
    int32 delivery = 3;
}
message PrivilegeUrl {
    string privilegePanelSchema = 1;
    string privilegeCardSchema = 2;
}
message PaidLiveUIBaseConfig {
    int32 paidLiveType = 1;
    Image paidLiveIcon = 2;
    string paidLiveIconTitle = 3;
    int64 duration = 4;
    string noRightEndTitle = 5;
    string noRightEndSubTitle = 6;
    string hasRightEndTitle = 7;
    string hasRightEndSubTitle = 8;
    string paidConversionJumpUrl = 9;
    string panelSixJumpUrl = 10;
    string feedPagePurchaseBtnWord = 11;
    string purchaseBtnWord = 12;
    string imShareTopTitle = 13;
    string imShareBottomTitle = 14;
    string searchCardTopTitle = 15;
    string panelFeedJumpUrl = 16;
    string panelImCardJumpUrl = 17;
    string panelInRoomJumpUrl = 18;
    string panelSearchCardJumpUrl = 19;
}
message PaidLiveConfig {
    int64 spuId = 1;
    string spuIdStr = 2;
    int64 skuId = 3;
    string skuIdStr = 4;
    string title = 5;
    string subTitle = 6;
    repeated string tags = 7;
    Image cover = 8;
    int32 sellStatus = 9;
    int32 status = 10;
    repeated PriceInfo iosPayPriceInfo = 11;
    repeated PriceInfo androidPayPriceInfo = 12;
    UserRight userRight = 13;
    message PriceInfo{
        int32 payType = 1;// 0 -> UnKnown, 1 -> Cash, 2 -> DouCoin
        int64 price = 2;
        int32 rightDuration = 3;
    }
    message UserRight{
        int32 viewRight = 1;
        int64 duration = 3;
        int32 delivery = 6;
        bool needDeliveryNotice = 7;
    }
}
message MainCameraMatchInfo {
    ToutiaoMatchData matchData = 1;
    int64 matchId = 2;
    repeated int64 eventIds = 3;
    int64 contentId = 4;
    LeagueInfo leagueInfo = 5;
    bool isDisplayScoreType = 6;
    repeated int64 hideTabIds = 7;
    string matchIdStr = 8;
    string contentIdStr = 9;
}
message LeagueInfo{
    int64 leagueId = 1;
    string cnnName = 2;
    string ennName = 3;
    string season = 4;
}
message VSConfigDrawer {
    MatchDrawer drawer = 1;
    Image drawerLabel = 2;
    Image drawerBackground = 3;
}
message MatchDrawer{
    string entryName = 1;
    Image entryIcon = 2;
    string drawerTitle = 3;
    Image drawerBackground = 4;
    string returnBtnText = 5;
    int32 displayMode = 6;
}
message TitleIcon {
    Image icon = 1;
    int32 sizeType = 2;
}
message ShortTouchAuth {
    int32 commerceLottery = 1;
}
message AnnouncementInfo {
    string scheduledTimeText = 1;
    string content = 2;
    bool subscribed = 3;
    int64 appointmentId = 4;
    int32 scheduledTime = 5;
    int32 scheduledDate = 6;
    repeated int32 scheduledWeekdays = 7;
}
message LiveStatusInfo {
    int32 liveStatus = 1;
    User liveUser = 2;
    map<int32, Image> liveNotifyLight = 3;
    string previewText = 4;
}
message RoomIMInfo {
    repeated string welcomeMessages = 1;
    string roomTag = 2;
    string hideGiftMessage = 3;
}

message LabelInfo {
    int64 labelType = 1;
    int64 displayType = 2;
    SpliceLabel spliceLabel = 3;
    Image wholeLabel = 4;
    string extra = 5;
    ProfilePicSpliceLabel profilePicSpliceLabel = 6;
    map<string, string> etExtra = 7;
}
message SpliceLabel {
    string text = 1;
    string textColor = 2;
    Image backgroundImage = 3;
    Image iconImage = 4;
    string backgroundColor = 5;
    int64 iconWidth = 6;
    int64 iconHeight = 7;
    int64 iconRaduis = 8;
    int64 textFrontSize = 9;
    int64 textFrontBold = 10;
    int64 textFrontItalic = 11;
}
message ProfilePicSpliceLabel {
    repeated LabelProfileItem profileList = 1;
}
message LabelProfileItem {
    int64 userId = 1;
    string nickName = 2;
    Image profilePic = 3;
    string openId = 5000;
}
message InteractOpenExtra {
    map<string, string> debugInfo = 1;
}
message VerticalTypeInfo {
    string name = 1;
    int64 tabType = 2;
    int64 tagId = 3;
    bool isSubTag = 4;
    Image icon = 5;
    int64 validTime = 6;
    int64 priority = 7;
    string extra = 8;
    string eventExtra = 9;
}
message FilterWord {
    string id = 1;
    bool isSelected = 2;
    string name = 3;
}
message LinkerDetail {
    repeated int64 linkerPlayModes = 1;
    int32 bigPartyLayoutConfigVersion = 2;
    bool acceptAudiencePreApply = 3;
    int64 linkerUiLayout = 4;
    int32 enableAudienceLinkmic = 5;
    string functionType = 6;
    map<string, string> linkerMapStr = 7;
    string ktvLyricMode = 8;
    string initSource = 9;
    bool forbidApplyFromOther = 10;
    int32 ktvExhibitMode = 11;
    int64 enlargeGuestTurnOnSource = 12;
    map<string, string> playmodeDetail = 13;
    string clientUiInfo = 14;
    int64 manualOpenUi = 15;
    repeated int64 featureList = 16;
}
message CornerMarkReach {
    bool needReach = 1;
    int64 duration = 2;
    int64 elemType = 3;
}
message PreviewExposeData {
    int32 style = 1;
    PreviewGuide previewGuide = 2;
    repeated Meta metas = 3;
    repeated ChatMessage chatMsgs = 4;
    repeated Meta forceInsertion = 5;
    int32 scrollAfterMs = 6;
    bool needRealtime = 7;
    int32 messageScrollAfterMs = 8;
    int32 messageScrollIntervalMs = 9;
    string previewIntro = 10;
    PreviewExtendArea previewExtendArea = 11;
    int32 showUvPv = 12;
    int32 showNameAbbreviation = 13;
    PreviewSwitch switch = 14;
    int32 isPreviewUseWebsocket = 15;
    bool isAwemeVideoFeed = 16;
    bool showPreviewCards = 17;
    PreviewPromotion previewPromotion = 18;
    PreviewIMExtend previewImExtend = 19;
    repeated EtData etData = 20;
    int32 aliveChecker = 21;
    EnterPublicAreaAnimation enterPublicAreaAnimation = 22;
    PreviewPromotionSyncData previewPromotionSyncData = 23;
    GrowthTask growthTask = 24;
    PreviewExitGuide previewExitGuide = 25;
    repeated PreviewExitGuide previewExitGuideList = 26;
    ClientComponent clientComponent = 27;
    IOSClientComponent iosClientComponent = 28;

    message PreviewGuide {
        Image icon = 1;
        string tip = 2;
        int64 type = 3;
        repeated string rollTips = 4;
        int64 couponMateId = 5;
        int64 userTagGetCoin = 6;
        int64 liveMessionStyle = 7;
        string etType = 8;
        string extra = 9;
        map<string, string> etExtra = 10;
        string enterTip = 11;
        repeated string enterRollTips = 12;
    }

    message Meta {
        Type type = 1;
        Host host = 2;
        string content = 3;
        message Host{
            Image icon = 1;
            string tip = 2;
            string color = 3;
        }
        enum Type {
            Unknown = 0;
            Location = 1;
            PlatformLabel = 2;
            UserLabel = 3;
            Announcement = 4;
            Title = 5;
            ECommerce = 6;
            Relation = 7;
            Backup = 99;
        }
    }

    message ChatMessage{
        string nickName = 1;
        string nickNameColor = 2;
        string content = 3;
    }

    message PreviewExtendArea {
        Image extendIcon = 1;
        string extendPreText = 2;
        string extendText = 3;
        ExtendType extendType = 4;
        string extra = 5;
        int32 useMarquee = 6;
        int32 iconType = 7;
        repeated Part rightPart = 8;
        string buttonPart = 9;
        repeated Part midPart = 10;
        bool needDelimiter = 11;
        repeated Part bottomPart = 12;
        int32 uiType = 13;
        int32 version = 14;
        ActionConfig actionCfg = 17;
        bool usePassThroughExtraJson = 18;
        string passThroughExtraJson = 19;
        map<string, string> extraEnterRoom = 20;
        string extraBusiness = 21;
        bool noNeedPoll = 22;
        map<string, string> etExtra = 23;
        int32 clickableArea = 24;
        enum ExtendType {
            Unknown = 0;
            Ecommerce = 1;
            UvOrPv = 2;
            Announcement = 3;
            GamePromotion = 4;
            LocalLife = 5;
            LocalLifeMultiStore = 6;
            MicroApp = 7;
            Game = 8;
            PKPaintedEgg = 9;
            Welfare = 10;
            Revenue = 11;
            VarietyShow = 12;
            VarietyShowBigIcon = 13;
            Advertising = 15;
            TaskCenter = 16;
            EcommerceVideo = 17;
            AudienceLinkMic = 18;
            KTV = 19;
            AnchorLinkmic = 20;
            LocalLifeVideo = 21;
            HighQualityEcommerceVideo = 22;
            AnchorSongStatus = 23;
            Dance = 24;
            Instrument = 25;
            ExplainCard = 101;
        }
        enum IconType {
            UnKnownType = 0;
            Icon = 1;
            ProductCover = 2;
            BigIcon = 3;
        }
        enum ActionType{
            None = 0;
            EnterWithParams = 1;
            Schema = 2;
        }
        message Part{
            int32 fontSize = 1;
            int32 interval = 2;
            string text = 3;
            bool cuttable = 4;
            bool deleted = 5;
            string fontColor = 6;
            bool bold = 7;
            int32 partType = 8;
            Image image = 9;
            string backgroundColor = 10;
            Image leftImage = 11;
        }
        message ActionConfig{
            int32 areaAction = 1;
            map<string, string> actionPrams = 2;
        }
    }

    message PreviewSwitch {
        int32 title = 1;
        int32 previewGuide = 2;
        int32 dynamicLabel = 3;
        int32 assistLabel = 4;
        int32 extendArea = 5;
    }

    message PreviewPromotion {
        repeated Image avatarIcons = 1;
        string text = 2;
        repeated string fastComments = 3;
        repeated EmojiDetail fastCommentsEmoji = 4;
    }

    message PreviewIMExtend {
        int64 imExtendType = 1;
        Image icon = 2;
        string mainTitle = 3;
        string subTitle = 4;
        string extra = 5;
    }
    message EtData {
        string typeEnterRoom = 1;
        string typeMobParams = 2;
        string typeParams = 3;
        string typeFirstRoomParams = 4;
        string typeFirstRoomMobParams = 5;
        string typeFirstShowRoomParams = 6;
        string typeFirstShowMobParams = 7;
    }
    message GrowthTask {
        string taskKey = 1;
        string mainText = 2;
        string subText = 3;
        map<string, string> extra = 4;
    }
    message PreviewPromotionSyncData {
        Image icon = 1;
        string text = 2;
        LuckyBag luckyBag = 3;
        int64 type = 4;
        LuckyMoney luckyMoney = 5;
    }
    message LuckyBag {
        int64 lotteryId = 1;
        int64 startAt = 2;
        int64 drawAt = 3;
    }
    message LuckyMoney {
        int64 boxId = 1;
        int64 startAt = 2;
        int64 delaySeconds = 3;
        int64 displayEndAt = 4;
        int64 status = 5;
        string text = 6;
    }
    message PreviewExitGuide {
        Image icon = 1;
        string mainText = 2;
        string subText = 3;
        string schemaUrl = 4;
        int32 type = 5;
        string buttonText = 6;
        int64 latestLiveRecord = 7;
        repeated User userList = 8;
        string tag = 9;
        int32 lessFreq = 10;
    }
}
message WelfareProjectInfo {
    int64 projectId = 1;
    int64 showFrequency = 2;
    string containerCardUrl = 3;
    string welfareDetailPageUrl = 4;
    string projectIdStr = 5;
}
message PaidLiveData {
    int32 paidType = 1;
    int32 viewRight = 2;
    int64 duration = 3;
    TicketData ticketSession = 4;
    OrderData orderData = 5;
    int32 delivery = 6;
    bool needDeliveryNotice = 7;
    int32 anchorRight = 8;
    int32 payAbType = 9;
    map<string, PrivilegeInfo> privilegeInfo = 10;
    map<string, PrivilegeInfo> privilegeInfoMap = 11;
    AsyncAuthData asyncAuthData = 12;
    ProductsData productsData = 13;
    int64 maxPreviewDuration = 14;
}
message TicketData{
    int64 ticketSessionId = 1;
    string title = 2;
    string subTitle = 3;
    string price = 4;
    int64 rawReleaseTime = 5;
    repeated string tags = 6;
    string ticketExplanationCardJumpUrl = 7;
    string ticketPanelJumpUrl = 8;
    string ticketSessionIdStr = 9;
    int32 sellStatus = 10;
    string coverUri = 11;
    string coverUriVertical = 12;
    string Introduction = 13;
    int64 rawShowStartTime = 14;
    int64 rawShowEndTime = 15;
    int64 rawSaleStartTime = 16;
    int64 rawSaleEndTime = 17;
    int64 rawRefundDeadline = 18;
    int64 rawEarlyBirdStartTime = 19;
    int64 rawEarlyBirdEndTime = 20;
    int64 rawFullPriceStartTime = 21;
    int64 rawFullPriceEndTime = 22;
    int64 rawActivityStartTime = 23;
    int64 rawActivityEndTime = 24;
    string earlyBirdPrice = 25;
    string activityPrice = 26;
    int32 status = 27;
    string showStartTime = 28;
    string showEndTime = 29;
    string saleStartTime = 30;
    string saleEndTime = 31;
    string earlyBirdStartTime = 32;
    string earlyBirdEndTime = 33;
    string fullPriceStartTime = 34;
    string fullPriceEndTime = 35;
    string activityStartTime = 36;
    string activityEndTime = 37;
    string refundDeadline = 38;
    string activityTitle = 39;
    string activityJumpUrl = 40;
    string releaseTime = 41;
    int32 ticketExplanationCardStatus = 42;
    int32 lastDuration = 43;
    string paidConversionJumpUrl = 44;
    string ticketPanelSixJumpUrl = 45;
    string ticketPanelNineJumpUrl = 46;
    Image cover = 47;
    int32 iosPayType = 48;
    int32 androidPayType = 49;
    int32 iosPayPrice = 50;
    int32 androidPayPrice = 51;
    int32 rightDuration = 52;
    PaidLivePriceInfo friendPriceInfo = 53;
    PaidLivePriceInfo helpingPriceInfo = 54;
    map<int64, PaidLivePriceInfoV2> priceInfo = 55;
    int64 ticketAnchorId = 56;
    string extra = 57;
    repeated TicketSkuDetail skuDetails = 58;
    PaidLivePriceInfoV2 finalPriceInfo = 59;
    int64 parentTicketSessionId = 60;
    string parentTicketSessionIdStr = 61;
    int32 ticketCategory = 62;
    string prepayCashierJumpUrl = 63;
    SubscribeMemberMark subscribeMemberMark = 64;
    string ticketAnchorOpenId = 5000;
    message TicketSkuDetail{
        int32 ticketType = 1;
        string ticketName = 2;
        int64 startSellTime = 3;
        int64 closingSellTime = 4;
        int32 sellStatus = 5;
        PaidLivePriceInfoV2 priceInfo = 6;
        PaidLivePriceInfoV2 douPriceInfo = 7;
        int64 skuId = 8;
    }
    message PaidLivePriceInfoV2{
        string amount = 1;
        string currency = 2;
        int64 pos = 3;
        repeated int32 payType = 4;
    }
}
message PaidLivePriceInfo{
    int32 startTime = 1;
    int32 endTime = 2;
    int64 price = 3;
    int32 claimDdl = 4;
    int64 skuId = 5;
}
message SubscribeMemberMark{
    bool subscribeMemberWatchFree = 1;
}
message OrderData{
    string orderId = 1;
}
message ProductsData{
    ToolBar toolBar = 1;
    ExplainCard explainCard = 2;
    ToolBar productListToolBar = 3;
    int32 bringProductSwitchStatus = 4;
    int32 paidLiveBringingProductFlag = 5;

    message ToolBar {
        string panelJumpUrl = 1;
        string sixJumpUrl = 2;
        string nineJumpUrl = 3;
    }

    message ExplainCard {
        int32 status = 1;
        string cardJumpUrl = 2;
        int32 lastDuration = 3;
        Product product = 4;
    }
}
message Product{
    ProductBasicInfo basicInfo = 1;
    ProductPriceStruct priceInfo = 2;
    LiveInfo liveInfo = 3;
    ItemInfo itemInfo = 4;
    ContentInfo contentInfo = 5;
    PaymentArea paymentArrea = 6;
    ProductPriceRichText priceRichInfo = 7;
    ProductComments productComments = 8;
    ShareTicket shareTicket = 29;
    PrivilegeTicket privilege = 30;
    Combined combined = 31;
    Comments comments = 32;
    Rating rating = 33;
    User user = 35;
    repeated Author authors = 36;
    ProductBanner banner = 37;
    repeated SKU skuList = 17;
    SubscribeMember subscribeMember = 18;
    FilterReason filterNode = 999;
}
message ProductBasicInfo {
    int64 id = 1;
    string idStr = 2;
    int64 ownerUserId = 3;
    int32 status = 4;
    int32 biz = 5;
    int32 category = 6;
    string categoryContent = 7;
    string title = 8;
    int32 source = 9;
    string coverUri = 10;
    Image cover = 11;
    int32 reviewStatus = 12;
    int32 performanceStatus = 13;
    int32 disableReason = 14;
    int64 saleStartTime = 15;
    int64 saleEndTime = 16;
    int64 refundDeadline = 17;
    string introductionText = 18;
    repeated Introduction introduction = 19;
    repeated HeaderMedia headerMedia = 20;
    repeated ProductTag tags = 21;
    string contentTitle = 22;
    string contentCategory = 23;
    string contentExtentInfo = 24;
    int32 saleStatus = 26;
    int64 servicePeriod = 27;
    bool geoBan = 28;
    int32 combinedViewRight = 29;
    string jumpSchema = 30;
    string detailSchema = 31;
    int32 newProduct = 32;
    string contentExtentInfoColor = 33;
    int64 createTime = 34;
    int64 showStartTime = 35;
    repeated ContentTip contentTips = 36;
    string prepayCashierJumpUrl = 37;
    repeated ProductSellingPoint sellingPoints = 38;
    ProductSaleInfo saleInfo = 39;
    repeated RecentPurchasedUser recentPurchasedUser = 40;
    string ownerOpenId = 5000;
}
message Introduction {
    int32 type = 1;
    string content = 2;
}
message HeaderMedia {
    int32 type = 1;
    Image imageInfo = 2;
    ViewButton viewButton = 3;
    message ViewButton{
        string content = 1;
        Image icon = 2;
        string jumpSchema = 3;
        int32 schemaType = 4;
    }
}
message ProductTag {
    Image icon = 1;
    string content = 2;
}
message ContentTip {
    string title = 1;
    string context = 2;
    string clickTitle = 3;
    string clickContext = 4;
}
message ProductSellingPoint {
    int32 type = 1;
    string content = 2;
    int64 originalValue = 3;
}
message ProductSaleInfo {
    int64 saleCnt = 1;
    int64 helpingSaleCnt = 2;
    string writtenOffAmount = 3;
}
message RecentPurchasedUser {
    string uid = 1;
    string avatarUri = 2;
    Image avatar = 3;
    int64 episodes = 4;
    string openId = 5000;
}
message ProductPriceStruct {
    string amount = 1;
    string currency = 2;
    int64 pos = 3;
    repeated int32 payType = 4;
    string fullPrice = 5;
    int64 countdownTimestamp = 6;
    string start = 7;
}
message LiveInfo {
    int64 roomId = 1;
    string title = 2;
    int32 episodeStage = 3;
    int64 roomStartTime = 4;
    int32 liveStatus = 5;
    string vid = 6;
    string replayIdStr = 7;
    bool isReplay = 8;
    int32 delivery = 9;
    int64 viewRight = 10;
    string anchorSignTagContent = 11;
    string roomIdStr = 12;
    int32 paidLiveBringProductFlag = 13;
    string livingSkuId = 14;
    int64 replayAutoOfflineTime = 15;
}

message ItemInfo {
    int64 itemId = 1;
    string title = 2;
    string tagType = 3;
    int32 lastest = 4;
    int32 total = 5;
    string playCounts = 6;
    int64 viewRight = 7;
    string itemIdStr = 8;
    int32 duration = 9;
}
message ContentInfo {
    int64 contentId = 1;
    string title = 2;
    string tagType = 3;
    int32 lastest = 4;
    int32 total = 5;
    string category = 6;
    int64 viewRight = 7;
    string contentIdStr = 8;
    int32 duration = 9;
    int64 promiseUpdateTime = 10;
}
message PaymentArea {
    PayButton payButton = 1;
    AdditionalButton additionalButton = 2;
    repeated PayButton payButtonList = 3;
    message PayButton {
        string content = 1;
        Image icon = 2;
        string jumpSchema = 3;
        int32 avilable = 4;
        string copyWriting = 5;
        int32 clickType = 6;
        bool isNewEntTrade = 7;
    }
    message AdditionalButton {
        string content = 1;
        Image icon = 2;
        string jumpSchema = 3;
        int32 avilable = 4;
        string copyWriting = 5;
    }
}
message ProductPriceRichText {
    repeated RichText richTexts = 1;
    map<string, string> extra = 2;
}
message RichText{
    int32 type = 1;
    string text = 2;
    Image img = 3;
    int64 fontSize = 4;
    string fontColor = 5;
    int64 weight = 6;
}
message ProductComments {
    string Title = 1;
    string TotalCount = 2;
    string JumpTitle = 3;
    string JumpSchema = 4;
    repeated ProductComment Comments = 5;
}
message ProductCommentUser{
    string UID = 1;
    string NickName = 2;
    Image Avatar = 3;
    string openId = 5000;
}
message ProductComment {
    string CommentID = 1;
    string Text = 2;
    string Rate = 3;
    string CreateTime = 4;
    string DiggCount = 5;
    int32 UserDigged = 6;
    string Status = 7;
    int32 Anonymous = 8;
    ProductCommentUser User = 9;
    int32 IsAuthorDigged = 10;
    string Location = 11;
}
message ShareTicket {
    bool hasShare = 1;
    User fromUser = 2;
    string packageRecordId = 3;
    bool hasGrabShare = 4;
}
message PrivilegeTicket {
    repeated string skuId = 1;
    repeated PrivilegeConfigure privilegeConf = 2;
    bool privilegeHasPaid = 3;
    message PrivilegeConfigure{
        Image icon = 1;
        string name = 2;
        int32 entityType = 3;
        int64 privilegeTabKey = 4;
        int32 privilegeType = 5;
    }
}
message Combined {
    repeated BindSubProductData bindSubTicketList = 1;
    ParentProductSimpleData parentTicketPanelData = 2;
}
message BindSubProductData {
    string productId = 1;
    string parentProductId = 2;
    string title = 3;
    int32 liveStatus = 4;
    int32 ticketStatus = 5;
    string detail = 6;
    string vid = 7;
    string roomId = 8;
    int32 deliveryStatus = 9;
    int64 liveTime = 10;
    int64 endTime = 11;
    int64 liveStartTime = 12;
    int64 liveEndTime = 13;
    int64 viewRight = 14;
    bool isReplay = 15;
    int64 replayId = 16;
    string replayIdStr = 17;
    int32 disableReason = 18;
    string cameraId = 19;
}
message ParentProductSimpleData {
    string productId = 1;
    string title = 2;
    string subTitle = 3;
    Image cover = 4;
    int32 status = 5;
    int64 viewRight = 6;
    int32 bindSubProductCount = 7;
    repeated PurchaseSimpleData data = 8;
    string subTitleTag = 9;
    int32 currentShowNum = 10;
    int32 remainSubTicketsCount = 11;
    int64 latestFinishTime = 12;
}
message PurchaseSimpleData{
    int32 type = 1;
    string name = 2;
    repeated ProductTag tags = 3;
    int64 startSellTime = 4;
    int64 closingSellTime = 5;
    int32 sellStatus = 6;
    int32 purchaseStatus = 7;
    ProductPriceStruct priceInfo = 8;
}
message Comments {
    string title = 1;
    int32 count = 2;
    bool hasMore = 3;
    string jumpSchema = 4;
    repeated Comments.TextInfo texts = 5;
    message TextInfo{
        int64 commentId = 1;
        UserInfo userInfo = 2;
        string text = 3;
        int32 diggCount = 4;
        int64 createTimeStamp = 5;
        string commentIdStr = 6;
        message UserInfo{
            int64 userId = 1;
            string name = 2;
            Image avatar = 3;
            string openId = 5000;
        }
    }
}
message Rating {
}
message Author {
    int32 type = 1;
    User userInfo = 2;
    AuthorBasicInfo authorBasicInfo = 3;
    string jumpSchema = 4;
    message AuthorBasicInfo{
        string name = 1;
        string intorduce = 2;
        Image cover = 3;
    }
}
message SKU {
    string id = 1;
    int32 category = 2;
    int64 price = 3;
    int64 saleStartTime = 4;
    int64 saleEndTime = 5;
    int32 refundable = 6;
    string coverUri = 7;
    Image cover = 8;
    string introduction = 9;
    string title = 10;
    int32 saleStatus = 11;
    string productId = 12;
    repeated RelatedSkuSimpleInfo relatedSkuSimpleInfos = 13;
    int32 orderItemType = 14;
    string orderBizExtInfo = 15;
    int32 limitCount = 16;
    int32 purchaseStatus = 17;
    bool canPurchase = 18;
    int64 episode = 19;
    string introductionText = 20;
    int32 priceType = 21;
    CaremaInfo cameraInfo = 22;
    repeated EntityIDsMap entityIdsMap = 23;
    int32 freeStatus = 24;
    LimitedFreeNotice limitedFreeNotice = 25;
    int32 liveStatus = 26;
    bool enableRenewVip = 27;
    int64 bonusAmount = 28;
    int64 rightValidDuration = 29;
    message RelatedSkuSimpleInfo {
        string skuId = 1;
        string productId = 2;
    }
    message CaremaInfo {
        int64 cameraId = 1;
        string cameraIdStr = 2;
    }
    message EntityIDsMap {
        int32 entityType = 1;
        repeated int64 releatedEntityIds = 2;
    }
    message LimitedFreeNotice {
        string iconUri = 1;
        Image icon = 2;
        string content = 3;
    }
}
message SubscribeMember {
    string paySchema = 1;
    string title = 2;
    string subTitle = 3;
    ProductPriceStruct priceInfo = 4;
    int32 subscribeStatus = 5;
    int64 validStartTime = 6;
    int64 validEndTime = 7;
}
message ProductBanner {
    Image cover = 1;
    string schema = 2;
}
message FilterReason {
    bool failed = 1;
    bool canShow = 2;
    string reason = 3;
    int32 werrCode = 4;
    string filterName = 5;
}
message EasterEggData {
    bool hasEasterEgg = 1;
    int64 stage = 2;
    int64 totalStage = 3;
    int64 effectsNum = 4;
    int64 startCount = 5;
    int64 endCount = 6;
    int64 count = 7;
    string panelUrl = 8;
    Image entranceIcon = 9;
}
message AvatarLiveInfo {
    string type = 1;
    string text = 2;
    int32 textSize = 3;
    Image image = 4;
}
message CircleInfo {
    int64 id = 1;
    string name = 2;
    Image coverImg = 3;
    string description = 4;
}
message Appearance {
    Bubble headBubble = 1;
    int64 upRightStatsDisplayType = 2;
    MoreEntrance entrance = 3;
    repeated ToolbarItemConfig toolbarList = 4;
    int64 previewStyle = 5;
    Image coverGauss = 6;
    repeated ContentTag contentTags = 7;
    PreviewLabel previewLabel = 8;
    Image coverDynamicMask = 9;
    Image horizontalBackground = 10;
    repeated WideCover wideCoverList = 11;
    Image blurPlaceholderImg = 12;
    message Bubble {
        Type type = 1;
        repeated string rollTips = 2;
        Image icon = 3;
        int32 rollAfterMs = 4;
        int64 couponMateId = 5;
        string etType = 6;
        string extra = 7;
        enum Type {
            UNKNOWN = 0;
            TREASURE_BOX = 1;
            ECOM_PENETRATION = 2;
            QC_RED_PACKET = 3;
            AD_REWARD_COIN = 4;
            ECOM_GET_COUPON = 5;
            ECOM_USE_COUPON = 6;
        }
    }
    message  ContentTag {
        string text = 1;
    }

    message WideCover {
        Image cover = 1;
        int64  coverType = 2;
    }
}
message MoreEntrance {
    string Title = 1;
    int64  Type = 2;
}
message WebRoomAuthStatus{
    bool Chat = 1;
    bool Danmaku = 2;
    bool Gift = 3;
    bool LuckMoney = 4;
    bool Digg = 5;
    bool RoomContributor = 7;
    bool Props = 8;
    bool UserCard = 9;
    bool POI = 10;
    int64 MoreAnchor = 11;
    int64 Banner = 12;
    int64 Share = 13;
    int64 UserCorner = 14;
    int64 Landscape = 15;
    int64 LandscapeChat = 16;
    int64 PublicScreen = 17;
    int64 GiftAnchorMt = 18;
    int64 RecordScreen = 19;
    int64 DonationSticker = 20;
    int64 HourRank = 21;
    int64 CommerceCard = 22;
    int64 AudioChat = 23;
    int64 DanmakuDefault = 24;
    int64 KtvOrderSong = 25;
    int64 SelectionAlbum = 26;
    int64 Like = 27;
    int64 MultiplierPlayback = 28;
    int64 DownloadVideo = 29;
    int64 Collect = 30;
    int64 TimedShutdown = 31;
    int64 Seek = 32;
    int64 Denounce = 33;
    int64 Dislike = 34;
    int64 OnlyTa = 35;
    int64 CastScreen = 36;
    int64 CommentWall = 37;
    int64 BulletStyle = 38;
    int64 ShowGamePlugin = 39;
    int64 VSGift = 40;
    int64 VSTopic = 41;
    int64 VSRank = 42;
    int64 AdminCommentWall = 43;
    int64 CommerceComponent = 44;
    int64 DouPlus = 45;
    int64 GamePointsPlaying = 46;
    int64 Poster = 47;
    int64 Highlights = 48;
    int64 TypingCommentState = 49;
    int64 StrokeUpDownGuide = 50;
    int64 UpRightStatsFloatingLayer = 51;
    int64 CastScreenExplicit = 52;
    int64 Selection = 53;
    int64 IndustryService = 54;
    int64 VerticalRank = 55;
    int64 EnterEffects = 56;
    int64 FansClub = 57;
    int64 EmojiOutside = 58;
    int64 CanSellTicket = 59;
    int64 DouPlusPopularityGem = 60;
    int64 MissionCenter = 61;
    int64 ExpandScreen = 62;
    int64 FansGroup = 63;
    int64 Topic = 64;
    int64 AnchorMission = 65;
    int64 Teleprompter = 66;
    int64 LongTouch = 79;
    int64 FirstFeedHistChat = 80;
    int64 MoreHistChat = 81;
    int64 TaskBanner = 82;
    RoomAuthOffReasons OffReason = 100;
    RoomAuthSpecialStyle SpecialStyle = 101;
    int64 FixedChat = 103;
    int64 QuizGamePointsPlaying = 104;
    message RoomAuthOffReasons {
        string gift = 1;
    }
    message RoomAuthSpecialStyle {
        Style Chat = 1;
        Style Gift = 2;
        Style RoomContributor = 3;
        Style Like = 4;
        message Style {
            int32 UnableStyle = 1;
            string Content = 2;
            int32 OffType = 3;
            int32 AnchorSwitchForPaidLive = 4;
            string ContentForPaidLive = 5;
        }
    }
}
message PreviewLabel {
    int32 type = 1;
    repeated string labelTips = 2;
    int32 labelType = 3;
    Image labelImage = 4;
    bool hitTest = 5;
}
message EcomData {
    EcomLiveCard liveCard = 1;
    EcomPop pop = 2;
    EcomGoodsCard goodsCard = 3;
    repeated RedsShowInfo redsShowInfos = 4;
    RoomCartV2 roomCartV2 = 5;
    int32 instantType = 6;
}
message EcomLiveCard {
    EcomProduct  product = 1;
    EcomIcon icon = 2;
    EcomCampaign campaign = 3;
}
message EcomProduct {
    int64 promotionId = 1;
    int64 productId = 2;
    string title = 3;
    string coverImage = 4;
    EcomPrice price = 5;
    int64 regularPrice = 6;
    int64 depositPrice = 7;
}
message EcomPrice{
    string prefix = 1;
    string suffix = 2;
    int64 byCent = 3;
    string formatPrice = 4;
}
message EcomIcon {
    string url = 1;
}
message EcomCampaign {
    int64 remainingSeconds = 1;
    EcomAuction auction = 2;
    int64 type = 3;
}
message EcomAuction {
    int64 price = 1;
    string priceLabel = 2;
    string buttonLabel = 3;
    EcomBidder user = 4;
    int64 status = 5;
}
message EcomBidder{
    string name = 1;
    EcomAvatar avatar = 2;
}
message EcomAvatar{
    string url = 1;
    string width = 2;
    int64 height = 3;
}
message EcomPop {
    int64 productId = 1;
    int64 promotionId = 2;
    string title = 3;
    string cover = 4;
    EcomPrice minPrice = 5;
    string sellingPoint = 6;
    string jumanjiJson = 7;
}
message EcomGoodsCard {
    int64 productId = 1;
    string title = 2;
    string cover = 3;
    EcomPrice minPrice = 4;
    Coupon coupon = 5;
    int64 promotionId = 6;
    int64 jumpDestination = 7;
    int64 cardType = 8;
    Redpack redpack = 9;
}
message Coupon{
    string icon = 1;
    string startTime = 2;
    string expireTime = 3;
    string couponName = 4;
    string url = 5;
    string couponString = 6;
    int64 countdown = 7;
    int64 showType = 8;
    string couponMetaId = 9;
    string couponType = 10;
}
message Redpack {
    int64 redpackType = 1;
    int64 redpackActivityId = 2;
    string redpackActivityDisplayText = 3;
    int64 startApplyTime = 4;
    int64 endApplyTime = 5;
    int64 preheatTime = 6;
    int64 serverTime = 7;
    int64 hasApplied = 8;
    string iconUrl = 9;
}
message RedsShowInfo {
    int32 dataType = 1;
    string fullText =2;
}
message RoomCartV2 {
    int32 showCart =1;
}
message IndustryServiceInfo {
    bool entranceOpen = 1;
    Image iconImage = 2;
    ConsultInfo consultInfo = 3;
    message ConsultInfo{
        string bizInfo  = 1;
        int32 consultRole = 2;
    }
}
message RelevantRecommendation {
    RelevantType relevantType = 1;
    BottomBarCategory bottomBarCategory = 2;
    Image icon = 3;
    string barTextPrefix = 4;
    string barTextPostfix = 5;
    HighLightInfo highLightInfo = 6;
    EcomInfo ecomInfo = 7;
    VsInfo vsInfo = 8;
    WhiteCategoryInfo whiteCategoryInfo = 9;
    enum RelevantType {
        UNKNOWN = 0;
        RELEVANT_ROOM = 1;
        ECOM = 2;
        ECOM_HIGHLIGHT = 3;
        HIGHLIGHT = 4;
        RELEVANT_ROOM_INNER = 5;
    }
    enum BottomBarCategory {
        NO_RELATED = 0;
        ENTERTAINMENT = 1;
        GAME = 2;
        ECOM_ = 3;
        BIG_PROMOTION = 4;
        HOT_SENTENCE = 5;
        VS = 6;
        WHITE_CATEGORY = 7;
    }
    message HighLightInfo {
        string mainUrl = 1;
        string bakUrl = 2;
    }
    message EcomInfo {
        int64 productId = 1;
        string productName = 2;
    }
    message Episode{
        int64 episodeId = 1;
        string episodeName = 2;
        Image episodeCover = 3;
        string currentPeriod = 4;
        int64 seasonId = 5;
        string itemId = 6;
    }
    message VsInfo {
        repeated Episode episodes = 1;
    }

    message WhiteCategoryInfo {
        int64 firstLevelTagId = 1;
    }
}
message RoomSpecificSceneTypeInfo {
    bool isUnionLiveRoom = 1;
    bool isLife = 2;
    int32 isProtectedRoom = 3;
    int32 isLastedGoodsRoom = 4;
    int32 isDesireRoom = 5;
    bool commentaryType = 6;
    int32 isSubOrientationVerticalRoom = 7;
}

message GameCPData {
    int32 isLiveAPromotedA = 1;
    string gameId = 2;
    string gameName = 3;
    int64 promoteInstanceId = 4;
}

message GamePlayData {
    int32 playType = 1;
    int64 playId = 2;
    int64 gameId = 3;
}
message UnionLiveInfo {
    repeated GuestAnchor guestAnchors = 1;
    string avatarDescription = 2;
    bool fixedSort = 3;
    string tag = 4;
    int32 type = 5;
    message GuestAnchor{
        User user = 1;
        string tag = 2;
        int32 status = 3;
    }
}
message BeautifyInfo {
    bool useFilter = 1;
    bool commerceUseFilter = 2;
}
message ToolBarData {
    repeated ToolBarComponentData entranceList = 1;
    repeated ToolBarComponentData morePanel = 2;
    int32 maxEntranceCnt = 3;
    repeated ToolBarComponentData landscapeUpRight = 4;
    map<int32, ComponentSkin> skinResource = 5;
    int32 maxEntranceCntLandscape = 6;
    ToolbarPermutation permutation = 7;
    ToolbarExtraInfo extraInfo = 100;
}
message ToolBarComponentData {
    GroupId groupId = 1;
    ComponentType componentType = 2;
    OpType opType = 3;
    string text = 4;
    string schemaUrl = 5;
    Image icon = 6;
    int64 showType = 7;
    int64 dataStatus = 8;
    string extra = 99;
    enum GroupId {
        PublicSlot = 0;
        ShareSlot = 1;
        IMSlot = 2;
        EasterEggSlot = 3;
        VrSlot = 4;
        MoreSlot = 5;
        CommerceSlot = 6;
        LinkmicSlot = 7;
        GiftSlot = 8;
        LiuyanSlot = 9;
        ChannelSlot = 10;
        VsToolbarSlot = 11;
        ClaritySlot = 12;
        OperationSlot = 13;
        AnthologySlot = 14;
        SnapshotSlot = 15;
        MultiCameraSlot = 16;
        MatchMultiCameraSlot = 17;
        MultiTabsSlot = 18;
        MicroAppSlot = 19;
        InteractionSlot = 20;
        DecorationSlot = 21;
    }
    enum ComponentType {
        TUnkown = 0;
        Cart = 1;
        LiveItem = 2;
        MicroApp = 3;
        WelfareProject = 4;
        GamePromotion = 5;
        MiniGame = 6;
        LifeGroupon = 7;
        PaidLiveTicket = 8;
        LittleGame = 9;
        ShareComponent = 20;
        IMComponent = 21;
        EasterEgg = 22;
        Vr = 23;
        MoreComponent = 24;
        ExchangeCoupon = 25;
        LinkmicComponent = 26;
        LiuyanComponent = 27;
        GiftComponent = 28;
        SpeedyGift = 29;
        IncomeExchange = 30;
        RoomChannel = 31;
        Operation = 32;
        SnapShot = 33;
        MultiCamera = 34;
        Clarity = 35;
        Anthology = 36;
        MatchMultiCamera = 37;
        MultiTabs = 38;
        InteractionPanel = 39;
        DecorationPanel = 40;
        Treasure = 41;
        DouYinMultiStream = 42;
        Prompt = 1001;
        WishList = 1002;
        GiftVote = 1003;
        Manager = 1004;
        DouPlus = 1005;
        DanmakuSetting = 1006;
        GiftEffect = 1007;
        CleanScreen = 1008;
        CastScreen = 1009;
        PadPlay = 1010;
        PcPlay = 1011;
        PadPcPlay = 1012;
        PlaySetting = 1013;
        DiggShake = 1014;
        WindowSetting = 1015;
        WindowPlay = 1016;
        Record = 1017;
        VipIM = 1018;
        Collect = 1019;
        Definition = 1020;
        TimingClose = 1021;
        GamePointsPlay = 1022;
        Denounce = 1023;
        DisLike = 1024;
        OptimizeRecommend = 1025;
        GiftConsumeRemind = 1026;
        MinorRefund = 1027;
        Renqibao = 1028;
        ScreenSetting = 1029;
        Widget = 1030;
        ShakeLinkmic = 1031;
        InteractSwitch = 1032;
        MarketWidget = 1033;
        ShareStory = 1034;
        BackRecord = 1035;
        UnionAnchors = 1036;
        WebcastSetting = 1037;
        LinkmicGame = 1038;
        AudioSubtitle = 1039;
        AudioEffect = 1040;
        EmergencyHelp = 1041;
        GiftConsumptionManager = 1042;
        LiveListen = 1043;
        LiveExperienceOpt = 1044;
        PublicAreaSetting = 1050;
        LinkmicAnnounce = 1051;
        GameTeamManage = 1052;
        FollowerShare = 1053;
        AnchorCompMusic = 3001;
        AnchorCompFansGroup = 3002;
        AnchorCompComment = 3003;
        AnchorCompPreview = 3007;
        AnchorCompContentManage = 3051;
        AnchorCompShare = 3053;
        AnchorCompRecord = 3056;
        AnchorCompContentSelection = 3057;
        AnchorCompPublicAreaSetting = 3058;
        AnchorCompPause = 3059;
        AnchorStableMode = 3060;
        AnchorCompLiveManage = 3103;
        AnchorCompFunctionSetting = 3104;
    }
    enum OpType {
        ToEntrace = 0;
        ToMore = 1;
        NoAction = 3;
    }
}
message ComponentSkin {
    map<string, ToolbarBizSkin> vertical = 1;
    map<string, ToolbarBizSkin> landscape = 2;
    map<string, ToolbarBizSkin> general = 3;
}
message ToolbarBizSkin {
    Image icon = 1;
}
message ToolbarPermutation {
    Rule vertical = 1;
    Rule landscape = 2;
    Rule general = 3;
    repeated int32 onDemandComponentList = 4;
    message Rule{
        repeated int32 GroupPriority = 1;
        repeated int32 ComponentSequence = 2;
    }
}
message ToolbarExtraInfo {
    int64 gamePromotionCoexist = 1;
}
message AnchorTabLabel {
    string content = 1;
    Image label = 2;
    Image icon = 3;
    int32 style = 4;
    int32 type = 5;
    string key = 6;
    string accessibleContent = 7;
    map<string, string> trackParams = 8;
    DSLDetail dslLabel = 9;
}
message LifeGrouponInfo {
    int64 dslId = 1;
    int64 dslType = 2;
    string dslGeckoDownloadUri = 3;
    string dslSliceInfo = 4;
    int64 version = 5;
    string dslData = 6;
    ExtraInfo extra = 7;
    message ExtraInfo{
        int64 height = 1;
        int64 width = 2;
    }
}
message VipData {
    int32 vipRoom =1;
}
message UpperRightWidgetData {
    string name = 1;
    int32 widgetType = 2;
    string extra = 3;
    int32 priority = 4;
}
message FeedbackCard {
    string title = 1;
    int64 feedbackId = 2;
    repeated Question question = 3;
    Condition condition = 4;
    int64 roomId = 5;
    Room roomData = 6;
    string negativeText = 7;
    Image bgm = 8;
    int32 feedbackType = 9;
    int32 inflowFeedbackType = 10;
    int32 anchorFeedbackType = 11;
    message Option {
        string key = 1;
        string text = 2;
        int64 subQuestionId = 3;
        bool negative = 4;
        string toastText = 5;
        int32 tendency = 6;
    }
    message RoomCardStruct {
        Room data = 1;
        string title = 2;
        string subTitle = 3;
    }
    message Question{
        string questionKey = 1;
        string questionText = 2;
        int64 type = 3;
        repeated Option options = 4;
        int64 questionId = 5;
        repeated RoomCardStruct roomCards = 6;
    }
    message ClientImpression {
        int64 ignoreSubmitCounts = 1;
        int64 ignoreSubmitShowInterval = 2;
        int64 endingPageShowDuration = 3;
    }
    message Condition {
        int64 fromTime = 1;
        int64 toTime = 2;
        repeated int64 actionIds = 3;
        int64 actionType = 4;
        int64 previewTime = 5;
        int64 pcuLowerThreshold = 6;
        ClientImpression clientImpression = 7;
    }
}
message DesireInfo {
    int64 desireId = 1;
    string desireIdStr = 2;
}

message HotRoomInfo {
    int64 BitMap = 1;
}
message CastScreenData {
    string showText = 1;
    repeated int64 allowList = 2;
    SDKVersion sdkVersion = 3;
    CastOttPermission permission = 4;
    int32 forceCastOnly = 5;
    enum SDKVersion {
        UnknownSDK = 0;
        LeBoSDK = 1;
        OttCastSDK = 2;
        OttCastSDKNewUI = 3;
    }
    enum ForceCastOnlyType {
        UnknownForceCastOnly = 0;
        ForceCastOnly = 1;
    }
}
message CastOttPermission{
    bool enable = 1;
    string failureToast = 2;
}
message OfficialChannelExtraInfo {
    int64 showStartTs = 1;
    string showlistSchema = 2;
    int64 showlistId = 3;
    string showlistName = 4;
}

message ActivityLiveRecommendConfig {
    string name = 1;
    string level = 2;
    int64 startTime = 3;
    int64 endTime = 4;
    int32 liveType = 5;
    repeated int64 actorUids = 6;
    EpisodeInfo episodeInfo = 7;
    repeated string actorOpenids = 5000;
    message EpisodeInfo{
        int32 contentType = 1;
        string context = 2;
    }
}
message RoomChannelData {
    int32 status = 1;
    repeated RoomChannelInfo channelList = 2;
    int32 supportRoomChannelMode = 3;
    int32 limitOfNum = 4;
    bool canCreate = 5;
    Image background = 6;
    bool tabLanding = 7;
    bool canLinkMic = 8;
    bool hideLandscape = 101;
}
message RoomChannelInfo{
    int64 channelId = 1;
    string token = 2;
    bool isOwner = 3;
    int64 memberCount = 4;
    repeated User topUser = 5;
    string channelName = 6;
    int32 roomChannelMode = 7;
    User needApprovalUser = 8;
    User owner = 9;
    int64 maxMemberCount = 10;
    RtcExtInfo rtcExtInfo = 200;
}
message RtcExtInfo{
    string linkMicIdStr = 1;
    string rtcExtInfo = 2;
    string liveCoreExrInfo = 3;
    string rtcStrategy = 4;
    string publicStreamId = 5;
}
message PackMetaInfo {
    string scene = 1;
    string env = 2;
    string dc = 3;
    string traceId = 4;
    string cluster = 5;
    map<string, string> extras = 6;
}
message ActivityData {
    bool xgPlay = 1;
    MatchRoomData match = 2;
}

message LikeDisplayConfig {
    int32 showText = 1;
    string displayText = 2;
}

message RoomViewStats {
    bool isHidden = 1;
    string displayShort = 2;
    string displayMiddle = 3;
    string displayLong = 4;
    int64 displayValue = 5;
    int64 displayVersion = 6;
    bool incremental = 7;
    int32 displayType = 8;
    string displayShortAnchor = 9;
    string displayMiddleAnchor = 10;
    string displayLongAnchor = 11;
}
message MatchRoomData {
    MatchDrawer drawer = 1;
    ToutiaoMatchData match = 2;
    Image drawerLabel = 3;
    string pcuStr = 4;
    Image drawerOfficialLabel = 5;
    Image matchBackground = 6;
    MatchUserInfo matchUserInfo = 7;
}
message MatchUserInfo{
    TeamInfo userTeamInfo = 1;
    map<int64, int64> userQuizInfo = 2;
    bool isActivityAccount = 3;
    bool needRetry = 4;
}
message TeamInfo {
    int64 teamId = 1;
    string teamName = 2;
    string schemaUrl = 3;
    Image teamIcon = 4;
    Image teamBadge = 5;
    Image teamBackground = 6;
    string systemMsg = 7;
    string teamIdStr = 8;
    Image teamAvatarBox = 9;
}
message CommentaryRoomInfo {
    int64 userId = 1;
    Image avatar = 2;
    string nickname = 3;
    string title = 4;
    int64 roomId = 5;
    string openId = 5000;
}
message MatchChatConfig {
    string mainGroupIconUrl = 1;
    string mainGroupBackgroundUrl = 2;
    string guestGroupIconUrl = 3;
    string guestGroupBackgroundUrl = 4;
    repeated string aggregateIconUrl = 5;
}
message ShareResource {
    Image toastBackground = 1;
    map<string, string> qrcode = 2;
    string ugShareInfo = 3;
}
message PublicScreenBottomInfo {
    repeated BottomCard bottomCards = 1;
    message BottomCard{
        string name = 1;
        int64 priority = 2;
        int64 duration = 3;
        string bizParams = 4;
    }
}
message RoomBasisData {
    int64 nextPing = 1;
    bool isCustomizeAudioRoom = 2;
    int64 needRequestLuckybox = 3;
}
message RoomInteractData {
    int64 landscapeCommentStyle = 1;
    EpisodeExtraInfo vsComponentExtra = 2;
    FeaturedPublicScreenConf featuredPublicScreenData = 3;
    PublicScreenSpeedConf publicScreenSpeedConf = 4;
    RoomIntroLabel publicRoomIntroLabel = 5;
}
message FeaturedPublicScreenConf{
    int64 status = 1;
}
message PublicScreenSpeedConf{
    int64 updateInterval = 1;
    int32 scrollSize = 2;
    int32 foldSize = 3;
    int32 scrollSpeed = 4;
}
message RoomIntroLabel{
    int32 labelTag = 1;
    string labelName = 2;
    string labelText = 3;
    string labelIcon = 4;
    string typeName = 5;
    string showText = 6;
    bool selected = 7;
    int64 showOrder = 8;
}
message RoomRevenueData {
    OpenGame openGame = 1;
    AnchorLinkmic anchorLinkmic = 2;
}
message OpenGame{
    string streamId = 1;
    string appId = 2;
    int64 anchorType = 3;
    int64 interactType = 4;
}
message AnchorLinkmic{
    int64 channelId = 1;
}
message RoomReqUserData {
    float userShareRoomScore = 1;
    int32 enterUserDeviceType = 2;
}
message RoomAnchorData {
    string finishSchema = 1;
    GameAnchorInfo gameAnchorInfo = 2;
    string frameScale = 3;
    string groupIdList = 4;
}
message GameAnchorInfo{
    string categoryId = 1;
    bool isKeyAnchor = 2;
}
message RoomOthersData {
    DecotationDetail decoDetail = 1;
    MorePanelData morePanelInfo = 2;
    AppointmentData appointmentInfo = 3;
    WebSkinData webSkin = 4;
    WebProgramme programme = 5;
    LiveMatrixInfo liveMatrixInfo = 6;
    WebLivePortOptimization webLivePortOptimization = 7;
    GiftPanelTopperTray giftPanelTopperTray = 8;
    EnterGiftAnimation enterGiftAnimation = 9;
    PaidLiveSubscribe paidLiveSubscribe = 10;
    GroupLiveData groupLiveData = 11;
    int64 lvideoItemId = 12;
    WebEnterBenefitPointData webEnterBenefitPointData = 13;
    StreamRecognitionContainers recognitionContainers = 14;
    AnchorBottomToolBar anchorBottomToolBar = 15;
    AnchorTogetherLive anchorTogetherLive = 16;
    int64 mosaicVersion = 17;
}
message AnchorTogetherLive{
    int64 isTogetherLive = 1;
    repeated User userList = 2;
    string title = 3;
    string schemaUrl = 4;
    int64 scene = 5;
    bool isShow = 6;
}
message AnchorBottomToolBar{
    AnchorLinkmicEntranceInfo anchorLinkmicEntrance = 1;
    AudienceLinkmicEntranceInfo audienceLinkmicEntrance = 2;
}
message AnchorLinkmicEntranceInfo{
    PanelType panelType = 1;
    Image icon = 2;
    string text = 3;
    Image pkToLinkmicImage = 4;
    Image linkmicToPkImage = 5;
    Image leadPkImage = 6;
    string leadPkFreqKey = 7;
    Image pkIcon = 8;
    Image anchorIcon = 9;
    string wordType = 10;
    map<int64, LeadInfo> buttonLead = 11;
    enum PanelType{
        PK = 0;
        Anchor = 1;
    }
    enum LeadType{
        PKToLinkmic = 0;
        LinkmicToPK = 1;
        LeadPK =2;
    }
    message LeadInfo{
        Image leadImage = 1;
        string bubbleText = 2;
        int32 bubblePanel = 3;
    }
}
message AudienceLinkmicEntranceInfo{
    Image icon = 1;
    string text = 2;
}
message StreamRecognitionContainers{
    repeated StreamRecognitionCandidate recognitionCandidates = 1;
}
message StreamRecognitionCandidate{
    int32 sceneEnum = 1;
    int32 priority = 2;
}
message WebEnterBenefitPointData{
    bool hasOngoingLottery = 1;
    bool hasOngoingLuckyMoney = 2;
}
message GroupLiveData{
    int64 isGroupLive = 1;
    int32 groupLiveMode = 2;
}
message PaidLiveSubscribe{
    int32 paidLiveType = 1;
    int64 subscribeEventTime = 2;
}
message EnterGiftAnimation{
    int32 giftEffectId = 1;
    int32 displayGap = 2;
    int32 displayFreq = 3;
}
message GiftPanelTopperTray{
    string panelSchema = 1;
    repeated PeriodText periodTexts = 2;
    int32 displayDuration = 3;
    string jumpSchema = 4;
    string animationType = 5;
    int32 animationGap = 6;
    int32 animationFreq = 7;
    int32 textRoundPeriod = 8;
    string extra = 9;
    message PeriodText{
        string text = 1;
        int32 dailyShowTimes = 2;
        int32 priority = 3;
        int32 panelEnum = 4;
        int32 key = 5;
        string extra = 6;
    }
}
message LiveMatrixInfo{
    int64 id = 1;
    string name = 2;
}
message DecotationDetail{
    Decoration textDecoration = 1;
    Decoration imageDecoration = 2;
}
message WebLivePortOptimization{
    map<string, WebLivePortConfig> strategyConfig = 1;
    string strategyExtra = 2;
}
message AppointmentData{
    int64 appointmentId = 1;
    bool isSubscribe = 2;
}
message MorePanelData{
    int32 loadStrategy = 1;
}
message WebLivePortConfig{
    int32 strategyType = 1;
    bool useConfigDuration = 2;
    string pauseMonitorDuration = 3;
}
message WebSkinData{
    bool enableSkin = 1;
}
message WebProgramme{
    bool enableProgramme = 1;
}
message PicoInfo {
    int64 picoLiveType = 1;
    string picoVirtualLiveBgImageUri = 2;
    string picoCreateScene = 3;
    string customInfo = 4;
    string picoVirtualLiveBgImageDigest = 5;
    VirtualLiveBgImages virtualLiveBgImages = 6;
    float pitch = 7;
    int64 clientLiveType = 8;
    int32 picoVrTransfer = 9;
    int64 picoLiveMode = 11;
    map<string, string> streamMapping = 13;
    message VirtualLiveBgImages{
        Image originalImage = 1;
        string originalDigest = 2;
        bool isUpright = 3;
        repeated Image convertedImages = 4;
        repeated ConvertedImage convertedList = 5;
        message ConvertedImage{
            int64 quality = 1;
            Image image = 2;
            string digest = 3;
        }
    }
}
message RoomGameData {
    RoomGameDataChannelConfig roomGameDataChannelConfig = 1;
    GameTagInfo gameTagInfo = 2;
    SandwichBorderInfo sandwichBorderInfo = 3;
}
message RoomGameDataChannelConfig {
    string fusionTagId = 1;
    string gameId = 2;
    int64 liveStayLimit = 3;
}
message GameTagInfo {
    int32 isGame = 1;
    int64 gameTagId = 2;
    string gameTagName = 3;
}
message SandwichBorderInfo {
    double top = 1;
    double bottom = 2;
    double left = 3;
    double right = 4;
}
message RoomFeedData {
    LabelInfo relationLabel = 1;
    FollowTopAppearance followTopAppearance = 2;
    StreamCutPosition cutPosition = 3;
    DrawerConfig drawerConfig = 4;
    bool canShowInnerCard = 5;
    message DrawerConfig{
        AvatarArea avatarArea = 1;
        EntranceIcon entranceIcon = 2;
        EntranceTitle entraceTitle = 3;
        ArrowView arrowView = 4;
        EntranceLayout entranceLayout = 5;
        SpecialConfig specialConfig = 6;
        message AvatarArea {
            int32 canShowFollow = 1;
        }
        message EntranceIcon {
            Image image = 1;
            int32 animShowMethod = 2;
            int32 animStopMethod = 3;
            string height = 4;
        }
        message EntranceTitle {
            string text = 1;
        }
        message ArrowView {
            int32 visibility = 1;
        }
        message EntranceLayout {
            int32 layoutParams = 1;
        }
        message FreqInfo{
            int32 showDuration = 1;
            string freqKey = 2;
            int32 freqDuration = 3;
        }
        message SpecialConfig {
            EntranceTitle entraceTitle = 1;
            int32 moreLiveTab = 2;
            int32 insertRoomReason = 3;
            FreqInfo freqInfo = 4;
            string currentTalent = 5;
        }
    }
}
message FollowTopAppearance {
    int32 style = 1;
    StreamCutPosition cutPosition = 2;
    Image image = 3;
}
message StreamCutPosition{
    double x1 = 1;
    double y1 = 2;
    double x2 = 3;
    double y2 = 4;
    int32 sourceWidth = 5;
    int32 sourceHeight = 6;
}
message OpenContentData {
    OpenActivityData openActivityData = 1;
}
message OpenActivityData{
    Image coverLabel = 1;
}
message ClientComponent {
    map<string, ClientComponentInfo> portraitComponentMap = 1;
    map<string, ClientComponentInfo> landscapeComponentMap = 2;
    bool isOpen = 3;
    int64 templateId = 4;
    map<string, ClientComponentInfo> previewDefaultComponentMap = 5;
}
message ClientComponentInfo{
    string containerId = 1;
    ClientComponentFunctionInfo functionDetail = 2;
    bool isOpen = 3;
    DSLDetail dslDetail = 4;
    string dslLayout = 5;
    ContainerLayout containerLayout = 6;
}
message ClientComponentFunctionInfo {
    string functionId = 1;
    map<string, string> data = 2;
}
message DSLDetail {
    int64 dslId = 1;
    int64 dslType = 2;
    string dslGeckoDownloadUri = 3;
    string dslSliceInfo = 4;
    int64 version = 5;
    string dslData = 6;
    ExtraInfo extra = 7;
    message ExtraInfo {
        int64 height = 1;
        int64 width = 2;
    }
}
message ContainerLayout {
    int32 width = 1;
    int32 height = 2;
    int32 marginLeft = 3;
    int32 marginTop = 4;
    int32 marginBottom = 5;
    int32 marginRight = 6;
    int32 paddingLeft = 7;
    int32 paddingRight = 8;
    int32 paddingBottom = 9;
    int32 paddingTop = 10;
}
message RoomPlatformComponentsData {
    RoomTitle roomTitle = 1;
    RoomViewStats roomCount = 2;
    RoomAvator roomAvator = 3;
    RoomPublicScreenInfo publicScreenInfo = 4;
    RoomFullVideoBTN fullVideoBtn = 5;
    RoomBanner banner = 6;
    RoomCore core = 7;
}
message RoomTitle {
    string title = 1;
    string introduction = 2;
    int64 leftTag = 3;
    string leftPeriod = 4;
    string leftToast = 5;
    TitleIcon leftIcon = 6;
    int32 uiType = 7;
    bool portraitShow = 8;
    bool landscapeShow = 9;
}

message RoomAvator {
    Image avatar = 1;
    Image authenticationInfo = 2;
    string nickName = 3;
    bool hasFansClub = 4;
    int64 followStatus = 5;
    bool invalidFollowStatus = 6;
    int64 followReportScene = 7;
    AvatorBorder border = 8;
    repeated int64 firstLabel = 9;
    repeated int64 secondLabel = 10;
    int64 fansClubType = 11;
    int64 animationType = 12;
    int64 uid = 13;
    string secUid = 14;
    int64 fanTicket = 15;
    int64 likeCount = 16;
    string totalUserStr = 17;
    int32 secret = 18;
    AnchorTabLabel firstTabLabel = 19;
    AnchorTabLabel secondTabLabel = 20;
    string openId = 5000;
}
message AvatorBorder{
    Image icon = 1;
    int64 level = 2;
    Image thumbIcon = 3;
    string dressId = 4;
}
message RoomPublicScreenInfo {
    repeated RoomPublicScreenAttachments attachments = 1;
    string backgroundColor = 2;
    string longPressColor = 3;
}
message RoomPublicScreenAttachments{
    int64 hotMessageTray = 1;
    int64 heightSetting = 2;
    int64 firstMessageShowOpt = 3;
    int64 anchorColdMessageShowOpt = 4;
}

message RoomFullVideoBTN {
    int64 show = 1;
    Image icon = 2;
    Image disableIcon = 3;
    int64 showType = 4;
}

message RoomBanner {
    int64 AuthMsg = 1;
}
message RoomCore {
    int64 roomId = 1;
    int64 anchorId = 2;
    string anchorOpenId = 5000;
}
message IOSClientComponent {
    repeated IOSFragment fragments = 1;
    repeated IOSContainerLayout landscapeLayout = 2;
    repeated IOSContainerLayout portraitLayout = 3;
    bool isOpen = 4;
    int64 templateId = 5;
    repeated IOSFragment elements = 6;
}
message IOSFragment {
    string name = 1;
    bool isOpen = 2;
}
message IOSContainerLayout {
    string name = 1;
    IOSContainerLayoutRule layoutRule = 2;
    int32 dsl = 3;
}
message IOSContainerLayoutRule{
    repeated string items = 1;
    bool ruleReplace = 2;
    string target = 3;
    string ruleType = 4;
}
message RoomExtra{
    bool isSandbox = 1;
    RegionRestriction enterRegionRestriction = 2;
    RegionMatch enterRegionMatch = 3;
    SafeReason filterWithNoContext = 4;
    int64 xiguaUid = 5;
    int64 limitStrategy = 6;
    string limitAppid = 7;
    int32 geoBlock = 8;
    int32 vrType = 9;
    bool isVirtualAnchor = 10;
    string createScene = 11;
    bool realtimeReplayEnabled = 12;
    int64 realtimePlaybackShift = 13;
    int64 realtimePlaybackStartShift = 14;
    repeated StreamUrl.LiveCoreSDKData.PullData.Options.Quality realtimePlaybackQualities = 15;
    int32 facialUnrecognised = 16;
    int64 vsType = 17;
    LocationInfo locationInfo = 18;
}
message RegionMatch{
    int64 type = 1;
    repeated string allowList = 2;
    repeated string denyList = 3;
}
message SafeReason{
    int32 safeType = 1;
}
message LocationInfo {
    AddressInfo gpsInfo = 1;
    AddressInfo ipInfo = 2;
    bool enablePermission = 3;
    bool enableLocationMode = 4;
}
message AddressInfo{
    int64 countryGeoNameId = 1;
    int64 provinceCode = 2;
    int64 cityCode = 3;
    string country = 4;
    string province = 5;
    string city = 6;
}
message RegionRestriction{
    int64 type = 1;
    repeated string whiteList = 2;
    repeated string blackList = 3;
}
message RoomStats {
    int64 id = 1;
    string idStr = 2;
    int64 fanTicket = 3;
    int64 money = 4;
    int64 totalUser = 5;
    int64 giftUvCount = 6;
    int64 followCount = 7;
    UserCountComposition userCountComposition = 8;
    int64 watermelon = 9;
    int64 diggCount = 10;
    int64 enterCount = 11;
    string douPlusPromotion = 12;
    string totalUserDesp = 13;
    int64 likeCount = 14;
    string totalUserStr = 15;
    string userCountStr = 16;
    int64 commentCount = 17;
    int64 welfareDonationAmount = 18;
    string upRightStatsStr = 19;
    string upRightStatsStrComplete = 20;
    message UserCountComposition{
        double city = 1;
        double videoDetail = 2;
        double myFollow = 3;
        double other = 4;
    }
}
message RoomUserAttr{
    int64 roomId = 1;
    string roomIdStr = 2;
    int64 silenceFlag = 3;
    int64 adminFlag = 4;
    int64 rank = 5;
}
message StreamUrl{
    int64 provider = 1;
    int64 id = 2;
    string idStr = 3;
    map<string, string> resolutionName = 4;
    string defaultResolution = 5;
    StreamUrlExtra extra = 6;
    string rtmpPushUrl = 7;
    string rtmpPullUrl = 8;
    map<string, string> flvPullUrl = 9;
    repeated string candidateResolution = 10;
    string hlsPullUrl = 11;
    string hlsPullUrlParams = 12;
    string rtmpPullUrlParams = 13;
    map<string, string> flvPullUrlParams = 14;
    string rtmpPushUrlParams = 15;
    repeated string pushUrls = 16;
    LiveCoreSDKData liveCoreSdkData = 17;
    map<string, string> hlsPullUrlMap = 18;
    repeated string completePushUrls = 19;
    int32 streamControlType = 20;
    int32 streamOrientation = 21;
    int32 pushStreamType = 22;
    map<string, LiveCoreSDKData.PullData> pullDatas = 23;
    PlaySetting play = 24;
    map<string, LiveCoreSDKData.PushData> pushDatas = 25;
    int32 vrType = 26;
    OpenStreamUrlEncrypt openStreamUrlEncrypt = 5000;
    message StreamUrlExtra{int64 height = 1;
        int64 width = 2;
        int64 fps = 3;
        int64 maxBitrate = 4;
        int64 minBitrate = 5;
        int64 defaultBitrate = 6;
        int64 bitrateAdaptStrategy = 7;
        int64 anchorInteractProfile = 8;
        int64 audienceInteractProfile = 9;
        bool hardwareEncode = 10;
        int64 videoProfile = 12;
        SuperResolution superResolution = 14;
        bool h265Enable = 15;
        int64 gopSec = 16;
        bool bframeEnable = 17;
        bool roi = 18;
        bool swRoi = 19;
        bool bytevc1Enable = 20;
        AnchorClientInfo anchorClientInfo = 21;
        AdaptionInfo adaptionInfo = 22;
        message SuperResolution{
            bool enable = 1;
            int64 strength = 2;
            bool antialiasing = 3;
        }
        message AnchorClientInfo{
            string customInfo = 1;
        }
        message AdaptionInfo{
            int32 verticalResizeMode = 1;
        }
    }
    message LiveCoreSDKData{
        PullData pullData = 1;
        PushData pushData = 2;
        string size = 3;
        message PullData{
            string streamData = 1;
            Options options = 2;
            int64 version = 3;
            map<string, string> hlsDataUnencrypted = 4;
            int32 kind = 5;
            Extension extension = 6;
            repeated PlayInfo Hls = 7;
            repeated PlayInfo Flv = 8;
            string codec = 9;
            Display display = 10;
            string compensatoryData = 11;

            message Options {
                message Quality {
                    string name = 1;
                    string sdkKey = 2;
                    string vCodec = 3;
                    string resolution = 4;
                    int32 level = 5;
                    int32 vBitRate = 6;
                    string additionalContent = 7;
                    int32 fps = 8;
                    int32 disable = 9;
                }
                Quality defaultQuality = 1;
                repeated Quality qualities = 2;
                bool vpassDefault = 3;
            }

            message Clip {
                int32 anchor = 1;
                float x = 2;
                float y = 3;
                float w = 4;
                float h = 5;
            }
            message Extension {
                Clip gameClip = 1;
                Clip cameraClip = 2;
                int32 cameraHidden = 3;
                string ts = 4;
                int64 refresh = 5;
                int32 displayMode = 6;
                int32 gameHidden = 7;
                string gameRoomId = 8;
                int32 layout = 9;
                Clip cameraClipCustom = 10;
                int32 cameraVerticalType = 11;
                CameraHorizontalPosition cameraHorizontalPosition = 12;
                int32 cameraHorizontalHidden = 13;
                int32 cameraHorizontalType = 14;
            }
            message CameraHorizontalPosition {
                int32 anchor = 1;
                float x = 2;
                float y = 3;
                float w = 4;
                float h = 5;
            }

            message PlayInfo {
                string url = 1;
                string qualityName = 2;
                string params = 3;
            }

            message Display {
                int64 scaleWidth = 1;
                int64 scaleHeight = 2;
            }
        }
        message PushData {
            map<string, ResolutionParams> resolutionParams = 1;
            int32 pushStreamLevel = 2;
            bool preSchedule = 3;
            string rtmpPushUrl = 4;
            string pushParams = 5;
            int32 kind = 6;
            int64 streamId = 7;
            string streamIdStr = 8;
            message ResolutionParams {
                int64 width = 1;
                int64 height = 2;
                int64 defaultBitrate = 3;
                int64 minBitrate = 4;
                int64 maxBitrate = 5;
                int64 fps = 6;
            }
        }
    }
    message PlaySetting{
        string horizontal = 1;
        string vertical = 2;
    }
}
message OpenStreamUrlEncrypt{
    string rtmpPullUrl = 1;
    string hlsPullUrl = 2;
    map<string, string> flvPullUrl = 3;
    string streamData = 4;
    repeated StreamUrl.LiveCoreSDKData.PullData.PlayInfo Hls = 5;
    repeated StreamUrl.LiveCoreSDKData.PullData.PlayInfo Flv = 6;
    map<string, string> hlsPullUrlMap = 7;
    map<string, StreamUrl.LiveCoreSDKData.PullData> pullDatas = 8;
}
message LinkMic {
    int64 channelId = 1;
    LinkMicChannelInfo channelInfo = 2;
    repeated LinkMicBattleScore battleScores = 3;
    LinkMicBattleSetting battleSettings = 4;
    int64 rivalAnchorId = 5;
    int64 linkmicAnchorCount = 6;
    string rivalAnchorOpenId = 5000;
    message LinkMicChannelInfo {
        int64 layout = 1;
        int64 vendor = 2;
        int64 dimension = 3;
    }

    message LinkMicBattleScore {
        int64 userId = 1;
        int64 score = 2;
        string openId = 5000;
    }

    message LinkMicBattleSetting {
        int64 channelId = 1;
        int64 duration = 2;
        int64 startTime = 3;
        int64 startTimeMs = 4;
        string theme = 5;
        int64 finished = 6;
        int64 battleId = 7;
        int64 matchType = 8;
        int64 playMode = 9;
        int64 teamMode = 10;
        int64 activityMode = 11;
    }
}
message Decoration {
    int64 id = 1;
    Image image = 2;
    int64 type = 3;
    repeated int64 inputRect = 4;
    int64 textSize = 5;
    string textColor = 6;
    string content = 7;
    int64 maxLength = 8;
    int64 status = 9;
    int64 h = 10;
    int64 x = 11;
    int64 w = 12;
    int64 y = 13;
    int64 kind = 14;
    int64 subType = 15;
    Reservation reservation = 16;
    Image ninePatchImage = 17;
    repeated int64 textSpecialEffects = 18;
    int64 textImageAdjustableStartPosition = 19;
    int64 textImageAdjustableEndPosition = 20;
    DecorationFontConfig textFontConfig = 21;
    string auditTextColor = 22;
}
message Reservation {
    int64 appointmentId = 1;
    int64 anchorId = 2;
    int64 roomId = 3;
    int64 startTime = 4;
    int64 endTime = 5;
    repeated int64 btnRect = 6;
    string btnColor = 7;
    bool isReserved = 8;
    string anchorOpenId = 5000;
}
message DecorationFontConfig {
    Decoration textDecoration = 1;
    Decoration imageDecoration = 2;
}
message TopFan {
    int64 fanTicket = 1;
    User user = 2;
}
message IndustryCertificationProfile{
    Image icon = 1;
    string content = 2;
    string schema = 3;
    string contentColor = 4;
    string bgColor = 5;
    int64 certType = 6;
}
message IndustryCertificationRoom{
    RoomOwner roomOwner = 1;
    message RoomOwner {
        Image title = 1;
        string subTitle = 2;
        string leftColor = 3;
        string rightColor = 4;
        int32 anchorIndustryType = 5;
        Image brandStoreTitle = 6;
        Image brandStoreBackground = 7;
        Image anchorRedsIcon = 8;
        Image anchorRedsSmallIcon = 9;
    }
}
message IndustryCertification {
    IndustryCertificationProfile profile = 1;
    IndustryCertificationRoom room = 2;
}
message OpenHostInfo {
    string nickname = 1;
    int32 gender = 2;
    string signature = 3;
    Image avatarThumb = 4;
    Image avatarMedium = 5;
    Image avatarLarge = 6;
    User.FollowInfo followInfo = 7;
    map<string, string> schemas = 8;
    map<string, string> extra = 100;
}
message GiftSortStrategy{
    string scene = 1;
    repeated int64 giftIds = 2;
    repeated int64 tagIds = 3;
    int64 startTime = 4;
    int64 endTime = 5;
    string extra = 6;
    int32 strategyType = 7;
    int32 updateType = 8;
    repeated int64 effectedGiftIds = 9;
}
message GiftVoteResult{
    string text = 1;
    int64 count = 2;
    Image icon = 3;
    int64 giftId = 4;
    string name = 5;
    int64 diamondCount = 6;
    int64 giftType = 7;
    string countStr = 8;
}
message GuestBattleInfo{
    int64 battleId = 1;
    int64 battleType = 2;
    int64 status = 3;
    repeated BattleResult results = 4;
    int64 currentTime = 5;
    int64 finishTime = 6;
    int64 showDuration = 7;
    string battleIdStr = 8;
    int64 loserNum = 9;
    string dressId = 10;
    int64 scoreType = 11;
    GuestBattleUIInfo uiInfo = 12;
    int64 threshold = 13;
    int64 sprintDuration = 14;
    int32 battleStage = 15;
    int64 uiType = 16;
    int64 startOperatorUserId = 17;
    string startOperatorUserOpenId = 5000;
}
message BattleResult{
    int64 guestId = 1;
    string score = 2;
    int64 rank = 3;
    string scoreFuzzy = 4;
    GuestBattleContributors contributors = 5;
    LinkmicQuickInteract quickInteract = 6;
    GuestBattleUserGradeInfo crownInfo = 7;
    string nickname = 8;
    int64 scoreNum = 9;
    int32 role = 10;
    string guestIdStr = 11;
    bool forcePlay = 12;
    User user = 13;
    int64 realScore = 14;
    User titleSponsor = 15;
    bool hitToast = 16;
    string guestOpenId = 5000;
    string guestOpenIdStr = 5001;
}
message GuestBattleContributors{
    repeated User userIds = 1;
    repeated string openIds = 5000;
}
message LinkmicQuickInteract{
    User sendGiftUser = 1;
    int64 thanksCarouselDuration = 2;
    string thanksContent = 3;
    int64 receiveGiftUserId = 4;
    int32 interactType = 5;
    Text text = 6;
    string schema = 7;
    string receiveGiftUserOpenId = 5000;
}
message GuestBattleUserGradeInfo{
    GuestBattleGradeItem curGrade = 1;
    GuestBattleGradeItem nextGrade = 2;
    int64 upgradeScore = 3;
    string upgradeScoreStr = 4;
}
message GuestBattleGradeItem{
    int64 level = 1;
    string levelStr = 2;
    int64 score = 3;
    string scoreStr = 4;
    bool isFullLevel = 5;
}
message GuestBattleUIInfo{
    Image openAnimation = 1;
    Image scoreIcon = 2;
    Image hotBattleOpenAnimation = 3;
    Image sprintOpenAnimation = 4;
}
message EnterPublicAreaAnimation {
    string publicAreaEffectUrl = 1;
}
message EmojiDetail{
    int64 id = 1;
    Image content = 2;
}
message ChatIdentity{
    int32 showIdentity = 1;
    Image identityLabel = 2;
}
message ShortTouchArea{
    int32 type = 1;
    uint32 priority = 2;
    int64 minWebcastSdkVersion = 3;
    int32 shortTouchType = 4;
    ShortTouchInfo shortTouchInfo = 5;
    ShortTouchBigCard shortTouchBigCard = 6;
    string containerPayload = 7;
    int32 loadType = 8;
    ShortTouchBubble bubbleParams = 9;
    string name = 10;
    int32 hideMode = 11;
    int32 enhancedTouch = 12;
}
message ShortTouchInfo {
    string shortTouchUrl = 1;
    string shortTouchFallbackUrl = 2;
    int32 containerType = 3;
    uint32 width = 4;
    uint32 height = 5;
    string imgUrl = 6;
    string jumpSchema = 7;
    int32 showAnimation = 8;
    int32 animationType = 9;
    repeated string subItemList = 10;
    ShortTouchImageLayers imgLayers = 11;
    uint32 animationQuota = 12;
    string accessibleName = 13;
}
message ShortTouchImageLayers{
    repeated Layer layers = 1;
    int32 baseWidth = 2;
    int32 baseHeight = 3;
    message Layer {
        string url = 1;
        int32 x = 2;
        int32 y = 3;
        int32 width = 4;
        int32 height = 5;
        int32 radius = 6;
    }
}
message ShortTouchBigCard {
    string bigCardUrl = 1;
    int32 containerType = 2;
    int32 width = 3;
    int32 height = 4;
    int32 duration = 5;
}
message ShortTouchBubble {
    string bubbleId = 1;
    TextViewModel uiModel = 2;
    int32 strategy = 3;
    int32 duration = 4;
}
message TextViewModel{
    string text = 1;
    string textColor = 2;
    int32 textSize = 3;
    string bgColor = 4;
}
message RoomIntroAppointmentInfo {
    bool enabled = 1;
    string appointment_id = 2;
    string scheduled_time_text = 3;
    int32 scheduled_time = 4;
    int32 scheduled_date = 5;
    int64 next_live_start_time = 6;
    repeated int32 scheduled_weekdays = 7;
    string content = 8;
}
message ActivityEmojiGroup{
    int64 id = 1;
    string id_str = 2;
    string name = 3;
    Image tag_icon = 4;
    string desc = 5;
    repeated ActivityEmoji emoji_list = 6;
    int64 insert_emoji_num = 7;
}
message ActivityEmoji{
    int64 id = 1;
    string id_str = 2;
    string name = 3;
    Image emoji = 4;
}
message CombinedText {
    repeated DisplayItem display_items = 1;
    SchemaInfo schema_info = 10;
    ComboInfo combo_info = 11;
}
message DisplayItem {
    int32 display_item_type = 1;
    bool combo_fresh = 20;
    SchemaInfo schema_info = 21;
    ImagesItem images_item = 50;
    TextItem text_item = 51;
    DisplayItemFormat format = 100;
}
message ImagesItem{
    repeated Image images = 1;
    int32 display_style = 20;
}
message TextItem{
    Text text = 1;
}
message DisplayItemFormat{
    bool enable_left_space = 1;
    int64 left_space = 2;
}
message ComboInfo{
    int64 combo_seq = 1;
    int64 combo_order = 2;
}
message VideoEqualRoomRtcInfo{
    map<int64, string> rtcInfoMap = 1;
}
message VideoPositionRtcInfo{
    map<int64, string> rtcInfoMap = 1;
}
message MultiAnchorLinkmicRtcInfo{
    map<int64, string> rtcInfoMap = 1;
    map<int64, string> pushStreamModeRtcMap = 2;
}
message SingingChallengeRtcInfo{
    map<int64, string> rtcInfoMap = 1;
}
message CrossRoomLinkmicRtcInfo{
    map<int64, string> rtcInfoMap = 1;
}
message GameBarrageRtcInfo{
    int64 linkerId = 1;
    string rtcInfo = 2;
}
message VideoDUOBattleRtcInfo{
    map<int64, string> rtcInfoMap = 1;
}