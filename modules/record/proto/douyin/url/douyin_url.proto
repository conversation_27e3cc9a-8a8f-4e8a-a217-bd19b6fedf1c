syntax = "proto3";
package douyin;

message Gift {
  int32 diamond_count = 1;
  int32 gift_id = 2;
  int32 status = 3;
  int32 type = 4;
}

message Image {
  string avg_color = 1;
  string uri = 2;
  repeated string url_list = 3;
}

message Avatar {
  string avg_color = 1;
  string uri = 2;
  repeated string url_list = 3;
}

message User {
  Avatar avatar = 1;
  string nickname = 2;
  int64 user_id = 3;
  string user_id_str = 4;
}

message LightedItem {
  message AdvancedTask {
    int32 current_count = 1;
    int32 group_id = 2;
    bool is_finished = 3;
    int32 reward_star_count = 4;
    string task_id = 5;
    int32 threshold = 6;
  }

  message Extra {
    string is_customized = 1;
  }

  Gift gift = 1;
  bool is_lighted = 2;
  int32 lighted_threshold = 3;
  User naming_user = 4;
  int32 received_count = 5;
  int32 total_received_count = 6;
  AdvancedTask advanced_task = 7;
  Extra extra = 8;
}

message UnlightedItem {
  message AdvancedTask {
    int32 current_count = 1;
    int32 group_id = 2;
    bool is_finished = 3;
    int32 reward_star_count = 4;
    string task_id = 5;
    int32 threshold = 6;
  }

  message Extra {}

  Gift gift = 1;
  bool is_lighted = 2;
  int32 lighted_threshold = 3;
  int32 received_count = 4;
  int32 total_received_count = 5;
  AdvancedTask advanced_task = 6;
  Extra extra = 7;
}

message Data {
  message AbInfo {
    string exhibition_detail_new_page = 1;
    string exhibition_detail_opt = 2;
    string gift_exhibition_grade_user = 3;
  }

  message DefaultGift {
    Gift Gift = 1;
    bool IsLighted = 2;
    int32 LightedThreshold = 3;
    int32 ReceivedCount = 4;
  }

  Avatar anchor_avatar = 1;
  int64 anchor_id = 2;
  string anchor_name = 3;
  DefaultGift default_gift = 4;
  string exhibition_entrance_url = 5;
  bool is_all_collected = 6;
  repeated LightedItem lighted_items = 7;
  int32 personalise_switch_duration = 8;
  string sub_title = 9;
  string title = 10;
  repeated UnlightedItem unlighted_items = 11;
}

message Extra {
  int64 now = 1;
}

// webcast/gift/exhibition/home/<USER>
message GiftExHibitionHomeUrl {
  Data data = 1;
  Extra extra = 2;
  int32 status_code = 3;
}