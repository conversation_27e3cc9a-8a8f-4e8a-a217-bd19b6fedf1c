
[//]: # (protoc --js_out=./proto/out ./proto/payload_base.proto -I./proto/)

[//]: # (protoc --js_out=./proto/out ./proto/message.proto -I./proto/)

[//]: # (protoc --js_out=./proto/out ./proto/webcast/im.proto -I./proto/)

[//]: # (protoc --js_out=./proto/out ./proto/webcast/data.proto -I./proto/)

#### npm i grpc-tools
#### npm i google-protobuf

node node_modules/grpc-tools/bin/protoc.js --js_out=import_style=commonjs,binary:./proto/out --proto_path=./proto/ ./proto/payload_base.proto
node node_modules/grpc-tools/bin/protoc.js --js_out=import_style=commonjs,binary:./proto/out --proto_path=./proto/ ./proto/message.proto
node node_modules/grpc-tools/bin/protoc.js --js_out=import_style=commonjs,binary:./proto/out --proto_path=./proto/ ./proto/webcast/im.proto
node node_modules/grpc-tools/bin/protoc.js --js_out=import_style=commonjs,binary:./proto/out --proto_path=./proto/ ./proto/webcast/data.proto


