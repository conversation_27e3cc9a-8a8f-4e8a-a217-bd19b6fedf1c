const WebSocket = require('ws');

class LiveApiWebSocketClient {
    constructor(url = 'ws://localhost:8080') {
        this.url = url;
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        
        // 统计信息
        this.stats = {
            messagesReceived: 0,
            liveEventsReceived: 0,
            logsReceived: 0,
            errorsReceived: 0,
            connectionTime: null
        };
    }

    connect() {
        console.log(`尝试连接到 ${this.url}...`);
        
        this.ws = new WebSocket(this.url);
        
        this.ws.on('open', () => {
            console.log('✅ WebSocket连接已建立');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.stats.connectionTime = new Date();
            
            // 发送订阅消息
            this.subscribe(['live_events', 'log', 'stats']);
        });
        
        this.ws.on('message', (data) => {
            try {
                const message = JSON.parse(data);
                this.handleMessage(message);
            } catch (error) {
                console.error('❌ 解析消息失败:', error);
                this.stats.errorsReceived++;
            }
        });
        
        this.ws.on('close', (code, reason) => {
            console.log(`🔌 WebSocket连接已关闭 (代码: ${code}, 原因: ${reason})`);
            this.isConnected = false;
            
            // 尝试重连
            if (this.reconnectAttempts < this.maxReconnectAttempts) {
                this.reconnectAttempts++;
                console.log(`🔄 ${this.reconnectDelay/1000}秒后尝试重连 (第${this.reconnectAttempts}次)...`);
                setTimeout(() => {
                    this.connect();
                }, this.reconnectDelay);
            } else {
                console.log('❌ 达到最大重连次数，停止重连');
            }
        });
        
        this.ws.on('error', (error) => {
            console.error('❌ WebSocket错误:', error.message);
            this.stats.errorsReceived++;
        });
    }

    handleMessage(message) {
        this.stats.messagesReceived++;
        
        switch (message.type) {
            case 'welcome':
                console.log('🎉 收到欢迎消息:', message.message);
                break;
                
            case 'live_events':
                this.handleLiveEvents(message);
                break;
                
            case 'log':
                this.handleLog(message);
                break;
                
            case 'stats':
                this.handleStats(message);
                break;
                
            case 'custom':
                this.handleCustomMessage(message);
                break;
                
            case 'pong':
                console.log('🏓 收到Pong响应');
                break;
                
            case 'subscribed':
                console.log('✅ 订阅成功:', message.eventTypes);
                break;
                
            case 'error':
                console.error('❌ 服务器错误:', message.message);
                this.stats.errorsReceived++;
                break;
                
            case 'connect_live_response':
                if (message.success) {
                    console.log(`✅ 成功连接直播间: ${message.liveUrl} (ID: ${message.connectionId})`);
                } else {
                    console.error('❌ 连接直播间失败:', message.error);
                }
                break;

            case 'disconnect_live_response':
                if (message.success) {
                    if (message.connectionId) {
                        console.log(`✅ 成功断开连接: ${message.connectionId}`);
                    } else {
                        console.log(`✅ 成功断开 ${message.disconnectedCount} 个连接`);
                    }
                } else {
                    console.error('❌ 断开连接失败:', message.error);
                }
                break;

            case 'live_connection_added':
                console.log(`📡 新增连接: ${message.liveUrl} (ID: ${message.connectionId})`);
                break;

            case 'live_connection_removed':
                console.log(`📡 移除连接: ${message.liveUrl} (ID: ${message.connectionId})`);
                break;

            case 'all_connections_disconnected':
                console.log(`📡 所有连接已断开 (${message.disconnectedCount}个)`);
                break;

            case 'connections_list_response':
                console.log(`📋 活跃连接列表 (${message.totalCount}个):`);
                message.connections.forEach(conn => {
                    const uptime = Math.floor(conn.uptime / 1000);
                    const lastEvent = conn.lastEventTime ?
                        new Date(conn.lastEventTime).toLocaleTimeString() : '无';
                    console.log(`  • ${conn.id}: ${conn.liveUrl}`);
                    console.log(`    运行时间: ${uptime}s | 事件数: ${conn.eventCount || 0} | 最后事件: ${lastEvent} | 🪟 独立窗口`);
                });
                break;

            case 'windows_info_response':
                console.log(`🪟 窗口信息 (${message.totalWindows}个窗口):`);
                if (message.windowsInfo && message.windowsInfo.length > 0) {
                    message.windowsInfo.forEach(win => {
                        console.log(`  • ${win.connectionId}: ${win.title}`);
                        console.log(`    状态: ${win.isVisible ? '显示' : '隐藏'} | 最小化: ${win.isMinimized ? '是' : '否'}`);
                        console.log(`    位置: ${win.bounds.x},${win.bounds.y} | 大小: ${win.bounds.width}x${win.bounds.height}`);
                    });
                } else {
                    console.log('  暂无窗口');
                }
                break;

            case 'toggle_window_response':
                if (message.success) {
                    console.log(`🪟 窗口 ${message.connectionId} ${message.visible ? '显示' : '隐藏'}成功`);
                } else {
                    console.error('❌ 窗口操作失败:', message.error);
                }
                break;

            case 'status_response':
                console.log('📊 状态信息:');
                if (message.status.bridge) {
                    console.log(`   总连接数: ${message.status.bridge.totalConnections || 0}`);
                    console.log(`   活跃连接数: ${message.status.bridge.activeConnections || 0}`);
                    if (message.status.bridge.connections && message.status.bridge.connections.length > 0) {
                        console.log('   连接详情:');
                        message.status.bridge.connections.forEach(conn => {
                            const uptime = Math.floor(conn.uptime / 1000);
                            console.log(`     • ${conn.id}: ${conn.liveUrl} (运行${uptime}s, ${conn.stats.totalEvents || 0}个事件)`);
                        });
                    }
                }
                if (message.status.websocket) {
                    console.log(`   WebSocket状态: ${message.status.websocket.isRunning ? '运行中' : '已停止'}`);
                    console.log(`   客户端数量: ${message.status.websocket.clientCount}`);
                }
                if (message.status.globalStats) {
                    console.log(`   全局事件总数: ${message.status.globalStats.totalEvents || 0}`);
                    console.log(`   系统运行时间: ${Math.floor((message.status.globalStats.uptime || 0) / 1000)}秒`);
                }
                break;

            case 'shutdown':
                console.log('🛑 服务器正在关闭:', message.message);
                break;

            default:
                console.log('📨 未知消息类型:', message.type, message);
        }
    }

    handleLiveEvents(message) {
        this.stats.liveEventsReceived++;
        const eventsData = message.data;
        
        if (eventsData && eventsData.events_data) {
            console.log(`🎬 收到直播事件 (${eventsData.events_data.length}个事件):`);
            console.log(`   平台: ${eventsData.live_platform_type}`);
            console.log(`   上下文: ${eventsData.context}`);
            console.log(`   时间戳: ${new Date(eventsData.timestamp).toLocaleString()}`);
            
            // 显示每个事件的详细信息
            eventsData.events_data.forEach((event, index) => {
                console.log(`   事件${index + 1}: ${event.msg_type_str} (类型: ${event.msg_type})`);
                
                // 根据事件类型显示特定信息
                if (event.msg_type_str === 'comment_msg' && event.comment_msg) {
                    console.log(`     💬 评论: ${event.comment_msg.content} (用户: ${event.comment_msg.user.user_name})`);
                } else if (event.msg_type_str === 'gift_msg' && event.gift_msg) {
                    console.log(`     🎁 礼物: ${event.gift_msg.gift_name} x${event.gift_msg.gift_count} (用户: ${event.gift_msg.user_name})`);
                } else if (event.msg_type_str === 'member_msg' && event.member_msg) {
                    console.log(`     👋 进入: ${event.member_msg.user.user_name}`);
                }
                // else if (event.msg_type_str === 'follow_msg' && event.follow_msg) {
                //     console.log(`     ❤️ 关注: ${event.follow_msg.user_name}`);
                // }
                // else {
                //     console.log(`      未知消息： ${JSON.stringify(event)}`)
                // }
            });
        }
    }

    handleLog(message) {
        this.stats.logsReceived++;
        console.log(`📝 日志: ${message.message}`);
    }

    handleStats(message) {
        const stats = message.data;
        console.log('📊 统计信息:');
        console.log(`   总事件数: ${stats.totalEvents}`);
        console.log(`   运行时间: ${Math.floor(stats.uptime / 1000)}秒`);
        console.log(`   WebSocket客户端数: ${stats.wsServerStatus?.clientCount || 0}`);
        
        if (stats.eventsByType && Object.keys(stats.eventsByType).length > 0) {
            console.log('   事件类型统计:');
            Object.entries(stats.eventsByType).forEach(([type, count]) => {
                console.log(`     ${type}: ${count}`);
            });
        }
    }

    handleCustomMessage(message) {
        console.log('🔧 自定义消息:', message.data);
    }

    // 发送消息
    send(message) {
        if (this.isConnected && this.ws) {
            this.ws.send(JSON.stringify(message));
        } else {
            console.warn('⚠️ WebSocket未连接，无法发送消息');
        }
    }

    // 发送ping
    ping() {
        this.send({
            type: 'ping',
            timestamp: new Date().getTime()
        });
    }

    // 订阅事件类型
    subscribe(eventTypes) {
        this.send({
            type: 'subscribe',
            eventTypes: eventTypes
        });
    }

    // 取消订阅
    unsubscribe(eventTypes) {
        this.send({
            type: 'unsubscribe',
            eventTypes: eventTypes
        });
    }

    // 连接直播间
    connectLive(liveUrl) {
        this.send({
            type: 'connect_live',
            liveUrl: liveUrl
        });
    }

    // 断开直播间连接
    disconnectLive(connectionId = null) {
        const message = {
            type: 'disconnect_live'
        };
        if (connectionId) {
            message.connectionId = connectionId;
        }
        this.send(message);
    }

    // 获取连接列表
    listConnections() {
        this.send({
            type: 'list_connections'
        });
    }

    // 断开特定连接
    disconnectConnection(connectionId) {
        this.send({
            type: 'disconnect_connection',
            connectionId: connectionId
        });
    }

    // 获取状态
    getStatus() {
        this.send({
            type: 'get_status'
        });
    }

    // 获取窗口信息
    getWindowsInfo() {
        this.send({
            type: 'get_windows_info'
        });
    }

    // 切换窗口显示状态
    toggleWindow(connectionId, visible) {
        this.send({
            type: 'toggle_window',
            connectionId: connectionId,
            visible: visible
        });
    }

    // 断开连接
    disconnect() {
        if (this.ws) {
            this.ws.close();
        }
    }

    // 获取客户端统计信息
    getClientStats() {
        const uptime = this.stats.connectionTime ? 
            new Date().getTime() - this.stats.connectionTime.getTime() : 0;
            
        return {
            ...this.stats,
            uptime: uptime,
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts
        };
    }

    // 打印客户端统计信息
    printStats() {
        const stats = this.getClientStats();
        console.log('\n📈 客户端统计信息:');
        console.log(`   连接状态: ${stats.isConnected ? '已连接' : '未连接'}`);
        console.log(`   连接时间: ${Math.floor(stats.uptime / 1000)}秒`);
        console.log(`   收到消息总数: ${stats.messagesReceived}`);
        console.log(`   直播事件数: ${stats.liveEventsReceived}`);
        console.log(`   日志消息数: ${stats.logsReceived}`);
        console.log(`   错误消息数: ${stats.errorsReceived}`);
        console.log(`   重连次数: ${stats.reconnectAttempts}`);
    }
}

// 如果直接运行此文件，则启动客户端
if (require.main === module) {
    console.log('🚀 启动LiveApi WebSocket客户端...');
    console.log('');

    const client = new LiveApiWebSocketClient();
    client.connect();

    // 定期发送ping
    setInterval(() => {
        if (client.isConnected) {
            client.ping();
        }
    }, 30000);

    // 定期打印统计信息
    setInterval(() => {
        client.printStats();
    }, 60000);

    // 添加交互式命令
    const readline = require('readline');
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    console.log('💡 可用命令:');
    console.log('  connect <直播间链接> - 连接新的直播间（创建独立窗口）');
    console.log('  disconnect [连接ID] - 断开指定连接或所有连接');
    console.log('  list - 列出所有活跃连接');
    console.log('  status - 获取详细状态信息');
    console.log('  windows - 获取窗口信息');
    console.log('  show <连接ID> - 显示指定连接的窗口');
    console.log('  hide <连接ID> - 隐藏指定连接的窗口');
    console.log('  ping - 发送ping');
    console.log('  stats - 显示客户端统计');
    console.log('  help - 显示帮助');
    console.log('  quit - 退出程序');
    console.log('');

    rl.on('line', (input) => {
        const [command, ...args] = input.trim().split(' ');

        switch (command.toLowerCase()) {
            case 'connect':
                if (args.length === 0) {
                    console.log('❌ 请提供直播间链接');
                    console.log('示例: connect https://live.kuaishou.com/u/3xtzx7d2tpj4j2s');
                } else {
                    const liveUrl = args.join(' ');
                    console.log(`🔗 正在连接直播间: ${liveUrl}`);
                    client.connectLive(liveUrl);
                }
                break;

            case 'disconnect':
                if (args.length > 0) {
                    const connectionId = args[0];
                    console.log(`🔌 正在断开连接: ${connectionId}`);
                    client.disconnectConnection(connectionId);
                } else {
                    console.log('🔌 正在断开所有连接...');
                    client.disconnectLive();
                }
                break;

            case 'list':
                console.log('📋 获取连接列表...');
                client.listConnections();
                break;

            case 'windows':
                console.log('🪟 获取窗口信息...');
                client.getWindowsInfo();
                break;

            case 'show':
                if (args.length === 0) {
                    console.log('❌ 请提供连接ID');
                    console.log('示例: show abc123');
                } else {
                    const connectionId = args[0];
                    console.log(`🪟 显示窗口: ${connectionId}`);
                    client.toggleWindow(connectionId, true);
                }
                break;

            case 'hide':
                if (args.length === 0) {
                    console.log('❌ 请提供连接ID');
                    console.log('示例: hide abc123');
                } else {
                    const connectionId = args[0];
                    console.log(`🪟 隐藏窗口: ${connectionId}`);
                    client.toggleWindow(connectionId, false);
                }
                break;

            case 'status':
                console.log('📊 获取状态信息...');
                client.getStatus();
                break;

            case 'ping':
                console.log('🏓 发送ping...');
                client.ping();
                break;

            case 'stats':
                client.printStats();
                break;

            case 'help':
                console.log('💡 可用命令:');
                console.log('  connect <直播间链接> - 连接新的直播间（创建独立窗口）');
                console.log('  disconnect [连接ID] - 断开指定连接或所有连接');
                console.log('  list - 列出所有活跃连接');
                console.log('  status - 获取详细状态信息');
                console.log('  windows - 获取窗口信息');
                console.log('  show <连接ID> - 显示指定连接的窗口');
                console.log('  hide <连接ID> - 隐藏指定连接的窗口');
                console.log('  ping - 发送ping');
                console.log('  stats - 显示客户端统计');
                console.log('  help - 显示帮助');
                console.log('  quit - 退出程序');
                break;

            case 'quit':
            case 'exit':
                console.log('👋 正在关闭客户端...');
                client.disconnect();
                rl.close();
                process.exit(0);
                break;

            default:
                if (command) {
                    console.log(`❌ 未知命令: ${command}`);
                    console.log('输入 help 查看可用命令');
                }
        }
    });

    // 处理程序退出
    process.on('SIGINT', () => {
        console.log('\n👋 正在关闭客户端...');
        client.disconnect();
        rl.close();
        process.exit(0);
    });
}

module.exports = LiveApiWebSocketClient;
