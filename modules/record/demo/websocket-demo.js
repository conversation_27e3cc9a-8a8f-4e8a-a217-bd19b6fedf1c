const { app, BrowserWindow } = require('electron');
const LiveWebSocketBridge = require('../src/live-websocket-bridge');

let mainWindow;
let liveWebSocketBridge;

// 创建主窗口
async function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1920,
        height: 1080,
    });

    // 可选：打开开发者工具
    // mainWindow.webContents.openDevTools();

    console.log('Main window created');
}

// 启动LiveWebSocketBridge
function startLiveWebSocketBridge() {
    // 创建LiveWebSocketBridge实例
    // 参数：context, wsPort, test
    liveWebSocketBridge = new LiveWebSocketBridge('websocket-demo-context', 8080, false);

    // 启动服务器，不需要传入主窗口（每个直播间会创建独立窗口）
    liveWebSocketBridge.startServer();

    console.log('WebSocket server started at: ws://localhost:8080');
    console.log('Ready to create independent windows for each live room...');
    console.log('');
    console.log('You can now:');
    console.log('1. Connect a WebSocket client to ws://localhost:8080');
    console.log('2. Send a connect_live message to connect to a live room');
    console.log('3. Each live room will create an independent Electron window');
    console.log('4. Example message: {"type":"connect_live","liveUrl":"https://live.kuaishou.com/u/3xtzx7d2tpj4j2s"}');

    // 定期广播统计信息（每30秒）
    setInterval(() => {
        liveWebSocketBridge.broadcastStats();
    }, 30000);

    // 5秒后发送一个测试消息
    setTimeout(() => {
        liveWebSocketBridge.broadcast({
            message: 'LiveWebSocketBridge is ready to accept live room connections',
            architecture: 'Each live room will create an independent Electron window',
            instructions: {
                connect: 'Send {"type":"connect_live","liveUrl":"YOUR_LIVE_URL"} to connect',
                disconnect: 'Send {"type":"disconnect_live"} to disconnect',
                status: 'Send {"type":"get_status"} to get current status',
                windows: 'Send {"type":"get_windows_info"} to get window information',
                toggle: 'Send {"type":"toggle_window","connectionId":"ID","visible":true/false} to show/hide window'
            }
        });
    }, 5000);
}

// Electron应用事件处理
app.whenReady().then(async () => {
    await createWindow();
    
    // 等待窗口完全加载后启动LiveWebSocketBridge
    setTimeout(() => {
        startLiveWebSocketBridge();
    }, 2000);
});

app.on('window-all-closed', () => {
    // 清理资源
    if (liveWebSocketBridge) {
        liveWebSocketBridge.release();
    }
    
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', async () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        await createWindow();
    }
});

// 处理应用退出
app.on('before-quit', () => {
    console.log('Application is quitting, cleaning up...');
    if (liveWebSocketBridge) {
        liveWebSocketBridge.release();
    }
});

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    if (liveWebSocketBridge) {
        liveWebSocketBridge.release();
    }
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

console.log('WebSocket Demo Application Starting...');
console.log('This demo will:');
console.log('1. Create an Electron window');
console.log('2. Start a WebSocket server on port 8080');
console.log('3. Monitor a live room and forward events via WebSocket');
console.log('4. You can connect to ws://localhost:8080 to receive live events');
