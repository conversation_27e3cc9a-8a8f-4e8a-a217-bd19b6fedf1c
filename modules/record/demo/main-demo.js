
// Modules to control application life and create native browser window

const {app, BrowserWindow, session} = require('electron');
const {LiveApi} = require("../dist/liveApi");
const fs = require("fs");
const {eventCallback} = require('./demo_common')

var mainWindow;

// tiktok 需要添加代理
//app.commandLine.appendSwitch('proxy-server', 'http://127.0.0.1:1080')

// 必须修改 ua，不然登录二维码加载不出来
// app.userAgentFallback = 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36';
app.userAgentFallback = app.userAgentFallback.replace(/Electron\/[0-9.]+\s/g, '');

// 手机 ua
// Mozilla/5.0 (Linux; Android 13; Pixel 7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36

var liveApi = new LiveApi('_context_123456');
async function createWindow() {
    mainWindow = new BrowserWindow({
        width: 1920,
        height: 1080,
    })

    // await mainWindow.webContents.session.clearCache();
    // console.log('Cache cleared');
    // mainWindow.openDevTools();
    // mainWindow.loadURL('https://live.kuaishou.com/u/3x4i7dudbfsnxmi');
    // 3xphut7zrw7zcbq
    // Mubai0806
    // 3xkedwqpa4qp5cy
    // 3xg732f6wrbxnsc https://live.kuaishou.com/u/3xjrfwinr23gycg
    // https://live.kuaishou.com/u/3xdxpgw6f9ckwru
    // https://live.kuaishou.com/u/3xz6iukffuuq3fu
    // https://v.kuaishou.com/2zJXSwD
    // https://live.kuaishou.com/u/3xtzx7d2tpj4j2s
    // mainWindow.openDevTools();
    liveApi.startMonitorRoom(mainWindow, 'https://live.kuaishou.com/u/3xtzx7d2tpj4j2s', eventCallback, logCallback);// x150
    // 推出监控的时候需要释放
    // liveApi.release();
}

function logCallback(info) {
    console.log(info)
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
    createWindow()

    app.on('activate', function () {
        // On macOS it's common to re-create a window in the app when the
        // dock icon is clicked and there are no other windows open.
        if (BrowserWindow.getAllWindows().length === 0) createWindow()
    })
})


// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', function () {
    // liveApi.release();
    if (process.platform !== 'darwin') app.quit()
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
