<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiveApi WebSocket Client</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .messages {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            font-family: monospace;
            font-size: 12px;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .message.live_events {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
        }
        .message.log {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        .message.stats {
            background-color: #d1ecf1;
            border-left: 4px solid #17a2b8;
        }
        .message.error {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .stat-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .stat-value {
            font-size: 18px;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LiveApi WebSocket Client</h1>
        
        <div id="status" class="status disconnected">
            未连接
        </div>
        
        <div class="controls">
            <button id="connectBtn" class="btn-primary">连接WebSocket</button>
            <button id="disconnectBtn" class="btn-secondary" disabled>断开WebSocket</button>
            <button id="pingBtn" class="btn-success" disabled>发送Ping</button>
            <button id="subscribeBtn" class="btn-success" disabled>订阅事件</button>
            <button id="clearBtn" class="btn-danger">清空消息</button>
        </div>

        <div class="controls" style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 15px;">
            <h4>多直播间管理</h4>
            <div style="margin: 10px 0;">
                <input type="text" id="liveUrlInput" placeholder="输入直播间链接" style="width: 300px; padding: 8px; margin-right: 10px;">
                <button id="connectLiveBtn" class="btn-primary" disabled>添加直播间</button>
                <button id="disconnectAllBtn" class="btn-secondary" disabled>断开所有</button>
                <button id="listConnectionsBtn" class="btn-success" disabled>刷新列表</button>
                <button id="getStatusBtn" class="btn-success" disabled>获取状态</button>
                <button id="getWindowsBtn" class="btn-success" disabled>窗口信息</button>
            </div>
            <div style="font-size: 12px; color: #666;">
                支持的直播间链接示例（每个直播间将创建独立窗口）：<br>
                • 快手: https://live.kuaishou.com/u/3xtzx7d2tpj4j2s<br>
                • 抖音: https://live.douyin.com/xxxxx<br>
                • TikTok: https://www.tiktok.com/@username/live<br>
                • 微信视频号: weixin
            </div>
        </div>

        <div style="border-top: 1px solid #ddd; padding-top: 15px; margin-top: 15px;">
            <h4>活跃连接列表</h4>
            <div id="connectionsList" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background-color: #f8f9fa;">
                <div style="color: #666; text-align: center;">暂无连接</div>
            </div>
        </div>
        
        <div class="stats-panel" id="statsPanel" style="display: none;">
            <div class="stat-card">
                <div class="stat-title">总事件数</div>
                <div class="stat-value" id="totalEvents">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">连接客户端数</div>
                <div class="stat-value" id="clientCount">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">运行时间</div>
                <div class="stat-value" id="uptime">0s</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">最后事件时间</div>
                <div class="stat-value" id="lastEventTime">-</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">活跃连接数</div>
                <div class="stat-value" id="activeConnections">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">总连接数</div>
                <div class="stat-value" id="totalConnections">0</div>
            </div>
        </div>
        
        <h3>消息日志</h3>
        <div id="messages" class="messages"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        
        const statusEl = document.getElementById('status');
        const messagesEl = document.getElementById('messages');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const pingBtn = document.getElementById('pingBtn');
        const subscribeBtn = document.getElementById('subscribeBtn');
        const clearBtn = document.getElementById('clearBtn');
        const statsPanel = document.getElementById('statsPanel');

        // 直播间控制元素
        const liveUrlInput = document.getElementById('liveUrlInput');
        const connectLiveBtn = document.getElementById('connectLiveBtn');
        const disconnectAllBtn = document.getElementById('disconnectAllBtn');
        const listConnectionsBtn = document.getElementById('listConnectionsBtn');
        const getStatusBtn = document.getElementById('getStatusBtn');
        const getWindowsBtn = document.getElementById('getWindowsBtn');
        const connectionsList = document.getElementById('connectionsList');

        // 存储连接信息
        let connections = new Map();

        function updateStatus(connected) {
            isConnected = connected;
            if (connected) {
                statusEl.textContent = '已连接到 ws://localhost:8080';
                statusEl.className = 'status connected';
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                pingBtn.disabled = false;
                subscribeBtn.disabled = false;
                connectLiveBtn.disabled = false;
                disconnectAllBtn.disabled = false;
                listConnectionsBtn.disabled = false;
                getStatusBtn.disabled = false;
                getWindowsBtn.disabled = false;
                statsPanel.style.display = 'grid';
            } else {
                statusEl.textContent = '未连接';
                statusEl.className = 'status disconnected';
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                pingBtn.disabled = true;
                subscribeBtn.disabled = true;
                connectLiveBtn.disabled = true;
                disconnectAllBtn.disabled = true;
                listConnectionsBtn.disabled = true;
                getStatusBtn.disabled = true;
                getWindowsBtn.disabled = true;
                statsPanel.style.display = 'none';
            }
        }

        function addMessage(type, data) {
            const messageEl = document.createElement('div');
            messageEl.className = `message ${type}`;
            
            const timestamp = new Date().toLocaleTimeString();
            let content = '';
            
            switch (type) {
                case 'live_events':
                    const eventsCount = data.data?.events_data?.length || 0;
                    content = `[${timestamp}] 直播事件 (${eventsCount}个事件): ${JSON.stringify(data, null, 2)}`;
                    break;
                case 'log':
                    content = `[${timestamp}] 日志: ${data.message}`;
                    break;
                case 'stats':
                    content = `[${timestamp}] 统计信息: ${JSON.stringify(data.data, null, 2)}`;
                    updateStatsDisplay(data.data);
                    break;
                case 'connect_live_response':
                    if (data.success) {
                        content = `[${timestamp}] ✅ 成功连接直播间: ${data.liveUrl} (ID: ${data.connectionId})`;
                        // 刷新连接列表
                        listConnections();
                    } else {
                        content = `[${timestamp}] ❌ 连接直播间失败: ${data.error}`;
                    }
                    break;
                case 'disconnect_live_response':
                    if (data.success) {
                        if (data.connectionId) {
                            content = `[${timestamp}] ✅ 成功断开连接: ${data.connectionId}`;
                        } else {
                            content = `[${timestamp}] ✅ 成功断开 ${data.disconnectedCount} 个连接`;
                        }
                        // 刷新连接列表
                        listConnections();
                    } else {
                        content = `[${timestamp}] ❌ 断开连接失败: ${data.error}`;
                    }
                    break;
                case 'live_connection_added':
                    content = `[${timestamp}] 📡 新增连接: ${data.liveUrl} (ID: ${data.connectionId})`;
                    // 刷新连接列表
                    listConnections();
                    break;
                case 'live_connection_removed':
                    content = `[${timestamp}] 📡 移除连接: ${data.liveUrl} (ID: ${data.connectionId})`;
                    // 刷新连接列表
                    listConnections();
                    break;
                case 'all_connections_disconnected':
                    content = `[${timestamp}] 📡 所有连接已断开 (${data.disconnectedCount}个)`;
                    // 刷新连接列表
                    listConnections();
                    break;
                case 'connections_list_response':
                    content = `[${timestamp}] 📋 连接列表更新 (${data.totalCount}个连接)`;
                    updateConnectionsList(data.connections);
                    break;
                case 'windows_info_response':
                    content = `[${timestamp}] 🪟 窗口信息 (${data.totalWindows}个窗口)`;
                    if (data.windowsInfo) {
                        data.windowsInfo.forEach(win => {
                            content += `\n  • ${win.connectionId}: ${win.title} (${win.isVisible ? '显示' : '隐藏'})`;
                        });
                    }
                    break;
                case 'toggle_window_response':
                    if (data.success) {
                        content = `[${timestamp}] 🪟 窗口 ${data.connectionId} ${data.visible ? '显示' : '隐藏'}成功`;
                    } else {
                        content = `[${timestamp}] ❌ 窗口操作失败: ${data.error}`;
                    }
                    break;
                case 'status_response':
                    content = `[${timestamp}] 📊 状态信息: 活跃连接 ${data.status.bridge?.activeConnections || 0}个`;
                    if (data.status.bridge && data.status.bridge.connections) {
                        updateConnectionsList(data.status.bridge.connections);
                    }
                    break;
                case 'error':
                    content = `[${timestamp}] 错误: ${data.message || JSON.stringify(data)}`;
                    break;
                default:
                    content = `[${timestamp}] ${type}: ${JSON.stringify(data, null, 2)}`;
            }
            
            messageEl.textContent = content;
            messagesEl.appendChild(messageEl);
            messagesEl.scrollTop = messagesEl.scrollHeight;
        }

        function updateStatsDisplay(stats) {
            // 处理新的多连接统计格式
            if (stats.global) {
                document.getElementById('totalEvents').textContent = stats.global.totalEvents || 0;
                document.getElementById('clientCount').textContent = stats.global.wsServerStatus?.clientCount || 0;
                document.getElementById('activeConnections').textContent = stats.global.activeConnections || 0;
                document.getElementById('totalConnections').textContent = stats.global.totalConnections || 0;

                if (stats.global.uptime) {
                    const uptimeSeconds = Math.floor(stats.global.uptime / 1000);
                    document.getElementById('uptime').textContent = `${uptimeSeconds}s`;
                }

                if (stats.global.lastEventTime) {
                    const lastTime = new Date(stats.global.lastEventTime).toLocaleTimeString();
                    document.getElementById('lastEventTime').textContent = lastTime;
                }
            } else {
                // 兼容旧格式
                document.getElementById('totalEvents').textContent = stats.totalEvents || 0;
                document.getElementById('clientCount').textContent = stats.wsServerStatus?.clientCount || 0;

                if (stats.uptime) {
                    const uptimeSeconds = Math.floor(stats.uptime / 1000);
                    document.getElementById('uptime').textContent = `${uptimeSeconds}s`;
                }

                if (stats.lastEventTime) {
                    const lastTime = new Date(stats.lastEventTime).toLocaleTimeString();
                    document.getElementById('lastEventTime').textContent = lastTime;
                }
            }
        }

        function connect() {
            if (ws) {
                ws.close();
            }
            
            ws = new WebSocket('ws://localhost:8080');
            
            ws.onopen = function(event) {
                console.log('WebSocket连接已建立');
                updateStatus(true);
                addMessage('info', { message: 'WebSocket连接已建立' });
            };
            
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    console.log('收到消息:', data);
                    addMessage(data.type, data);
                } catch (error) {
                    console.error('解析消息失败:', error);
                    addMessage('error', { message: '解析消息失败: ' + error.message });
                }
            };
            
            ws.onclose = function(event) {
                console.log('WebSocket连接已关闭');
                updateStatus(false);
                addMessage('info', { message: 'WebSocket连接已关闭' });
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket错误:', error);
                addMessage('error', { message: 'WebSocket连接错误' });
            };
        }

        function disconnect() {
            if (ws) {
                ws.close();
            }
        }

        function sendPing() {
            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: 'ping',
                    timestamp: new Date().getTime()
                }));
            }
        }

        function subscribe() {
            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: 'subscribe',
                    eventTypes: ['live_events', 'log', 'stats']
                }));
            }
        }

        function clearMessages() {
            messagesEl.innerHTML = '';
        }

        function connectLive() {
            const liveUrl = liveUrlInput.value.trim();
            if (!liveUrl) {
                alert('请输入直播间链接');
                return;
            }

            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: 'connect_live',
                    liveUrl: liveUrl
                }));
                // 清空输入框
                liveUrlInput.value = '';
            }
        }

        function disconnectAll() {
            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: 'disconnect_live'
                }));
            }
        }

        function listConnections() {
            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: 'list_connections'
                }));
            }
        }

        function disconnectConnection(connectionId) {
            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: 'disconnect_connection',
                    connectionId: connectionId
                }));
            }
        }

        function updateConnectionsList(connections) {
            if (!connections || connections.length === 0) {
                connectionsList.innerHTML = '<div style="color: #666; text-align: center;">暂无连接</div>';
                return;
            }

            let html = '';
            connections.forEach(conn => {
                const uptime = Math.floor(conn.uptime / 1000);
                const lastEvent = conn.lastEventTime ?
                    new Date(conn.lastEventTime).toLocaleTimeString() : '无';

                html += `
                    <div style="border: 1px solid #ddd; margin: 5px 0; padding: 10px; border-radius: 4px; background: white;">
                        <div style="display: flex; justify-content: between; align-items: center;">
                            <div style="flex: 1;">
                                <strong>ID:</strong> ${conn.id}<br>
                                <strong>URL:</strong> <span style="font-size: 12px; word-break: break-all;">${conn.liveUrl}</span><br>
                                <strong>运行时间:</strong> ${uptime}s | <strong>事件数:</strong> ${conn.eventCount || 0} | <strong>最后事件:</strong> ${lastEvent}<br>
                                <small style="color: #666;">🪟 独立窗口已创建</small>
                            </div>
                            <div style="margin-left: 10px;">
                                <button onclick="toggleWindow('${conn.id}', true)" class="btn-success" style="margin: 2px; font-size: 12px;">显示窗口</button>
                                <button onclick="toggleWindow('${conn.id}', false)" class="btn-secondary" style="margin: 2px; font-size: 12px;">隐藏窗口</button>
                                <button onclick="disconnectConnection('${conn.id}')" class="btn-danger" style="margin: 2px;">断开</button>
                            </div>
                        </div>
                    </div>
                `;
            });

            connectionsList.innerHTML = html;

            // 更新统计显示
            document.getElementById('activeConnections').textContent = connections.length;
        }

        function getStatus() {
            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: 'get_status'
                }));
            }
        }

        function getWindowsInfo() {
            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: 'get_windows_info'
                }));
            }
        }

        function toggleWindow(connectionId, visible) {
            if (ws && isConnected) {
                ws.send(JSON.stringify({
                    type: 'toggle_window',
                    connectionId: connectionId,
                    visible: visible
                }));
            }
        }

        // 事件监听器
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        pingBtn.addEventListener('click', sendPing);
        subscribeBtn.addEventListener('click', subscribe);
        clearBtn.addEventListener('click', clearMessages);

        // 直播间控制事件监听器
        connectLiveBtn.addEventListener('click', connectLive);
        disconnectAllBtn.addEventListener('click', disconnectAll);
        listConnectionsBtn.addEventListener('click', listConnections);
        getStatusBtn.addEventListener('click', getStatus);
        getWindowsBtn.addEventListener('click', getWindowsInfo);

        // 回车键连接直播间
        liveUrlInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                connectLive();
            }
        });

        // 页面加载完成后自动连接
        window.addEventListener('load', function() {
            setTimeout(() => {
                connect();
                // 连接成功后自动获取连接列表
                setTimeout(listConnections, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
