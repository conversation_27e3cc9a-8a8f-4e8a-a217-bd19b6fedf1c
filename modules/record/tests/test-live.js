
const os = require("os");
const fs = require('fs');
const {LiveApi, createLiveObject} = require("../src/liveApi");

const {eventCallback} = require('../demo/demo_common')
function processFile(liveObj, filePath) {
    var data = fs.readFileSync(filePath, 'utf8');

    data.split('\n').forEach(line => {
        try {
            liveObj.testParseRaw(line, 'test', eventCallback, logcallback);
        } catch (error) {
            console.error(`Error -`, error);
        }
    });
}
function processWeixinFile(liveObj, filePath) {
    var data = fs.readFileSync(filePath, 'utf8');

    data.split('\n').forEach(line => {
        try {
            const jsonMatch = line.match(/\[.*?\]WeixinJsonStr: (.*)/);
            if (!jsonMatch || !jsonMatch[1]) {
                console.error(`Line Invalid format, no JSON string found`);
                return;
            }

            // Parse the JSON string
            const jsonStr = jsonMatch[1];
            const jsonObj = JSON.parse(jsonStr);
            liveObj.testParseRaw(jsonObj, 'test', eventCallback, logcallback);
        } catch (error) {
            console.error(`Error -`, error);
        }
    });
}

function processRecallFile(fileName) {
    var fileData = fs.readFileSync(fileName, 'utf8');
    testRecall(fileData, "test", eventCallback, logcallback);
}

function logcallback(info) {
    console.log(info);
}

// test 1
function testDouyin(raw_name) {
    var liveDy = new LiveDouyin(true);
    processFile(liveDy, raw_name);
    liveDy.release();
    liveDy = null;
}

function testLiveProcessFile(live_name,raw_name) {
    var liveDy = createLiveObject(live_name, '_test_live', true);

    if (live_name === 'weixin') {
        processWeixinFile(liveDy, raw_name);
    } else {
        processFile(liveDy, raw_name);
    }
    liveDy.release();
    liveDy = null;
}

// testLiveProcessFile('douyin',"raw/douyin/debug_dyEvents-aHR0cHM6Ly9saXZlLmRvdXlpbi5jb20vNTk4OTU1NTgyNjg5Lw==.txt");
// testDouyin("raw/douyin/debug_dyEvents-aHR0cHM6Ly9saXZlLmRvdXlpbi5jb20vNTMyMDA2Njk1MzY1Lw==.txt");
// testLiveProcessFile('kuaishou',"raw/kuaishou/debug_ksEvents-aHR0cHM6Ly9saXZlLmt1YWlzaG91LmNvbS91LzN4dHp4N2QydHBqNGoycw==.txt");

// testLiveProcessFile('tiktok',"raw/tiktok/debug_tkEvents-296088.txt");


// testLiveProcessFile('weixin',"raw/weixin/3824-_params_jsapi_resp.txt");

// 1. 抖音礼物组数是用来和连击数相乘的。
// 2025/6/24 11:20:02.314 [杜杜嘟 Club.12]Lv.27[有娃爱狗男] 送出:星光闪耀x2,总共:7*4=28	1750733127_26591770381 ,9
// 2025/6/24 11:20:02.314 [杜杜嘟 Club.12]Lv.27[有娃爱狗男] 送出:星光闪耀x4,总共:16*2=32	1750733127_26591770381 ,9
// testLiveProcessFile('douyin', 'raw/douyin/debug_douyinEvents-121931.txt')
// 2. 团播，给指定用户送礼物
// testLiveProcessFile('douyin', 'raw/douyin/debug_douyinEvents-151900.txt')

// testLiveProcessFile('kuaishou',"raw/kuaishou/debug_kuaishouEvents-214625-test.txt");

// debug_kuaishouEvents-429072.txt
testLiveProcessFile('kuaishou',"raw/kuaishou/debug_kuaishouEvents-429072.txt");