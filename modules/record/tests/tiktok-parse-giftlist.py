import json

def parse_and_write_tiktok_gifts(input_json_file_path, output_json_file_path='tiktok_gift_data.json'):
    """
    解析TikTok礼物列表的JSON文件，提取礼物ID、名称和图标URL，
    然后将提取到的数据写入一个新的JSON文件。

    Args:
        input_json_file_path (str): TikTok礼物列表JSON文件的路径。
        output_json_file_path (str): 输出JSON文件的路径。
    """
    gift_data = []
    try:
        with open(input_json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        gifts = data.get('data', {}).get('gifts', [])

        for gift in gifts:
            gift_id = gift.get('id')
            gift_name = gift.get('name')
            diamond_count = gift.get('diamond_count')

            icon_url = None
            icon_info = gift.get('icon', {})
            if icon_info and isinstance(icon_info, dict):
                url_list = icon_info.get('url_list')
                if url_list and isinstance(url_list, list) and len(url_list) > 0:
                    icon_url = url_list[0]

            if gift_id is not None and gift_name is not None and icon_url is not None and diamond_count is not None:
                gift_data.append({
                    'id': gift_id,
                    'name': gift_name,
                    'icon': icon_url,
                    'diamond_count': diamond_count
                })

        # 将提取到的数据写入新的JSON文件
        with open(output_json_file_path, 'w', encoding='utf-8') as outfile:
            json.dump(gift_data, outfile, ensure_ascii=False, indent=4)

        print(f"成功从 '{input_json_file_path}' 中提取了 {len(gift_data)} 个礼物信息，并已写入到 '{output_json_file_path}'。")

    except FileNotFoundError:
        print(f"错误: 输入文件 '{input_json_file_path}' 未找到。")
    except json.JSONDecodeError:
        print(f"错误: 无法解析输入文件 '{input_json_file_path}'。请确保它是有效的JSON格式。")
    except Exception as e:
        print(f"发生未知错误: {e}")

if __name__ == "__main__":
    input_file = 'raw/tiktok/info/tiktok-list-gift.json'
    output_file = 'raw/tiktok/info/tiktok_gift_data2.json' # 默认输出文件名
    parse_and_write_tiktok_gifts(input_file, output_file)
