{"name": "live-plugin", "version": "2.0.0", "scripts": {"hunxiao-proto": "rollup -c rollup-hunxiao-proto.config.mjs"}, "dependencies": {"@rollup/plugin-commonjs": "^28.0.2", "@rollup/plugin-json": "^6.1.0", "@rollup/plugin-terser": "^0.4.4", "protobufjs": "^7.5.3", "protobufjs-cli": "^1.2.0", "jsdom": "^26.0.0", "rollup": "^4.40.2"}, "devDependencies": {"long": "^5.2.4", "protobufjs": "^7.5.1", "ts-loader": "^9.5.2", "typescript": "^5.7.3", "webpack": "^5.97.1", "webpack-cli": "^6.0.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "*"}}