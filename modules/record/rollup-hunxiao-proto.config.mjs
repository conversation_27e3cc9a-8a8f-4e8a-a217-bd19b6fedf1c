import json from '@rollup/plugin-json';
import terser from '@rollup/plugin-terser';

export default {
    input: 'auto_gen_webcast.js',
    output: [
        {
            file: 'src/release_webcast.js',
            format: 'cjs'
        },
        {
            file: 'src/release_webcast.min.js',
            format: 'iife',
            name: 'version',
            plugins: [terser()]
        }
    ],
    plugins: [json()]
};
